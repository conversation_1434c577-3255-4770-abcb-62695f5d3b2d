package summary_operation_helper

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils"
	"iaa_data/utils/constant"
	"iaa_data/utils/country_helper"
	"iaa_data/utils/game_helper"
	"iaa_data/utils/sql_helper"
	"log"
	"strings"
	"time"
)

var keyValMap map[string]string
var addList []string

func SummaryReport(startTime, endTime, timeZoneStr string) {
	if !utils.IsTimeZone(timeZoneStr) {
		return
	}
	startDate, err1 := utils.ParseBjTime(time.DateOnly, startTime)
	if err1 != nil {
		log.Println("时间格式有误startTime")
		return
	}
	endDate, err2 := utils.ParseBjTime(time.DateOnly, endTime)
	if err2 != nil {
		log.Println("时间格式有误endTime")
		return
	}

	for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {

		timeVal := currentDate.Format("2006-01-02")
		sTime := timeVal + " 00:00:00"
		eTime := timeVal + " 23:59:59"
		if timeZoneStr == constant.TIME_ERU {
			sTime = utils.ERUtoBjTime(sTime)
			eTime = utils.ERUtoBjTime(eTime)
		} else if timeZoneStr == constant.TIME_US {
			sTime = utils.UStoBjTime(sTime)
			eTime = utils.UStoBjTime(eTime)
		}

		tday := currentDate.Format("20060102")

		timeZone := utils.GeTimeZoneTag(timeZoneStr)

		addList = nil
		keyValMap = nil
		keyValMap = sql_helper.GetUniqueLogs(tday, fmt.Sprint(timeZone), "iaa_report_operation")

		delSql := "delete FROM iaa_report_operation where tday=" + tday + " and time_zone = " + fmt.Sprint(timeZone)
		_, errDel := plusQ.Db().Delete(delSql)
		if errDel != nil {
			log.Println("删除失败", errDel)
		}

		searchCommon(sTime, eTime, tday, timeZoneStr, "hw_sdk_user_login_package", "new_user_num")
		searchActiveUserNum(sTime, eTime, tday, timeZoneStr, "active_num")
		searchCommonAdRe(sTime, eTime, tday, timeZoneStr, "revenue")
		searchIapPay(sTime, eTime, tday, timeZoneStr, "iap_pay_money")
		searchIapPayUserUser(sTime, eTime, tday, timeZoneStr, "new_iap_pay_money")
		searchNewAdRe(sTime, eTime, tday, timeZoneStr, "new_user_revenue")
		searchNewAdRe(sTime, eTime, tday, timeZoneStr, "old_user_revenue")
		searchCommonAd(sTime, eTime, tday, timeZoneStr, "hw_sdk_ad_revenue_log", "ad_show_num")
		searchCommonAd(sTime, eTime, tday, timeZoneStr, "hw_sdk_ad_request_log", "ad_request_num")
		searchCommonAd(sTime, eTime, tday, timeZoneStr, "hw_sdk_ad_loaded_log", "ad_fill_num")
		searchCommonWatch(sTime, eTime, tday, timeZoneStr, "hw_sdk_ad_revenue_log", "ad_user")
		searchRemain(sTime, eTime, tday, timeZoneStr, "newlogin_user_yesterday_remain")

		if len(addList) > 0 {
			for _, keyVal := range addList {
				delete(keyValMap, keyVal)
				info := make(map[string]interface{})
				info["key_val"] = keyVal
				info["table_name"] = "iaa_report_operation"
				info["tday"] = tday
				info["time_zone"] = timeZone
				info["update_time"] = time.Now().Format("2006-01-02 15:04:05")
				sql_helper.LogHelperInstance().HandleDataMapInsert("data", "iaa_report_unique_log", "iaa_report_unique_log", info, 30, 2)
			}
			sql_helper.LogHelperInstance().HandleDataMapEnd("data", "iaa_report_unique_log", "iaa_report_unique_log")
		}
		if len(keyValMap) > 0 {
			for _, keyVal := range keyValMap {
				keyVals := strings.Split(keyVal, "||")
				if len(keyVals) == 5 {
					//`tday`,`time_zone`,`package_id`,`country_id`,`source_id`
					info := make(map[string]interface{})

					info["tday"] = keyVals[0]
					info["time_zone"] = keyVals[1]
					info["package_id"] = keyVals[2]
					info["country_id"] = keyVals[3]
					info["source_id"] = keyVals[4]

					channelId := game_helper.GameHelInstant().GetChannelId(utils.AnyToInt(keyVals[2]))
					info["channel_id"] = channelId
					info["new_user_num"] = 0
					info["active_num"] = 0
					info["newlogin_user_yesterday_remain"] = 0
					info["ad_user"] = 0
					info["ad_show_num"] = 0
					info["ad_request_num"] = 0
					info["ad_fill_num"] = 0
					info["revenue"] = 0
					info["new_user_revenue"] = 0
					info["old_user_revenue"] = 0
					info["iap_pay_money"] = 0
					info["new_iap_pay_money"] = 0
					info["utime"] = time.Now().Format("2006-01-02 15:04:05")
					err := sql_helper.InsertOrUpdateAllFiledSummary("", "iaa_report_operation", "tday|time_zone|country_id|package_id|source_id", info)
					if err != nil {
						log.Println("更新置0失败 iaa_report_operation:", err)
					}
				}
			}
		}
	}
}

func searchCommon(sTime, eTime, tday, timeZoneStr, tableName, numTag string) {
	list, err := getNumList(sTime, eTime, tableName)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func searchActiveUserNum(sTime, eTime, tday, timeZoneStr, numTag string) {
	list, err := getActiveNumList(sTime, eTime)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func searchCommonAdRe(sTime, eTime, tday, timeZoneStr, numTag string) {
	list, err := getAdRevenueList(sTime, eTime)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func searchIapPay(sTime, eTime, tday, timeZoneStr, numTag string) {
	list, err := getIapPayList(sTime, eTime)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}
func searchIapPayUserUser(sTime, eTime, tday, timeZoneStr, numTag string) {
	list, err := getIapPayNewUserList(sTime, eTime)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func searchCommonAd(sTime, eTime, tday, timeZoneStr, tableName, numTag string) {
	list, err := getAdList(sTime, eTime, tableName)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func searchCommonWatch(sTime, eTime, tday, timeZoneStr, tableName, numTag string) {
	list, err := getAdWatchList(sTime, eTime, tableName)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func searchRemain(sTime, eTime, tday, timeZoneStr, numTag string) {
	list, err := getRemainList(sTime, eTime)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func searchNewAdRe(sTime, eTime, tday, timeZoneStr, numTag string) {
	list, err := getNewAdRevenueList(sTime, eTime, numTag)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, list, numTag)
	}
}

func summaryCommonNum(timeZoneStr, tday string, list []map[string]interface{}, numTag string) {

	for _, v := range list {
		timeZone := utils.GeTimeZoneTag(timeZoneStr)
		gameId := fmt.Sprint(v["game_id"])
		cpGameId := fmt.Sprint(v["cp_game_id"])
		country := fmt.Sprint(v["country"])
		package_id := fmt.Sprint(v["package_id"])
		source_id := fmt.Sprint(v["source_id"])
		country_id := country_helper.CountryInstant().GetCountryId(country)
		num := fmt.Sprint(v["num"])

		//if country_id == 0 {
		//	continue
		//}

		info := make(map[string]interface{})
		info["time_zone"] = timeZone
		info["tday"] = tday
		info["game_id"] = gameId
		info["cp_game_id"] = cpGameId
		info["country_id"] = country_id
		info["package_id"] = package_id
		info["source_id"] = source_id
		info["platform"] = v["os"]
		info["utime"] = time.Now().Format(time.DateTime)
		channelId := game_helper.GameHelInstant().GetChannelId(utils.AnyToInt(package_id))
		info["channel_id"] = channelId
		info[numTag] = num

		//`tday`,`time_zone`,`package_id`,`country_id`,`source_id`
		keyVal := tday + "||" + fmt.Sprint(timeZone) + "||" + package_id + "||" + fmt.Sprint(country_id) + "||" + source_id
		if !sql_helper.IsHaveKey(addList, keyVal) {
			addList = append(addList, keyVal)
		}

		err := sql_helper.InsertOrUpdateAllFiledSummary("", "iaa_report_operation", "tday|time_zone|country_id|package_id|source_id", info)
		if err != nil {
			log.Println("insert err:", err)
		}
	}
}

func getNumList(start, end, tableName string) ([]map[string]interface{}, error) {
	selectSql := "select game_id,cp_game_id, country, package_id,source_id,os," +
		" count(*) AS num " +
		" FROM " + tableName +
		" where time_bj between '" + start + "' and '" + end + "'"
	//`tday`,`time_zone`,`package_id`,`country_id`,`source_id`
	selectSql = selectSql + " GROUP BY package_id,country, source_id "

	list, err := plusQ.Db("data").List(selectSql)
	return list, err
}

func getActiveNumList(start, end string) ([]map[string]interface{}, error) {
	selectSql := "select game_id,cp_game_id, country, package_id,source_id,os," +
		" COUNT(DISTINCT core_account) AS num " +
		" FROM " + "hw_sdk_user_login" +
		" where time_bj between '" + start + "' and '" + end + "'"
	//`tday`,`time_zone`,`package_id`,`country_id`,`source_id`
	selectSql = selectSql + " GROUP BY package_id,country, source_id "

	list, err := plusQ.Db("data").List(selectSql)
	return list, err
}

func getAdRevenueList(start, end string) ([]map[string]interface{}, error) {
	selectSql := "SELECT cp_game_id,game_id,country,package_id,source_id,os," +
		" sum(revenue) as num" +
		" FROM hw_sdk_ad_revenue_log " +
		" where time_bj between '" + start + "' and '" + end + "'"

	//`tday`,`time_zone`,`package_id`,`country_id`,`source_id`
	selectSql = selectSql + " group by package_id,country,source_id"
	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("getAdRevenueList=3", selectSql)
	return list, err
}

func getIapPayList(start, end string) ([]map[string]interface{}, error) {
	selectSql := "SELECT cp_game_id,game_id,country,package_id,source_id,os," +
		" sum(money) as num" +
		" FROM hw_sdk_user_payment " +
		" where pay_time between '" + start + "' and '" + end + "'" +
		" and pay_result = 1"

	//`tday`,`time_zone`,`package_id`,`country_id`,`source_id`
	selectSql = selectSql + " group by package_id,country,source_id"
	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("getAdRevenueList=3", selectSql)
	return list, err
}

func getIapPayNewUserList(start, end string) ([]map[string]interface{}, error) {
	selectSql := "select a.cp_game_id, a.game_id, a.country, a.package_id, a.source_id, a.os, sum(a.money) as num from hw_sdk_user_payment  a " +
		" left join hw_sdk_user_login_package  b on a.core_account = b.core_account and a.package_id = b.package_id  " +
		" where a.pay_time  between '" + start + "' and '" + end + "' " +
		" and b.time_bj  between '" + start + "' and '" + end + "' " +
		" and pay_result = 1 "

	//`tday`,`time_zone`,`package_id`,`country_id`,`source_id`
	selectSql = selectSql + " group by package_id,country,source_id"
	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("getAdRevenueList=3", selectSql)
	return list, err
}

func getNewAdRevenueList(start, end, numTag string) ([]map[string]interface{}, error) {
	newUserSql := "select core_account " +
		" FROM hw_sdk_user_login_package " +
		" where time_bj between '" + start + "' and '" + end + "'"
	userList, errUser := plusQ.Db("data").List(newUserSql)
	if errUser != nil || len(userList) == 0 {
		return nil, errUser
	}
	users := ""
	for _, user := range userList {
		if fmt.Sprint(user["core_account"]) != "<nil>" && len(fmt.Sprint(user["core_account"])) > 1 {
			if users == "" {
				users = "'" + fmt.Sprintf("%v", user["core_account"]) + "'"
			} else {
				users += "," + "'" + fmt.Sprint(user["core_account"]) + "'"
			}
		}
	}
	if users == "" {
		return nil, nil
	}

	selectSql := "SELECT cp_game_id,game_id,country,package_id,source_id,os," +
		" sum(revenue) as num" +
		" FROM hw_sdk_ad_revenue_log " +
		" where time_bj between '" + start + "' and '" + end + "'"
	if numTag == "new_user_revenue" {
		selectSql = selectSql + " and core_account in (" + users + ")"
	} else {
		selectSql = selectSql + " and core_account not in (" + users + ")"
	}

	selectSql = selectSql + " group by package_id,country,source_id"
	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("getNewAdRevenueList2", selectSql)
	return list, err
}

func getAdList(start, end, tableName string) ([]map[string]interface{}, error) {
	selectSql := "select game_id,cp_game_id, country, package_id,source_id,os," +
		" count(*) AS num" +
		" FROM " + tableName +
		" where time_bj between '" + start + "' and '" + end + "'"
	if "hw_sdk_ad_revenue_log" == tableName {
		selectSql = selectSql + " and revenue > 0 "
	}
	selectSql = selectSql + " GROUP BY package_id,country, source_id "

	list, err := plusQ.Db("data").List(selectSql)
	return list, err
}

func getAdWatchList(start, end, tableName string) ([]map[string]interface{}, error) {
	selectSql := "select game_id,cp_game_id, country, package_id,source_id,os," +
		" count(distinct(core_account)) as num " +
		" FROM " + tableName +
		" where time_bj between '" + start + "' and '" + end + "'"
	selectSql = selectSql + " and revenue > 0 "
	selectSql = selectSql + " GROUP BY package_id,country,source_id"
	list, err := plusQ.Db("data").List(selectSql)
	return list, err
}

func getRemainList(start, end string) ([]map[string]interface{}, error) {

	timeDay, _ := time.Parse("2006-01-02 15:04:05", start)
	timeDay = timeDay.Add(-24 * time.Hour)
	yS := timeDay.Format("2006-01-02") + " 00:00:00"
	yE := timeDay.Format("2006-01-02") + " 23:59:59"

	sql := "select a.game_id,a.cp_game_id,a.package_id,a.country,a.source_id,a.os,count(*) as num " +
		" from ( select newlogin.cp_game_id,newlogin.game_id,newlogin.package_id,newlogin.country,newlogin.source_id,newlogin.os,newlogin.core_account " +
		" from hw_sdk_user_login_package newlogin " +
		" where newlogin.time_bj  between '" + yS + "' and '" + yE + "'" +
		" group by newlogin.cp_game_id,newlogin.game_id,newlogin.package_id,newlogin.country,newlogin.source_id,newlogin.core_account ) as a join" +
		" ( select game_id,package_id,country,source_id,core_account from hw_sdk_user_login " +
		" where time_bj between '" + start + "' and '" + end + "' group by package_id,core_account) as b using(game_id,package_id,core_account ) " +
		" group by package_id,country,source_id"

	list, err := plusQ.Db("data").List(sql)

	return list, err
}
