package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/plus/server"
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/provider"
	"context"
	_ "embed"
	"github.com/joho/godotenv"
	"iaa_data/modules"
	"log"
	"os"
	"os/signal"
	"syscall"
)

//go:embed config/config_prod.yaml
var configProd string

//go:embed config/config.yaml
var configDev string

func main() {

	log.SetFlags(log.LstdFlags | log.Lshortfile)
	// 加载配置文件
	godotenv.Load()
	configStr := configProd
	if os.Getenv("APP_ENV") == "dev" {
		configStr = configDev
	}

	_, err := plus.LoadServerConfigDefault(configStr)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}

	//注册服务
	provider.Register()

	//关闭信号
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM, os.Kill)
	defer stop()

	//注册接口
	services := server.NewWorkerServer(ctx,
		modules.NewService(),
	)
	//启动服务
	err = plus.New(ctx, services).Run("iaa_data")
	if err != nil {
		plusQ.Logger().Alert("shutdown", err)
	}

}
