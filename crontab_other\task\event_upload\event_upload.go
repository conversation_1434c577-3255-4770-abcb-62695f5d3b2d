package event_upload

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/crontab_other/task/event_upload/event_upload_helper"
	"iaa_data/utils"
	"iaa_data/utils/constant"
	"time"
)

func InitFunc(args ...string) {
	uploadRemain<PERSON>ommon(1)
	upload<PERSON><PERSON>in<PERSON>ommon(7)
	upload<PERSON>ema<PERSON><PERSON>om<PERSON>(14)
	upload<PERSON><PERSON>in<PERSON>om<PERSON>(30)
	upload<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(90)
}

func uploadRemain<PERSON><PERSON>mon(day int) {
	now := time.Now()
	nowStr := now.Format("2006-01-02")
	start := nowStr + " 00:00:00"
	end := nowStr + " 23:59:59"
	list, _ := GetRemainList(start, end, day)

	eventName := ""
	if day == 1 {
		eventName = constant.DAY2LOGIN
	} else if day == 7 {
		eventName = constant.DAY7LOGIN
	} else if day == 14 {
		eventName = constant.DAY14LOGIN
	} else if day == 30 {
		eventName = constant.DAY30LOGIN
	} else if day == 90 {
		eventName = constant.DAY90LOGIN
	}
	uploadEvent(list, eventName, event_upload_helper.CHANNEL_ADJUST)
	uploadEvent(list, eventName, event_upload_helper.CHANNEL_FIREBASE)
	uploadEvent(list, eventName, event_upload_helper.CHANNEL_FACEBOOK)

}

func GetRemainList(start, end string, day int) ([]map[string]interface{}, error) {
	timeDay, _ := time.Parse("2006-01-02 15:04:05", start)
	timeDay = timeDay.Add(-24 * time.Duration(day) * time.Hour)
	yS := timeDay.Format("2006-01-02") + " 00:00:00"
	yE := timeDay.Format("2006-01-02") + " 23:59:59"

	sql := "select * " +
		" from ( select * " +
		" from hw_sdk_user_login_package newlogin " +
		" where newlogin.time_bj  between '" + yS + "' and '" + yE + "'" +
		" group by newlogin.core_account ) as a join" +
		" ( select game_id,package_id,core_account from hw_sdk_user_active " +
		" where tday between '" + start + "' and '" + end + "' group by core_account) as b using(game_id,package_id,core_account ) " +
		" group by core_account"

	list, err := plusQ.Db("data").List(sql)

	return list, err
}

func uploadEvent(list []map[string]interface{}, eventName, channel string) {
	for _, item := range list {
		gameId := utils.AnyToInt(item["game_id"])
		coreAccount := fmt.Sprint(item["core_account"])
		keyTag := channel + fmt.Sprint(gameId) + eventName + coreAccount
		val, err := plusQ.Cache("ssdb").Get(keyTag)
		if err != nil || val == nil {
			err := event_upload_helper.UploadEvent(eventName, channel, item)
			if err == nil {
				plusQ.Cache("ssdb").Set(keyTag, "1", 0)
			}
		}
	}
}
