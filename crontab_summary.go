package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/provider"
	"fmt"
	"iaa_data/crontab_summary_data"
	"iaa_data/utils/args_iaa"
	"io"
	"log"
	"os"
	"strings"
	"time"
)

var currentDate string

// 定时任务、消息队列
func main() {

	configFile := "config/config_hw.yaml"
	_, err := plus.LoadServerConfigDefault(configFile)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}
	//plus.AppConfig.ConsoleLogFile = "./log_common/crontab_summary/demo.log"
	provider.Register() //注册服务'

	tag := args_iaa.GetArgs("tag")
	log.Println("==============开始", tag)
	timeZoneStr := args_iaa.GetArgs("zone")

	logFilePath := "crontab_new"
	if tag != "" {
		logFilePath = tag + timeZoneStr
		logFilePath = strings.Replace(logFilePath, "-", "", -1)
		if len(logFilePath) > 20 {
			logFilePath = logFilePath[0:18]
		}
	}
	log.Println("日志路径", logFilePath)

	for {
		today := time.Now().Format("20060102")
		if today != currentDate {

			dirPath := "./log_common/" + logFilePath
			// 检查目录是否存在
			if _, err := os.Stat(dirPath); os.IsNotExist(err) {
				// 创建目录（包括父目录）
				err := os.MkdirAll(dirPath, os.ModePerm)
				if err != nil {
					fmt.Printf("创建目录失败: %v\n", err)
					return
				}
				fmt.Println("目录创建成功")
			}

			file, err := os.OpenFile(dirPath+"/"+today+".log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				log.Fatalf("error opening file: %v", err)
			}
			defer file.Close()

			// 使用io.MultiWriter将日志同时输出到控制台和文件
			AA := io.MultiWriter(os.Stdout, file)
			log.SetOutput(AA)
			currentDate = today
		}

		time.Sleep(time.Second * 2)
		crontab_summary_data.InitServe(tag, timeZoneStr)
	}
}
