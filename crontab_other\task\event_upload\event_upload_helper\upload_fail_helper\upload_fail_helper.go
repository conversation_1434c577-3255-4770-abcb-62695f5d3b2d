package upload_fail_helper

import (
	"encoding/json"
	"iaa_data/utils/sql_helper"
	"time"
)

func Save(channel, event, msg string, revenue float64, data any) {
	item := make(map[string]interface{})
	item["channel_name"] = channel
	item["event_name"] = event
	item["msg"] = msg
	item["revenue"] = revenue
	item["utime"] = time.Now().Format("2006-01-02 15:04:05")

	aa, _ := json.Marshal(data)

	item["event_params"] = string(aa)

	sql_helper.LogHelperInstance().HandleDataMapInsert("data", "upload_fail_event_log", "upload_fail_event_log", item, 1, 1)

	//`channel_name` varchar(100) NOT NULL DEFAULT '' COMMENT '事件名称',
	//	`event_name` varchar(100) NOT NULL DEFAULT '' COMMENT '事件名称',
	//	`event_token` varchar(100) NOT NULL DEFAULT '' COMMENT '事件token',
	//	`event_params` json DEFAULT NULL COMMENT '事件参数',
	//	`revenue` decimal(10,8) unsigned DEFAULT '0.00000000' COMMENT '该次广告产生的收益（美元）',
	//	`msg` varchar(300) NOT NULL DEFAULT '' COMMENT '事件token',
	//	`utime` datetime NOT NULL DEFAULT '1979-01-01 00:00:00' COMMENT '更新时间',

}
