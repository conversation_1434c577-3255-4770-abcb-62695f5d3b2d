package parser

import (
	"910.com/plus2.git/plusQ"
	"context"
	"database/sql"
	"encoding/json"
)

const MysqlBatchSize = 100

// ProcessBatch 批量分批插入数据库
func ProcessBatch(c *context.Context, tableName string, data *[]map[string]any,
	transformer func(map[string]any) map[string]any) error {

	insertData := make([]map[string]any, 0, MysqlBatchSize)

	for _, item := range *data {
		insertData = append(insertData, transformer(item))
		if len(insertData) == MysqlBatchSize {
			if err := BatchInsert(c, tableName, &insertData); err != nil {
				return err
			}
			insertData = insertData[:0] // 清空切片但保留容量
		}
	}
	// 处理剩余数据
	if len(insertData) > 0 {
		return BatchInsert(c, tableName, &insertData)
	}
	return nil
}

// BatchInsert 批量插入数据库
func BatchInsert(ctx *context.Context, tableName string, insertData *[]map[string]interface{}) error {
	return plusQ.Db("platform").Transaction(func(tx *sql.Tx) error {
		sqlQuery, values, err := plusQ.Db("platform").InsertOrUpdateBatch(tableName, *insertData, false)
		if err != nil {
			return err
		}

		_, err = tx.ExecContext(*ctx, sqlQuery, values...)
		if err != nil {
			logError(err, sqlQuery, values)
			return err
		}

		*insertData = (*insertData)[:0] // Clear the slice
		return nil
	})
}

func logError(err error, sqlQuery string, values []interface{}) {
	errMsg := map[string]interface{}{
		"sqlQuery": sqlQuery,
		"values":   values,
	}
	errStr, _ := json.Marshal(errMsg)
	plusQ.Logger().Alert("batch_sql", " Insert error:"+err.Error())
	plusQ.Logger().Alert("batch_sql", " Insert data:"+string(errStr))
}
