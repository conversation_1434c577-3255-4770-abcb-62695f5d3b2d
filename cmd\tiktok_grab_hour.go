package cmd

/*
命令执行：/path/to/cli  -op tiktok_grab_hour -start_date 2025-04-28 -end_date 2025-04-28
*/

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/cmd/helper"
	"iaa_data/cmd/model/tiktok"
	"iaa_data/model/data/ad_account_conf"
	"iaa_data/model/platform/game"
	"iaa_data/utils"
	"strings"
	"time"
)

type TiktokGrabHour struct {
}

func (this *TiktokGrabHour) Run(params map[string]string) error {
	utils.Debug("命令行参数", params)
	var ok bool
	var startDate string
	var endDate string
	startDate, ok = params["start_date"]
	if !ok {
		startDate = time.Now().AddDate(0, 0, -3).Format(time.DateOnly)
	}
	endDate, ok = params["end_date"]
	if !ok {
		endDate = time.Now().Format(time.DateOnly)
	}
	accountId, ok := params["account_id"]
	if !ok {
		accountList, _ := ad_account_conf.ListBy(ad_account_conf.ChannelId.Eq(TK_CHANNEL_ID).And(ad_account_conf.Status.Eq(1)))
		for _, accountInfo := range accountList {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("tiktok_grab_hour_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
		}
	} else {
		accountInfo, _ := ad_account_conf.GetBy(ad_account_conf.ChannelId.Eq(TK_CHANNEL_ID).And(ad_account_conf.AccountId.Eq(accountId)).And(ad_account_conf.Status.Eq(1)))
		if accountInfo != nil {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("tiktok_grab_hour_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
		}
	}

	return nil
}
func (this *TiktokGrabHour) fetch(accountInfo *ad_account_conf.AdAccountConfModel, startDate string, endDate string) error {
	request := tiktok.TikTokReportRequest{
		AdvertiserID: accountInfo.AccountId,
		StartDate:    startDate,
		EndDate:      endDate,
		ServiceType:  "AUCTION",
		ReportType:   "BASIC",
		DataLevel:    "AUCTION_AD",
		Dimensions:   []string{"stat_time_hour", "ad_id", "country_code"},
		Metrics:      []string{"campaign_id", "campaign_name", "spend", "impressions", "clicks", "ctr", "cpm", "conversion", "adgroup_id", "adgroup_name", "ad_name", "ad_id"},
		AccessToken:  "6b25be77664611f8f4833eca1cdcc2a3755a8d3c",
	}
	response, err := getTikTokAdReport(request)
	if err != nil {
		return err
	}
	// 入库处理
	insertData := make([]map[string]any, 0)

	for _, item := range response {

		platformId := 1
		gameId := 0
		cpGameId := 0
		countryCode := ""

		// Mini Heroes_iOS-iOS-US-AEO-ADC-1026
		parts := strings.Split(item.Metrics.CampaignName, "-")
		if len(parts) >= 2 {
			platform := strings.ToLower(parts[1])
			if platform == "ios" {
				platformId = 2
			}
			gameInfo, _ := game.GetBy(game.AppShowName.Eq(parts[0]))
			if gameInfo != nil {
				gameId = int(gameInfo.Id)
				cpGameId = gameInfo.CpGameId
			}
			countryCode = item.Dimensions.CountryCode
			if countryCode == "None" {
				countryCode = parts[2]
			}
		}

		//投放账户时区
		beforeTimezone := strings.ToLower(accountInfo.TimeZone)
		afterTimezone := "utc+0"
		beforeTime := item.Dimensions.StatTimeHour
		afterTime, err := utils.TimeZoneConvert(beforeTime, beforeTimezone, afterTimezone)

		if err != nil {
			return err
		}

		inserted := make(map[string]any)
		// 基本信息
		inserted["source_id"] = 78
		inserted["country_code"] = countryCode
		inserted["time"] = afterTime
		inserted["time_zone"] = afterTimezone

		inserted["channel_id"] = TK_CHANNEL_ID
		inserted["cp_game_id"] = cpGameId
		inserted["game_id"] = gameId
		inserted["package_id"] = 0
		inserted["account_id"] = accountInfo.AccountId
		inserted["ad_account"] = accountInfo.AdAccount
		inserted["platform_id"] = platformId

		inserted["update_time"] = time.Now().Format(time.DateTime)
		// 广告信息
		inserted["campaign_id"] = item.Metrics.CampaignId
		inserted["campaign_name"] = item.Metrics.CampaignName
		inserted["plan_id"] = item.Metrics.AdGroupId
		inserted["plan_name"] = item.Metrics.AdGroupName
		inserted["creative_id_origin"] = item.Metrics.AdId
		inserted["creative_id"] = fmt.Sprintf("%s-%s-%s", item.Metrics.CampaignId, item.Metrics.AdGroupId, item.Metrics.AdId)
		inserted["creative_name"] = item.Metrics.AdName
		// 广告指标
		inserted["show"], _ = item.Metrics.Impressions.Int64()
		inserted["click"], _ = item.Metrics.Clicks.Int64()
		inserted["install"], _ = item.Metrics.Conversion.Int64()
		cost, _ := item.Metrics.Spend.Float64()
		inserted["cost"] = cost
		// 返点后金额
		inserted["cost_discount"] = helper.GetDiscount(cost, afterTime, accountInfo)
		inserted["cpm"], _ = item.Metrics.Cpm.Float64()
		inserted["ctr"], _ = item.Metrics.Ctr.Float64()
		//媒体安装成本
		install, _ := item.Metrics.Conversion.Float64()
		if install > 0 {
			inserted["install_cost"] = cost / install
		} else {
			inserted["install_cost"] = 0
		}

		//点击安装率
		clicks, _ := item.Metrics.Clicks.Float64()
		if clicks > 0 {
			inserted["click_install_rate"] = install / clicks
		} else {
			inserted["click_install_rate"] = 0
		}

		//千次曝光安装量
		show, _ := item.Metrics.Impressions.Float64()
		if show > 0 {
			inserted["install_per_show"] = install / show * 1000
		} else {
			inserted["install_per_show"] = 0
		}

		insertData = append(insertData, inserted)

	}

	if len(insertData) > 0 {
		_, _, e := plusQ.Db("data").InsertOrUpdateBatch("ad_cost_hour", insertData, true)
		if e != nil {
			plusQ.Logger().Error("tiktok_grab_hour_err", insertData)
			plusQ.Logger().Error("tiktok_grab_hour_err", e)
		}
	}

	return nil
}
