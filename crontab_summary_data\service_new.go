package crontab_summary_data

import (
	"iaa_data/crontab_summary_data/task/adjust_match"
	"iaa_data/crontab_summary_data/task/max_hour_report"
	"iaa_data/crontab_summary_data/task/max_ltv_report"
	"iaa_data/crontab_summary_data/task/max_user_report"
	"iaa_data/crontab_summary_data/task/summary_ad_position"
	"iaa_data/crontab_summary_data/task/summary_ad_roi"
	"iaa_data/crontab_summary_data/task/summary_ad_user"
	"iaa_data/crontab_summary_data/task/summary_cost_data"
	"iaa_data/crontab_summary_data/task/summary_operation"
	"strings"
	"time"
)

var oldTimeMap map[string]int64

func InitServe(tag, timeZoneStr string) {
	if oldTimeMap == nil {
		oldTimeMap = make(map[string]int64)
	}
	if tag == "" {
		//===========消耗汇总
		playWork(3, "summary_cost_data", timeZoneStr)

		//广告api抓取
		playWork(30, "max_hour_report", timeZoneStr)
		playWork(3, "max_ltv_report", timeZoneStr)
		playWork(3, "max_user_report", timeZoneStr)

		//============adjust匹配
		playWork(5, "adjust_match", timeZoneStr)

		//=================汇总
		playWork(10, "summary_ad_user", timeZoneStr)
		playWork(25, "summary_operation", timeZoneStr)
		playWork(25, "summary_ad_roi", timeZoneStr)
		playWork(25, "summary_ad_position", timeZoneStr)
	} else {
		tags := strings.Split(tag, "-")
		for _, val := range tags {
			if val == "summary_cost_data" {
				playWork(3, "summary_cost_data", timeZoneStr)
			} else if val == "max_hour_report" {
				playWork(30, "max_hour_report", timeZoneStr)
			} else if val == "max_ltv_report" {
				playWork(3, "max_ltv_report", timeZoneStr)
			} else if val == "max_user_report" {
				playWork(3, "max_user_report", timeZoneStr)
			} else if val == "adjust_match" {
				playWork(5, "adjust_match", timeZoneStr)
			} else if val == "summary_ad_user" {
				playWork(50, "summary_ad_user", timeZoneStr)
			} else if val == "summary_operation" {
				playWork(25, "summary_operation", timeZoneStr)
			} else if val == "summary_ad_roi" {
				playWork(25, "summary_ad_roi", timeZoneStr)
			} else if val == "summary_ad_position" {
				playWork(25, "summary_ad_position", timeZoneStr)
			}
		}
	}

}

func playWork(num int64, mType, timeZoneStr string) {
	oldTime := oldTimeMap[mType]
	now := time.Now().Unix()
	if now-oldTime > num*60 {
		oldTimeMap[mType] = now
		if mType == "summary_cost_data" {
			summary_cost_data.InitFunc()
		} else if mType == "max_hour_report" {
			max_hour_report.InitFunc()
		} else if mType == "max_ltv_report" {
			max_ltv_report.InitFunc()
		} else if mType == "max_user_report" {
			max_user_report.InitFunc()
		} else if mType == "adjust_match" {
			adjust_match.InitFunc()
		} else if mType == "summary_ad_user" {
			summary_ad_user.InitFunc(timeZoneStr)
		} else if mType == "summary_operation" {
			summary_operation.InitFunc(timeZoneStr)
		} else if mType == "summary_ad_roi" {
			summary_ad_roi.InitFunc(timeZoneStr)
		} else if mType == "summary_ad_position" {
			summary_ad_position.InitFunc(timeZoneStr)
		}
	}
}
