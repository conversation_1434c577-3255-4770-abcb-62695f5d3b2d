package day_check_helper

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"time"
)

func CheckDay(tag string) bool {
	if time.Now().Hour() > 16 {
		nowTime := time.Now().Format(time.DateOnly)

		val, err := plusQ.Cache("c0").Get(tag)
		if err != nil {
			plusQ.Cache("c0").Set(tag, nowTime, 0)
			return false
		}
		if fmt.Sprint(val) == nowTime {
			return true
		}
		plusQ.Cache("c0").Set(tag, nowTime, 0)
		return false
	} else {
		return true
	}
}

func CheckDay14(tag string) bool {
	if time.Now().Hour() > 14 {
		nowTime := time.Now().Format(time.DateOnly)

		val, err := plusQ.Cache("c0").Get(tag)
		if err != nil {
			plusQ.Cache("c0").Set(tag, nowTime, 0)
			return false
		}
		if fmt.Sprint(val) == nowTime {
			return true
		}
		plusQ.Cache("c0").Set(tag, nowTime, 0)
		return false
	} else {
		return true
	}
}

func CheckHour7(tag string) string {
	nowTime := time.Now().Format(time.DateOnly)

	val, err := plusQ.Cache("c0").Get(tag)
	if err != nil {
		plusQ.Cache("c0").Set(tag, nowTime, 0)
		return "false"
	}
	if fmt.Sprint(val) == nowTime {
		return "true"
	}
	plusQ.Cache("c0").Set(tag, nowTime, 0)
	return "false"
}
