package args_iaa

import (
	"errors"
	"fmt"
	"os"
	"strings"
	"time"
)

func GetArgs(key string) string {
	keys := os.Args[1:]
	if len(keys) > 0 {
		for _, value := range keys {
			kks := strings.Split(value, "=")
			if len(kks) == 2 {
				if kks[0] == key {
					return kks[1]
				}
			}
		}
	}
	return ""
}

func GetReplayArgs() (time.Time, time.Time, error) {
	defaultTime := time.Now()
	startTimeStr := GetArgs("start")
	if startTimeStr == "" {
		return defaultTime, defaultTime, errors.New("没有参数 start")
	}
	endTimeStr := GetArgs("end")
	if endTimeStr == "" {
		return defaultTime, defaultTime, errors.New("没有参数 end")
	}

	//startT, err := time.Parse("2006-01-02-15:04:05", startTimeStr)
	startT, err := parseBjTime("2006-01-02-15:04:05", startTimeStr)
	if err != nil {
		return defaultTime, defaultTime, err
	}
	//endT, err := time.Parse("2006-01-02-15:04:05", endTimeStr)
	endT, err := parseBjTime("2006-01-02-15:04:05", endTimeStr)
	if err != nil {
		return defaultTime, defaultTime, err
	}

	return startT, endT, nil
}

func GetReplayArgsDataOnly() (time.Time, time.Time, error) {
	defaultTime := time.Now()
	startTimeStr := GetArgs("start")
	if startTimeStr == "" {
		return defaultTime, defaultTime, errors.New("没有参数 start")
	}
	endTimeStr := GetArgs("end")
	if endTimeStr == "" {
		return defaultTime, defaultTime, errors.New("没有参数 end")
	}

	startT, err := parseBjTime("2006-01-02", startTimeStr)
	if err != nil {
		return defaultTime, defaultTime, err
	}
	endT, err := parseBjTime("2006-01-02", endTimeStr)
	if err != nil {
		return defaultTime, defaultTime, err
	}

	return startT, endT, nil
}

func parseBjTime(layout, dateTimeStr string) (time.Time, error) {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Println("加载时区时出错:", err)
		return time.Now(), err
	}
	// 解析字符串为 time.Time 类型，并指定时区
	t, err := time.ParseInLocation(layout, dateTimeStr, loc)
	return t, err
}
