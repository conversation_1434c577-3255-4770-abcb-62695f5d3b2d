package event_upload_helper

type RequestBody struct {
	Data []Event `json:"data"`
}

type Event struct {
	EventName    string     `json:"event_name"`
	EventTime    int64      `json:"event_time"`
	ActionSource string     `json:"action_source"`
	UserData     UserData   `json:"user_data"`
	CustomData   CustomData `json:"custom_data"`
	AppData      AppData    `json:"app_data"`
}

type UserData struct {
	AnonId string `json:"anon_id"`
	Madid  string `json:"madid"`
}

type CustomData struct {
	Value float64 `json:"value,string"` // 处理字符串格式的数字
}

type AppData struct {
	AdvertiserTrackingEnabled  bool     `json:"advertiser_tracking_enabled"`
	ApplicationTrackingEnabled bool     `json:"application_tracking_enabled"`
	CampaignIDs                string   `json:"campaign_ids"`
	Extinfo                    []string `json:"extinfo"`
}
