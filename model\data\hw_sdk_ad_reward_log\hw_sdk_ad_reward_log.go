package hw_sdk_ad_reward_log

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("hw_sdk_ad_reward_log")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增ID
	Id = property.Property("id")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏ID
	GameId = property.Property("game_id")
	// PackageId 游戏包ID
	PackageId = property.Property("package_id")
	// CoreAccount 核心帐号
	CoreAccount = property.Property("core_account")
	// Time 行为发生时间
	Time = property.Property("time")
	// TimeServer 上报到服务器时间
	TimeServer = property.Property("time_server")
	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv = property.Property("aid_or_idfv")
	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa = property.Property("gid_or_idfa")
	// Mac 设备Mac地址
	Mac = property.Property("mac")
	// DeviceCode md5(android_id/gps_adid/IDFV/型号+ip+日期)
	DeviceCode = property.Property("device_code")
	// Useragent UA：一个特殊字符串头，识别客户信息
	Useragent = property.Property("useragent")
	// DeviceType 设备机型
	DeviceType = property.Property("device_type")
	// Os 操作系统类型:1-iOS；2-android；3-other
	Os = property.Property("os")
	// OsVersion 系统版本
	OsVersion = property.Property("os_version")
	// SdkVersion SDK版本号
	SdkVersion = property.Property("sdk_version")
	// GameVersion 游戏版本号
	GameVersion = property.Property("game_version")
	// NetworkType 网络环境：1-wifi；2-移动网络
	NetworkType = property.Property("network_type")
	// MobileType 移动网络类型：
	MobileType = property.Property("mobile_type")
	// ScreenWidth 屏幕分辨率：宽
	ScreenWidth = property.Property("screen_width")
	// ScreenHeight 屏幕分辨率：高
	ScreenHeight = property.Property("screen_height")
	// Ip ip地址：原生IP码
	Ip = property.Property("ip")
	// Ext 备用字段
	Ext = property.Property("ext")
	// DeviceLanguage 设备语言
	DeviceLanguage = property.Property("device_language")
	// AdUnitId 广告平台创建的广告位 ID
	AdUnitId = property.Property("ad_unit_id")
	// Placement 广告场景
	Placement = property.Property("placement")
	// AdType 广告类型 如：全屏视频、激励视频、插屏
	AdType = property.Property("ad_type")
	// CreativeId 创意ID
	CreativeId = property.Property("creative_id")
	// SourceId 媒体来源标识id
	SourceId = property.Property("source_id")
	// AdjustId adjust_id
	AdjustId = property.Property("adjust_id")
	// Md5Key 广告属性表关联值
	Md5Key = property.Property("md5_key")
	// ShowType 广告展示类型（start、end）
	ShowType = property.Property("show_type")
	// ClickId 点击id
	ClickId = property.Property("click_id")
	// Country 投放国家
	Country = property.Property("country")
)

var FieldsAll = Fields(Id, CpGameId, GameId, PackageId, CoreAccount, Time, TimeServer, AidOrIdfv, GidOrIdfa, Mac, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, ScreenWidth, ScreenHeight, Ip, Ext, DeviceLanguage, AdUnitId, Placement, AdType, CreativeId, SourceId, AdjustId, Md5Key, ShowType, ClickId, Country)
var NonePrimaryFields = Fields(CpGameId, GameId, PackageId, CoreAccount, Time, TimeServer, AidOrIdfv, GidOrIdfa, Mac, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, ScreenWidth, ScreenHeight, Ip, Ext, DeviceLanguage, AdUnitId, Placement, AdType, CreativeId, SourceId, AdjustId, Md5Key, ShowType, ClickId, Country)
var NoneAutoIncrementFields = Fields(CpGameId, GameId, PackageId, CoreAccount, Time, TimeServer, AidOrIdfv, GidOrIdfa, Mac, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, ScreenWidth, ScreenHeight, Ip, Ext, DeviceLanguage, AdUnitId, Placement, AdType, CreativeId, SourceId, AdjustId, Md5Key, ShowType, ClickId, Country)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// HwSdkAdRewardLogModel sdk广告奖励日志
type HwSdkAdRewardLogModel struct {
	// Id 自增ID
	Id uint64 `orm:"id" json:"id"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏ID
	GameId int `orm:"game_id" json:"game_id"`

	// PackageId 游戏包ID
	PackageId int `orm:"package_id" json:"package_id"`

	// CoreAccount 核心帐号
	CoreAccount string `orm:"core_account" json:"core_account"`

	// Time 行为发生时间
	Time CustomTime `orm:"time" json:"time"`

	// TimeServer 上报到服务器时间
	TimeServer CustomTime `orm:"time_server" json:"time_server"`

	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv string `orm:"aid_or_idfv" json:"aid_or_idfv"`

	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa string `orm:"gid_or_idfa" json:"gid_or_idfa"`

	// Mac 设备Mac地址
	Mac string `orm:"mac" json:"mac"`

	// DeviceCode md5(android_id/gps_adid/IDFV/型号+ip+日期)
	DeviceCode string `orm:"device_code" json:"device_code"`

	// Useragent UA：一个特殊字符串头，识别客户信息
	Useragent string `orm:"useragent" json:"useragent"`

	// DeviceType 设备机型
	DeviceType string `orm:"device_type" json:"device_type"`

	// Os 操作系统类型:1-iOS；2-android；3-other
	Os string `orm:"os" json:"os"`

	// OsVersion 系统版本
	OsVersion string `orm:"os_version" json:"os_version"`

	// SdkVersion SDK版本号
	SdkVersion string `orm:"sdk_version" json:"sdk_version"`

	// GameVersion 游戏版本号
	GameVersion string `orm:"game_version" json:"game_version"`

	// NetworkType 网络环境：1-wifi；2-移动网络
	NetworkType int `orm:"network_type" json:"network_type"`

	// MobileType 移动网络类型：
	MobileType string `orm:"mobile_type" json:"mobile_type"`

	// ScreenWidth 屏幕分辨率：宽
	ScreenWidth int `orm:"screen_width" json:"screen_width"`

	// ScreenHeight 屏幕分辨率：高
	ScreenHeight int `orm:"screen_height" json:"screen_height"`

	// Ip ip地址：原生IP码
	Ip string `orm:"ip" json:"ip"`

	// Ext 备用字段
	Ext *string `orm:"ext" json:"ext"`

	// DeviceLanguage 设备语言
	DeviceLanguage string `orm:"device_language" json:"device_language"`

	// AdUnitId 广告平台创建的广告位 ID
	AdUnitId string `orm:"ad_unit_id" json:"ad_unit_id"`

	// Placement 广告场景
	Placement string `orm:"placement" json:"placement"`

	// AdType 广告类型 如：全屏视频、激励视频、插屏
	AdType string `orm:"ad_type" json:"ad_type"`

	// CreativeId 创意ID
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// SourceId 媒体来源标识id
	SourceId uint32 `orm:"source_id" json:"source_id"`

	// AdjustId adjust_id
	AdjustId string `orm:"adjust_id" json:"adjust_id"`

	// Md5Key 广告属性表关联值
	Md5Key *string `orm:"md5_key" json:"md5_key"`

	// ShowType 广告展示类型（start、end）
	ShowType string `orm:"show_type" json:"show_type"`

	// ClickId 点击id
	ClickId int `orm:"click_id" json:"click_id"`

	// Country 投放国家
	Country string `orm:"country" json:"country"`
}

type PagedResult struct {
	Records      []*HwSdkAdRewardLogModel `json:"list"`
	PageNum      int                      `json:"page"`
	PageSize     int                      `json:"page_size"`
	TotalPages   int                      `json:"total_pages"`
	TotalRecords int                      `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:             1,
	CpGameId:       2,
	GameId:         3,
	PackageId:      4,
	CoreAccount:    5,
	Time:           6,
	TimeServer:     7,
	AidOrIdfv:      8,
	GidOrIdfa:      9,
	Mac:            10,
	DeviceCode:     11,
	Useragent:      12,
	DeviceType:     13,
	Os:             14,
	OsVersion:      15,
	SdkVersion:     16,
	GameVersion:    17,
	NetworkType:    18,
	MobileType:     19,
	ScreenWidth:    20,
	ScreenHeight:   21,
	Ip:             22,
	Ext:            23,
	DeviceLanguage: 24,
	AdUnitId:       25,
	Placement:      26,
	AdType:         27,
	CreativeId:     28,
	SourceId:       29,
	AdjustId:       30,
	Md5Key:         31,
	ShowType:       32,
	ClickId:        33,
	Country:        34,
}

func (m *HwSdkAdRewardLogModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *HwSdkAdRewardLogModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.CpGameId, m.GameId, m.PackageId, m.CoreAccount, m.Time, m.TimeServer, m.AidOrIdfv, m.GidOrIdfa, m.Mac, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Ext, m.DeviceLanguage, m.AdUnitId, m.Placement, m.AdType, m.CreativeId, m.SourceId, m.AdjustId, m.Md5Key, m.ShowType, m.ClickId, m.Country)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *HwSdkAdRewardLogModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.PackageId, m.CoreAccount, m.Time, m.TimeServer, m.AidOrIdfv, m.GidOrIdfa, m.Mac, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Ext, m.DeviceLanguage, m.AdUnitId, m.Placement, m.AdType, m.CreativeId, m.SourceId, m.AdjustId, m.Md5Key, m.ShowType, m.ClickId, m.Country)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *HwSdkAdRewardLogModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *HwSdkAdRewardLogModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.PackageId, m.CoreAccount, m.Time, m.TimeServer, m.AidOrIdfv, m.GidOrIdfa, m.Mac, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Ext, m.DeviceLanguage, m.AdUnitId, m.Placement, m.AdType, m.CreativeId, m.SourceId, m.AdjustId, m.Md5Key, m.ShowType, m.ClickId, m.Country)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkAdRewardLogModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *HwSdkAdRewardLogModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.PackageId, m.CoreAccount, m.Time, m.TimeServer, m.AidOrIdfv, m.GidOrIdfa, m.Mac, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Ext, m.DeviceLanguage, m.AdUnitId, m.Placement, m.AdType, m.CreativeId, m.SourceId, m.AdjustId, m.Md5Key, m.ShowType, m.ClickId, m.Country), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkAdRewardLogModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *HwSdkAdRewardLogModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.CpGameId, m.GameId, m.PackageId, m.CoreAccount, m.Time, m.TimeServer, m.AidOrIdfv, m.GidOrIdfa, m.Mac, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Ext, m.DeviceLanguage, m.AdUnitId, m.Placement, m.AdType, m.CreativeId, m.SourceId, m.AdjustId, m.Md5Key, m.ShowType, m.ClickId, m.Country), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.PackageId, m.CoreAccount, m.Time, m.TimeServer, m.AidOrIdfv, m.GidOrIdfa, m.Mac, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Ext, m.DeviceLanguage, m.AdUnitId, m.Placement, m.AdType, m.CreativeId, m.SourceId, m.AdjustId, m.Md5Key, m.ShowType, m.ClickId, m.Country))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkAdRewardLogModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *HwSdkAdRewardLogModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *HwSdkAdRewardLogModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *HwSdkAdRewardLogModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *HwSdkAdRewardLogModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *HwSdkAdRewardLogModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*HwSdkAdRewardLogModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*HwSdkAdRewardLogModel, error) {

	modelList := make([]*HwSdkAdRewardLogModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkAdRewardLogModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.CoreAccount, &m.Time, &m.TimeServer, &m.AidOrIdfv, &m.GidOrIdfa, &m.Mac, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Ext, &m.DeviceLanguage, &m.AdUnitId, &m.Placement, &m.AdType, &m.CreativeId, &m.SourceId, &m.AdjustId, &m.Md5Key, &m.ShowType, &m.ClickId, &m.Country)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint64) (*HwSdkAdRewardLogModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint64) (*HwSdkAdRewardLogModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint64) (*HwSdkAdRewardLogModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint64) (*HwSdkAdRewardLogModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*HwSdkAdRewardLogModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*HwSdkAdRewardLogModel, error) {

	m := &HwSdkAdRewardLogModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.CoreAccount, &m.Time, &m.TimeServer, &m.AidOrIdfv, &m.GidOrIdfa, &m.Mac, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Ext, &m.DeviceLanguage, &m.AdUnitId, &m.Placement, &m.AdType, &m.CreativeId, &m.SourceId, &m.AdjustId, &m.Md5Key, &m.ShowType, &m.ClickId, &m.Country)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*HwSdkAdRewardLogModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkAdRewardLogModel, error) {

	modelList := make([]*HwSdkAdRewardLogModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkAdRewardLogModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.CoreAccount, &m.Time, &m.TimeServer, &m.AidOrIdfv, &m.GidOrIdfa, &m.Mac, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Ext, &m.DeviceLanguage, &m.AdUnitId, &m.Placement, &m.AdType, &m.CreativeId, &m.SourceId, &m.AdjustId, &m.Md5Key, &m.ShowType, &m.ClickId, &m.Country)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*HwSdkAdRewardLogModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*HwSdkAdRewardLogModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &HwSdkAdRewardLogModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.CoreAccount, &m.Time, &m.TimeServer, &m.AidOrIdfv, &m.GidOrIdfa, &m.Mac, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Ext, &m.DeviceLanguage, &m.AdUnitId, &m.Placement, &m.AdType, &m.CreativeId, &m.SourceId, &m.AdjustId, &m.Md5Key, &m.ShowType, &m.ClickId, &m.Country)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint64) (*HwSdkAdRewardLogModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint64) (*HwSdkAdRewardLogModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*HwSdkAdRewardLogModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*HwSdkAdRewardLogModel, error) {

	m := &HwSdkAdRewardLogModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*HwSdkAdRewardLogModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkAdRewardLogModel, error) {

	modelList := make([]*HwSdkAdRewardLogModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &HwSdkAdRewardLogModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*HwSdkAdRewardLogModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*HwSdkAdRewardLogModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*HwSdkAdRewardLogModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*HwSdkAdRewardLogModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.CpGameId, d.GameId, d.PackageId, d.CoreAccount, d.Time, d.TimeServer, d.AidOrIdfv, d.GidOrIdfa, d.Mac, d.DeviceCode, d.Useragent, d.DeviceType, d.Os, d.OsVersion, d.SdkVersion, d.GameVersion, d.NetworkType, d.MobileType, d.ScreenWidth, d.ScreenHeight, d.Ip, d.Ext, d.DeviceLanguage, d.AdUnitId, d.Placement, d.AdType, d.CreativeId, d.SourceId, d.AdjustId, d.Md5Key, d.ShowType, d.ClickId, d.Country))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增ID",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "游戏包ID",
		},
		{
			"dataIndex": "core_account",
			"title":     "核心帐号",
		},
		{
			"dataIndex": "time",
			"title":     "行为发生时间",
		},
		{
			"dataIndex": "time_server",
			"title":     "上报到服务器时间",
		},
		{
			"dataIndex": "aid_or_idfv",
			"title":     "安卓ID or idfv",
		},
		{
			"dataIndex": "gid_or_idfa",
			"title":     "谷歌ID or 苹果ID",
		},
		{
			"dataIndex": "mac",
			"title":     "设备Mac地址",
		},
		{
			"dataIndex": "device_code",
			"title":     "md5(android_id/gps_adid/IDFV/型号+ip+日期)",
		},
		{
			"dataIndex": "useragent",
			"title":     "UA：一个特殊字符串头，识别客户信息",
		},
		{
			"dataIndex": "device_type",
			"title":     "设备机型",
		},
		{
			"dataIndex": "os",
			"title":     "操作系统类型:1-iOS；2-android；3-other",
		},
		{
			"dataIndex": "os_version",
			"title":     "系统版本",
		},
		{
			"dataIndex": "sdk_version",
			"title":     "SDK版本号",
		},
		{
			"dataIndex": "game_version",
			"title":     "游戏版本号",
		},
		{
			"dataIndex": "network_type",
			"title":     "网络环境：1-wifi；2-移动网络",
		},
		{
			"dataIndex": "mobile_type",
			"title":     "移动网络类型：",
		},
		{
			"dataIndex": "screen_width",
			"title":     "屏幕分辨率：宽",
		},
		{
			"dataIndex": "screen_height",
			"title":     "屏幕分辨率：高",
		},
		{
			"dataIndex": "ip",
			"title":     "ip地址：原生IP码",
		},
		{
			"dataIndex": "ext",
			"title":     "备用字段",
		},
		{
			"dataIndex": "device_language",
			"title":     "设备语言",
		},
		{
			"dataIndex": "ad_unit_id",
			"title":     "广告平台创建的广告位 ID",
		},
		{
			"dataIndex": "placement",
			"title":     "广告场景",
		},
		{
			"dataIndex": "ad_type",
			"title":     "广告类型 如：全屏视频、激励视频、插屏",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID",
		},
		{
			"dataIndex": "source_id",
			"title":     "媒体来源标识id",
		},
		{
			"dataIndex": "adjust_id",
			"title":     "adjust_id",
		},
		{
			"dataIndex": "md5_key",
			"title":     "广告属性表关联值",
		},
		{
			"dataIndex": "show_type",
			"title":     "广告展示类型（start、end）",
		},
		{
			"dataIndex": "click_id",
			"title":     "点击id",
		},
		{
			"dataIndex": "country",
			"title":     "投放国家",
		}}
}
