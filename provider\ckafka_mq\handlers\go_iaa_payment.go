package handlers

// 单机版订单入库处理
import (
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/utils/system"
	"context"
	"crypto/md5"
	"encoding/hex"
	amqp "github.com/rabbitmq/amqp091-go"
	"iaa_data/model/platform/test_account"
	"iaa_data/provider/ckafka_mq/parser"
	"iaa_data/utils"
	"time"
)

type GoIaaPaymentHandler struct {
}

func (this *GoIaaPaymentHandler) Callback(msg amqp.Delivery, ctx context.Context) error {
	plusQ.Logger().Info("go_iaa_payment", string(msg.Body))

	data, err := parser.Extract(string(msg.Body))
	if err != nil {
		return err
	}

	err = parser.ProcessBatch(&ctx, "payment", &data, func(item map[string]any) map[string]any {
		amount := parser.GetFloat(&item, "pay_amount") // 支付金额美分

		millis := parser.GetInt(&item, "order_time") // 毫秒时间戳
		sec := millis / 1000
		nsec := (millis % 1000) * 1000000
		t := time.Unix(sec, nsec)

		// 更新扩展表
		os := parser.GetStr(&item, "os")
		payResult := parser.GetInt(&item, "pay_result")
		if os == "android" && payResult == 1 {
			hash := md5.New()
			purchaseToken := parser.GetStr(&item, "purchase_token")
			hash.Write([]byte(purchaseToken))
			hashSum := hash.Sum(nil)
			receiptMd5 := hex.EncodeToString(hashSum)

			insertData := []map[string]any{{
				"order_id":          parser.GetStr(&item, "order_id"),
				"google_receipt":    purchaseToken,
				"receipt_md5":       receiptMd5,
				"add_time":          t.Format(time.DateTime),
				"receipt_type":      1, // 客户端无法区分
				"google_order_id":   parser.GetStr(&item, "channel_order_id"),
				"google_product_id": parser.GetStr(&item, "pay_product_id"),
				"pay_type":          parser.GetStr(&item, "pay_type"),
			}}
			_ = parser.BatchInsert(&ctx, "google_receipt", &insertData)
		}
		if os == "ios" && payResult == 1 {
			insertData := []map[string]any{{
				"order_id":              parser.GetStr(&item, "order_id"),
				"add_time":              t.Format(time.DateTime),
				"receipt_type":          1, // 客户端无法区分
				"transaction_id":        parser.GetStr(&item, "transaction_id"),
				"origin_transaction_id": parser.GetStr(&item, "origin_transaction_id"),
				"pay_type":              parser.GetStr(&item, "pay_type"),
			}}
			_ = parser.BatchInsert(&ctx, "ios_receipt", &insertData)
		}

		// 标记测试订单
		orderType := 1
		acc, _ := test_account.GetBy(test_account.CoreAccount.Eq(parser.GetStr(&item, "core_account")))
		if acc != nil {
			orderType = 2
		}

		ip := parser.GetStr(&item, "ip")
		country := utils.GetIpCountryCode(ip)

		return map[string]any{
			"device_code":      parser.GetStr(&item, "device_code"),
			"core_account":     parser.GetStr(&item, "core_account"),
			"order_id":         parser.GetStr(&item, "order_id"),
			"co_order_id":      parser.GetStr(&item, "cp_order_id"),
			"channel_order_id": parser.GetStr(&item, "channel_order_id"),
			"game_id":          parser.GetInt(&item, "game_id"),
			"package_id":       parser.GetInt(&item, "package_id"),
			"amount":           amount / 100,
			"payway":           system.If(os == "ios", "apple", "google"),
			"order_time":       t.Format(time.DateTime),
			"pay_time":         t.Format(time.DateTime),
			"pay_result":       system.If(parser.GetInt(&item, "pay_result") == 1, 1, 0),
			"game_result":      system.If(parser.GetInt(&item, "pay_result") == 1, 1, 0),
			"cp_product_id":    parser.GetStr(&item, "pay_product_id"),
			"product_id":       parser.GetStr(&item, "pay_product_id"),
			"product_name":     parser.GetStr(&item, "pay_product"),
			"android_id":       parser.GetStr(&item, "android_id"),
			"idfa":             parser.GetStr(&item, "idfa"),
			"idfv":             parser.GetStr(&item, "idfv"),
			"gps_adid":         parser.GetStr(&item, "gps_adid"),
			"source":           1, // 单机版
			"pay_currency":     parser.GetStr(&item, "pay_currency"),
			"order_amount":     parser.GetFloat(&item, "order_amount"),
			"real_amount":      parser.GetFloat(&item, "order_amount"),
			"ip":               ip,
			"country_code":     country,
			"order_type":       orderType,
		}
	})

	return err
}
