package google

import "encoding/json"

type Response struct {
	Results []Result `json:"results"`
}

type Result struct {
	Campaign  Campaign  `json:"campaign"`
	AdGroup   AdGroup   `json:"adGroup"`
	Metrics   Metrics   `json:"metrics"`
	AdGroupAd AdGroupAd `json:"adGroupAd"`
	Segments  Segments  `json:"segments"`
}
type Campaign struct {
	ResourceName string      `json:"resourceName"`
	Name         string      `json:"name"`
	ID           json.Number `json:"id"`
}

type AdGroup struct {
	ResourceName string      `json:"resourceName"`
	ID           json.Number `json:"id"`
	Name         string      `json:"name"`
}

type Metrics struct {
	Clicks      json.Number `json:"clicks"`
	Conversions json.Number `json:"conversions"`
	CostMicros  json.Number `json:"costMicros"`
	Ctr         json.Number `json:"ctr"`
	AverageCpm  json.Number `json:"averageCpm"`
	Impressions json.Number `json:"impressions"`
}

type AdGroupAd struct {
	ResourceName string `json:"resourceName"`
	Status       string `json:"status"`
	Ad           Ad     `json:"ad"`
}

type Ad struct {
	Type         string      `json:"type"`
	ResourceName string      `json:"resourceName"`
	ID           json.Number `json:"id"`
}

type Segments struct {
	Date string `json:"date"`
}

type HwtjData struct {
	Token string `json:"token"`
}
type HwtjRes struct {
	Code int      `json:"code"`
	Data HwtjData `json:"data"`
}
