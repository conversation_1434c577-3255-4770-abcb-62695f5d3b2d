package utils

import (
	"encoding/json"
	"fmt"
	"github.com/oschwald/geoip2-golang"
	"github.com/pkg/errors"
	"net"
	"reflect"
	"strconv"
	"strings"
	"time"
)

// interface 转 string by GPT
func InterfaceToString(input interface{}) string {
	value := reflect.ValueOf(input)
	//fmt.Printf(" \n vue.kind %+v \n", value.Kind())
	switch value.Kind() {
	case reflect.String:
		return value.String()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(value.Int(), 10)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return strconv.FormatUint(value.Uint(), 10)
	case reflect.Float32, reflect.Float64:
		return strconv.FormatFloat(value.Float(), 'f', 0, 64)
	case reflect.Bool:
		return strconv.FormatBool(value.Bool())
	case reflect.Map, reflect.Slice:
		js, _ := json.Marshal(input)
		fmt.Println(string(js))
		return string(js)
	default:
		return ""
	}
}

func RemoveDuplicates[T comparable](strArray []T) []T {
	seen := make(map[T]bool)
	result := []T{}
	for _, str := range strArray {
		if !seen[str] {
			seen[str] = true
			result = append(result, str)
		}
	}
	return result
}

func GetDateRange(startDate string, endDate string) ([]string, error) {
	layout := time.DateOnly
	start, err := time.Parse(layout, startDate)
	if err != nil {
		return nil, err
	}
	end, err := time.Parse(layout, endDate)
	if err != nil {
		return nil, err
	}
	if start.After(end) {
		return nil, errors.New("开始时间不能大于结束时间")
	}
	var dates []string
	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		dates = append(dates, d.Format(layout))
	}
	return dates, nil
}

func TimeZoneConvert(beforeTime string, beforeTimezone string, afterTimezone string) (string, error) {
	t, err := time.Parse(time.DateTime, beforeTime)
	if err != nil {
		return "", fmt.Errorf("解析时间错误: %v", err)
	}
	// 解析时区字符串，例如 "utc-8" 或 "utc+8"
	var beforeOffset, afterOffset int
	if strings.HasPrefix(beforeTimezone, "utc") {
		offsetStr := beforeTimezone[3:]
		if offsetStr == "" {
			beforeOffset = 0
		} else {
			offset, err := strconv.Atoi(offsetStr)
			if err != nil {
				return "", fmt.Errorf("无效的时区格式 %s: %v", beforeTimezone, err)
			}
			beforeOffset = offset
		}
	} else {
		return "", fmt.Errorf("无效的时区格式: %s", beforeTimezone)
	}
	if strings.HasPrefix(afterTimezone, "utc") {
		offsetStr := afterTimezone[3:]
		if offsetStr == "" {
			afterOffset = 0
		} else {
			offset, err := strconv.Atoi(offsetStr)
			if err != nil {
				return "", fmt.Errorf("无效的时区格式 %s: %v", afterTimezone, err)
			}
			afterOffset = offset
		}
	} else {
		return "", fmt.Errorf("无效的时区格式: %s", afterTimezone)
	}
	// 计算时区差异（小时）
	hoursDiff := afterOffset - beforeOffset
	// 调整时间
	adjustedTime := t.Add(time.Duration(hoursDiff) * time.Hour)
	// 格式化输出
	return adjustedTime.Format(time.DateTime), nil
}

func GetIpCountryCode(ipStr string) string {
	if ipStr == "" {
		return ""
	}
	db, err := geoip2.Open("/www/iaa_data/GeoLite2-City.mmdb")
	if err != nil {
		return ""
	}
	defer db.Close()
	ip := net.ParseIP(ipStr)
	record, err := db.Country(ip)
	if err != nil {
		return ""
	}
	return record.Country.IsoCode
}
