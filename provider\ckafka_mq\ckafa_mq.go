package ckafka_mq

import (
	"910.com/plus2.git/plusQ"
	"context"
	"fmt"
	"github.com/confluentinc/confluent-kafka-go/kafka"
	amqp "github.com/rabbitmq/amqp091-go"
	"iaa_data/provider/ckafka_mq/handlers"
	"log"
	"sync"
	"time"
)

func NewCKafkaMq() *CKafkaMQ {
	return &CKafkaMQ{}
}

type CKafkaMQ struct {
	Consumer *kafka.Consumer
	Producer *kafka.Producer
	config   map[string]any
}

func (this *CKafkaMQ) SetConfig(config map[string]interface{}) error {
	this.config = config
	var err error
	sasl, ok := config["sasl"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid ckafa sasl config")
	}
	topicsInterface, ok := config["topics"].([]interface{})
	if !ok {
		return fmt.Errorf("invalid ckafa topics config")
	}
	if len(topicsInterface) == 0 {
		return fmt.Errorf("empty ckafa topics config")
	}

	this.Consumer, err = kafka.NewConsumer(&kafka.ConfigMap{
		// 设置接入点，请通过控制台获取对应Topic的接入点。
		"bootstrap.servers": config["bootstrapServers"].(string),
		// SASL 验证机制类型默认选用 PLAIN
		"sasl.mechanism": "PLAIN",
		// 在本地配置 ACL 策略。
		"security.protocol": "SASL_PLAINTEXT",
		// username 是配置的用户名，password 是配置的用户密码。
		"sasl.username": fmt.Sprintf("%s#%s", sasl["instanceId"], sasl["username"]),
		"sasl.password": sasl["password"].(string),
		// 设置的消息消费组
		"group.id":          config["consumerGroupId"],
		"auto.offset.reset": "earliest",
		// 使用 Kafka 消费分组机制时，消费者超时时间。当 Broker 在该时间内没有收到消费者的心跳时，认为该消费者故障失败，Broker
		// 发起重新 Rebalance 过程。目前该值的配置必须在 Broker 配置group.min.session.timeout.ms=6000和group.max.session.timeout.ms=300000 之间

		// 会话和心跳优化
		"session.timeout.ms":    30000,  // 会话超时30秒
		"heartbeat.interval.ms": 10000,  // 心跳间隔10秒
		"max.poll.interval.ms":  300000, // 最大轮询间隔5分钟

		// 连接超时优化
		"socket.timeout.ms":                  30000, // 增加socket超时时间到30秒
		"socket.connection.setup.timeout.ms": 30000, // 连接建立超时30秒
		"socket.keepalive.enable":            true,  // 启用TCP keepalive

	})
	if err != nil {
		return err
	}
	// 订阅主题
	var topics []string
	for _, topic := range topicsInterface {
		if str, ok2 := topic.(string); ok2 {
			topics = append(topics, str)
		}
	}
	err = this.Consumer.SubscribeTopics(topics, nil)
	if err != nil {
		return err
	}

	this.Producer, err = kafka.NewProducer(&kafka.ConfigMap{
		// 设置接入点，请通过控制台获取对应Topic的接入点。
		"bootstrap.servers": config["bootstrapServers"].(string),
		// SASL 验证机制类型默认选用 PLAIN
		"sasl.mechanism": "PLAIN",
		// 在本地配置 ACL 策略。
		"security.protocol": "SASL_PLAINTEXT",
		// username 是配置的用户名，password 是配置的用户密码。
		"sasl.username": fmt.Sprintf("%s#%s", sasl["instanceId"], sasl["username"]),
		"sasl.password": sasl["password"].(string),

		"acks":                     1,
		"retries":                  3,
		"retry.backoff.ms":         100,
		"socket.timeout.ms":        6000,
		"reconnect.backoff.max.ms": 3000,
	})
	if err != nil {
		return err
	}

	return nil
}

// Put 消息入队
func (this *CKafkaMQ) Put(queueName string, data any, persistent ...bool) error {
	err := this.Producer.Produce(&kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &queueName, Partition: kafka.PartitionAny},
		Value:          []byte(fmt.Sprintf("%v", data)),
	}, nil)
	this.Producer.Flush(10 * 1000)

	return err
}

// ConsumeWithContext Consume 消息出队
func (this *CKafkaMQ) ConsumeWithContext(ctx context.Context, queueName string, callback func(amqp.Delivery) error, durable ...bool) error {
	// 注册处理函数
	chandlers := handlers.NewCkafkaHandlers().RegisterHandlers()

	// 并发控制
	maxWorkers := 10
	semaphore := make(chan struct{}, maxWorkers)
	var wg sync.WaitGroup

	for {
		select {
		case <-ctx.Done():
			// 优雅关闭：等待所有goroutine完成
			log.Println("收到信号，等待队列消息处理完成")
			wg.Wait()
			log.Println("消息处理完成，关闭ckafka链接")
			this.Close()
			return ctx.Err()
		default:
			// 使用超时设置来允许定期检查上下文是否被取消
			result, err := this.Consumer.ReadMessage(100 * time.Millisecond)
			if err != nil {
				kafkaErr, _ := err.(kafka.Error)
				if kafkaErr.Code() == kafka.ErrTimedOut {
					continue
				} else if kafkaErr.Code() == kafka.ErrTransport || kafkaErr.Code() == kafka.ErrAllBrokersDown || kafkaErr.Code() == kafka.ErrNetworkException {
					log.Println("ckafka网络异常，等待10秒后重连接")
					time.Sleep(10 * time.Second)
					this.Close()
					this.SetConfig(this.config)
					continue
				} else {
					plusQ.Logger().Alert("ckafka_mq", err)
					time.Sleep(time.Second)
					continue
				}
			}
			topic := result.TopicPartition.Topic
			handler, exists := chandlers[*topic]
			if exists {
				// 创建一个模拟的 amqp.Delivery 对象
				delivery := amqp.Delivery{
					Body: []byte(result.Value),
				}

				// 异步处理消息
				wg.Add(1)
				go func(delivery amqp.Delivery, handler handlers.Handler) {
					defer wg.Done()

					// 获取信号量，控制并发数
					semaphore <- struct{}{}
					defer func() { <-semaphore }()

					// 调用回调函数处理消息
					if err := handler.Callback(delivery, ctx); err != nil {
						plusQ.Logger().Alert("ckafka_mq", fmt.Sprintf("消息处理失败: %v", err))
					}
				}(delivery, handler)
			} else {
				plusQ.Logger().Alert("ckafka_mq", fmt.Sprintf("队列%s的回调函数不存在", *topic))
			}
		}
	}
}

func (this *CKafkaMQ) Publish(exchangeName string, msg any, deferTime int, exchangeType string, routingKey string, durable bool) error {
	return nil
}

func (this *CKafkaMQ) SubscribeWithContext(ctx context.Context, callback func(amqp.Delivery) error, exchangeName string, topicName string, exchangeType string, routingKey string, durable bool) error {
	return nil
}

func (this *CKafkaMQ) Close() {
	if this.Consumer != nil {
		_ = this.Consumer.Close()
	}
	if this.Producer != nil {
		this.Producer.Close()
	}
}
