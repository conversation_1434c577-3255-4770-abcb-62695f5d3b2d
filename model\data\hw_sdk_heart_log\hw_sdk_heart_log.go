package hw_sdk_heart_log

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("hw_sdk_heart_log")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增ID
	Id = property.Property("id")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏ID
	GameId = property.Property("game_id")
	// PackageId 游戏包ID
	PackageId = property.Property("package_id")
	// TimeLocal 行为发生本地时间
	TimeLocal = property.Property("time_local")
	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv = property.Property("aid_or_idfv")
	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa = property.Property("gid_or_idfa")
	// CoreAccount 用户ID(核心账号)
	CoreAccount = property.Property("core_account")
	// TimeBj 行为发生北京时间
	TimeBj = property.Property("time_bj")
	// TimeServer 上报到服务器的时间
	TimeServer = property.Property("time_server")
	// DeviceCode md5(MAC+android_id/IDFA)
	DeviceCode = property.Property("device_code")
	// Useragent 一个特殊字符串头,识别客户信息
	Useragent = property.Property("useragent")
	// DeviceType 设备机型
	DeviceType = property.Property("device_type")
	// Os 操作系统类型:1-android,2-iOS,3-WINPHONE
	Os = property.Property("os")
	// OsVersion 系统版本
	OsVersion = property.Property("os_version")
	// SdkVersion SDK版本号
	SdkVersion = property.Property("sdk_version")
	// GameVersion 游戏版本号
	GameVersion = property.Property("game_version")
	// NetworkType 网络环境:0-没有网络,1-WIFI,2-2G,3-3G,4-WAP,5-Other
	NetworkType = property.Property("network_type")
	// MobileType 移动网络类型:1-移动,2-联通,3-电信,4-其它
	MobileType = property.Property("mobile_type")
	// Ip ip地址:原生IP码
	Ip = property.Property("ip")
	// Country 投放国家(地区)
	Country = property.Property("country")
	// Mac MAC地址
	Mac = property.Property("mac")
	// DeviceLanguage 设备语言
	DeviceLanguage = property.Property("device_language")
	// GameServerId 服务器id
	GameServerId = property.Property("game_server_id")
	// RoleId 角色id
	RoleId = property.Property("role_id")
	// HeartInterval 时间间隔s
	HeartInterval = property.Property("heart_interval")
	// StatHour 小时
	StatHour = property.Property("stat_hour")
	// StatDay 天
	StatDay = property.Property("stat_day")
	// StatMonth 月
	StatMonth = property.Property("stat_month")
	// StatYear 年
	StatYear = property.Property("stat_year")
)

var FieldsAll = Fields(Id, CpGameId, GameId, PackageId, TimeLocal, AidOrIdfv, GidOrIdfa, CoreAccount, TimeBj, TimeServer, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, Ip, Country, Mac, DeviceLanguage, GameServerId, RoleId, HeartInterval, StatHour, StatDay, StatMonth, StatYear)
var NonePrimaryFields = Fields(CpGameId, GameId, PackageId, TimeLocal, AidOrIdfv, GidOrIdfa, CoreAccount, TimeBj, TimeServer, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, Ip, Country, Mac, DeviceLanguage, GameServerId, RoleId, HeartInterval, StatHour, StatDay, StatMonth, StatYear)
var NoneAutoIncrementFields = Fields(CpGameId, GameId, PackageId, TimeLocal, AidOrIdfv, GidOrIdfa, CoreAccount, TimeBj, TimeServer, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, Ip, Country, Mac, DeviceLanguage, GameServerId, RoleId, HeartInterval, StatHour, StatDay, StatMonth, StatYear)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// HwSdkHeartLogModel 心跳日志
type HwSdkHeartLogModel struct {
	// Id 自增ID
	Id uint64 `orm:"id" json:"id"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏ID
	GameId int `orm:"game_id" json:"game_id"`

	// PackageId 游戏包ID
	PackageId int `orm:"package_id" json:"package_id"`

	// TimeLocal 行为发生本地时间
	TimeLocal CustomTime `orm:"time_local" json:"time_local"`

	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv string `orm:"aid_or_idfv" json:"aid_or_idfv"`

	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa string `orm:"gid_or_idfa" json:"gid_or_idfa"`

	// CoreAccount 用户ID(核心账号)
	CoreAccount string `orm:"core_account" json:"core_account"`

	// TimeBj 行为发生北京时间
	TimeBj CustomTime `orm:"time_bj" json:"time_bj"`

	// TimeServer 上报到服务器的时间
	TimeServer CustomTime `orm:"time_server" json:"time_server"`

	// DeviceCode md5(MAC+android_id/IDFA)
	DeviceCode string `orm:"device_code" json:"device_code"`

	// Useragent 一个特殊字符串头,识别客户信息
	Useragent string `orm:"useragent" json:"useragent"`

	// DeviceType 设备机型
	DeviceType string `orm:"device_type" json:"device_type"`

	// Os 操作系统类型:1-android,2-iOS,3-WINPHONE
	Os int `orm:"os" json:"os"`

	// OsVersion 系统版本
	OsVersion string `orm:"os_version" json:"os_version"`

	// SdkVersion SDK版本号
	SdkVersion string `orm:"sdk_version" json:"sdk_version"`

	// GameVersion 游戏版本号
	GameVersion string `orm:"game_version" json:"game_version"`

	// NetworkType 网络环境:0-没有网络,1-WIFI,2-2G,3-3G,4-WAP,5-Other
	NetworkType string `orm:"network_type" json:"network_type"`

	// MobileType 移动网络类型:1-移动,2-联通,3-电信,4-其它
	MobileType int `orm:"mobile_type" json:"mobile_type"`

	// Ip ip地址:原生IP码
	Ip string `orm:"ip" json:"ip"`

	// Country 投放国家(地区)
	Country string `orm:"country" json:"country"`

	// Mac MAC地址
	Mac string `orm:"mac" json:"mac"`

	// DeviceLanguage 设备语言
	DeviceLanguage string `orm:"device_language" json:"device_language"`

	// GameServerId 服务器id
	GameServerId int `orm:"game_server_id" json:"game_server_id"`

	// RoleId 角色id
	RoleId string `orm:"role_id" json:"role_id"`

	// HeartInterval 时间间隔s
	HeartInterval int `orm:"heart_interval" json:"heart_interval"`

	// StatHour 小时
	StatHour int `orm:"stat_hour" json:"stat_hour"`

	// StatDay 天
	StatDay int `orm:"stat_day" json:"stat_day"`

	// StatMonth 月
	StatMonth int `orm:"stat_month" json:"stat_month"`

	// StatYear 年
	StatYear int `orm:"stat_year" json:"stat_year"`
}

type PagedResult struct {
	Records      []*HwSdkHeartLogModel `json:"list"`
	PageNum      int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
	TotalPages   int                   `json:"total_pages"`
	TotalRecords int                   `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:             1,
	CpGameId:       2,
	GameId:         3,
	PackageId:      4,
	TimeLocal:      5,
	AidOrIdfv:      6,
	GidOrIdfa:      7,
	CoreAccount:    8,
	TimeBj:         9,
	TimeServer:     10,
	DeviceCode:     11,
	Useragent:      12,
	DeviceType:     13,
	Os:             14,
	OsVersion:      15,
	SdkVersion:     16,
	GameVersion:    17,
	NetworkType:    18,
	MobileType:     19,
	Ip:             20,
	Country:        21,
	Mac:            22,
	DeviceLanguage: 23,
	GameServerId:   24,
	RoleId:         25,
	HeartInterval:  26,
	StatHour:       27,
	StatDay:        28,
	StatMonth:      29,
	StatYear:       30,
}

func (m *HwSdkHeartLogModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *HwSdkHeartLogModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.CpGameId, m.GameId, m.PackageId, m.TimeLocal, m.AidOrIdfv, m.GidOrIdfa, m.CoreAccount, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.Ip, m.Country, m.Mac, m.DeviceLanguage, m.GameServerId, m.RoleId, m.HeartInterval, m.StatHour, m.StatDay, m.StatMonth, m.StatYear)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *HwSdkHeartLogModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.PackageId, m.TimeLocal, m.AidOrIdfv, m.GidOrIdfa, m.CoreAccount, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.Ip, m.Country, m.Mac, m.DeviceLanguage, m.GameServerId, m.RoleId, m.HeartInterval, m.StatHour, m.StatDay, m.StatMonth, m.StatYear)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *HwSdkHeartLogModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *HwSdkHeartLogModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.PackageId, m.TimeLocal, m.AidOrIdfv, m.GidOrIdfa, m.CoreAccount, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.Ip, m.Country, m.Mac, m.DeviceLanguage, m.GameServerId, m.RoleId, m.HeartInterval, m.StatHour, m.StatDay, m.StatMonth, m.StatYear)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkHeartLogModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *HwSdkHeartLogModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.PackageId, m.TimeLocal, m.AidOrIdfv, m.GidOrIdfa, m.CoreAccount, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.Ip, m.Country, m.Mac, m.DeviceLanguage, m.GameServerId, m.RoleId, m.HeartInterval, m.StatHour, m.StatDay, m.StatMonth, m.StatYear), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkHeartLogModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *HwSdkHeartLogModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.CpGameId, m.GameId, m.PackageId, m.TimeLocal, m.AidOrIdfv, m.GidOrIdfa, m.CoreAccount, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.Ip, m.Country, m.Mac, m.DeviceLanguage, m.GameServerId, m.RoleId, m.HeartInterval, m.StatHour, m.StatDay, m.StatMonth, m.StatYear), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.PackageId, m.TimeLocal, m.AidOrIdfv, m.GidOrIdfa, m.CoreAccount, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.Ip, m.Country, m.Mac, m.DeviceLanguage, m.GameServerId, m.RoleId, m.HeartInterval, m.StatHour, m.StatDay, m.StatMonth, m.StatYear))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkHeartLogModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *HwSdkHeartLogModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *HwSdkHeartLogModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *HwSdkHeartLogModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *HwSdkHeartLogModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *HwSdkHeartLogModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*HwSdkHeartLogModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*HwSdkHeartLogModel, error) {

	modelList := make([]*HwSdkHeartLogModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkHeartLogModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.TimeLocal, &m.AidOrIdfv, &m.GidOrIdfa, &m.CoreAccount, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.Ip, &m.Country, &m.Mac, &m.DeviceLanguage, &m.GameServerId, &m.RoleId, &m.HeartInterval, &m.StatHour, &m.StatDay, &m.StatMonth, &m.StatYear)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint64) (*HwSdkHeartLogModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint64) (*HwSdkHeartLogModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint64) (*HwSdkHeartLogModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint64) (*HwSdkHeartLogModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*HwSdkHeartLogModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*HwSdkHeartLogModel, error) {

	m := &HwSdkHeartLogModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.TimeLocal, &m.AidOrIdfv, &m.GidOrIdfa, &m.CoreAccount, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.Ip, &m.Country, &m.Mac, &m.DeviceLanguage, &m.GameServerId, &m.RoleId, &m.HeartInterval, &m.StatHour, &m.StatDay, &m.StatMonth, &m.StatYear)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*HwSdkHeartLogModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkHeartLogModel, error) {

	modelList := make([]*HwSdkHeartLogModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkHeartLogModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.TimeLocal, &m.AidOrIdfv, &m.GidOrIdfa, &m.CoreAccount, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.Ip, &m.Country, &m.Mac, &m.DeviceLanguage, &m.GameServerId, &m.RoleId, &m.HeartInterval, &m.StatHour, &m.StatDay, &m.StatMonth, &m.StatYear)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*HwSdkHeartLogModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*HwSdkHeartLogModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &HwSdkHeartLogModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.TimeLocal, &m.AidOrIdfv, &m.GidOrIdfa, &m.CoreAccount, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.Ip, &m.Country, &m.Mac, &m.DeviceLanguage, &m.GameServerId, &m.RoleId, &m.HeartInterval, &m.StatHour, &m.StatDay, &m.StatMonth, &m.StatYear)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint64) (*HwSdkHeartLogModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint64) (*HwSdkHeartLogModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*HwSdkHeartLogModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*HwSdkHeartLogModel, error) {

	m := &HwSdkHeartLogModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*HwSdkHeartLogModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkHeartLogModel, error) {

	modelList := make([]*HwSdkHeartLogModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &HwSdkHeartLogModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*HwSdkHeartLogModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*HwSdkHeartLogModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*HwSdkHeartLogModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*HwSdkHeartLogModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.CpGameId, d.GameId, d.PackageId, d.TimeLocal, d.AidOrIdfv, d.GidOrIdfa, d.CoreAccount, d.TimeBj, d.TimeServer, d.DeviceCode, d.Useragent, d.DeviceType, d.Os, d.OsVersion, d.SdkVersion, d.GameVersion, d.NetworkType, d.MobileType, d.Ip, d.Country, d.Mac, d.DeviceLanguage, d.GameServerId, d.RoleId, d.HeartInterval, d.StatHour, d.StatDay, d.StatMonth, d.StatYear))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增ID",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "游戏包ID",
		},
		{
			"dataIndex": "time_local",
			"title":     "行为发生本地时间",
		},
		{
			"dataIndex": "aid_or_idfv",
			"title":     "安卓ID or idfv",
		},
		{
			"dataIndex": "gid_or_idfa",
			"title":     "谷歌ID or 苹果ID",
		},
		{
			"dataIndex": "core_account",
			"title":     "用户ID(核心账号)",
		},
		{
			"dataIndex": "time_bj",
			"title":     "行为发生北京时间",
		},
		{
			"dataIndex": "time_server",
			"title":     "上报到服务器的时间",
		},
		{
			"dataIndex": "device_code",
			"title":     "md5(MAC+android_id/IDFA)",
		},
		{
			"dataIndex": "useragent",
			"title":     "一个特殊字符串头,识别客户信息",
		},
		{
			"dataIndex": "device_type",
			"title":     "设备机型",
		},
		{
			"dataIndex": "os",
			"title":     "操作系统类型:1-android,2-iOS,3-WINPHONE",
		},
		{
			"dataIndex": "os_version",
			"title":     "系统版本",
		},
		{
			"dataIndex": "sdk_version",
			"title":     "SDK版本号",
		},
		{
			"dataIndex": "game_version",
			"title":     "游戏版本号",
		},
		{
			"dataIndex": "network_type",
			"title":     "网络环境:0-没有网络,1-WIFI,2-2G,3-3G,4-WAP,5-Other",
		},
		{
			"dataIndex": "mobile_type",
			"title":     "移动网络类型:1-移动,2-联通,3-电信,4-其它",
		},
		{
			"dataIndex": "ip",
			"title":     "ip地址:原生IP码",
		},
		{
			"dataIndex": "country",
			"title":     "投放国家(地区)",
		},
		{
			"dataIndex": "mac",
			"title":     "MAC地址",
		},
		{
			"dataIndex": "device_language",
			"title":     "设备语言",
		},
		{
			"dataIndex": "game_server_id",
			"title":     "服务器id",
		},
		{
			"dataIndex": "role_id",
			"title":     "角色id",
		},
		{
			"dataIndex": "heart_interval",
			"title":     "时间间隔s",
		},
		{
			"dataIndex": "stat_hour",
			"title":     "小时",
		},
		{
			"dataIndex": "stat_day",
			"title":     "天",
		},
		{
			"dataIndex": "stat_month",
			"title":     "月",
		},
		{
			"dataIndex": "stat_year",
			"title":     "年",
		}}
}
