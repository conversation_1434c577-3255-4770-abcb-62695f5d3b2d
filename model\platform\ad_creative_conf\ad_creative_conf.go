package ad_creative_conf

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("platform")
	Table    = orm.Table("ad_creative_conf")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// CreativeId 创意ID
	CreativeId = property.Property("creative_id")
	// CreativeName 创意名
	CreativeName = property.Property("creative_name")
	// CampaignId 广告组ID
	CampaignId = property.Property("campaign_id")
	// PlanId 计划ID
	PlanId = property.Property("plan_id")
	// PackageId 包号
	PackageId = property.Property("package_id")
	// GameId 游戏前端ID
	GameId = property.Property("game_id")
	// PlatformId 客户端ID
	PlatformId = property.Property("platform_id")
	// CountryId 国家ID
	CountryId = property.Property("country_id")
	// UpdateTime 更新时间
	UpdateTime = property.Property("update_time")
	// Ext creative_id原始日志
	Ext = property.Property("ext")
)

var FieldsAll = Fields(Id, CreativeId, CreativeName, CampaignId, PlanId, PackageId, GameId, PlatformId, CountryId, UpdateTime, Ext)
var NonePrimaryFields = Fields(CreativeId, CreativeName, CampaignId, PlanId, PackageId, GameId, PlatformId, CountryId, UpdateTime, Ext)
var NoneAutoIncrementFields = Fields(CreativeId, CreativeName, CampaignId, PlanId, PackageId, GameId, PlatformId, CountryId, UpdateTime, Ext)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdCreativeConfModel 广告创意配置表
type AdCreativeConfModel struct {
	// Id 自增id
	Id uint32 `orm:"id" json:"id"`

	// CreativeId 创意ID
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// CreativeName 创意名
	CreativeName string `orm:"creative_name" json:"creative_name"`

	// CampaignId 广告组ID
	CampaignId string `orm:"campaign_id" json:"campaign_id"`

	// PlanId 计划ID
	PlanId string `orm:"plan_id" json:"plan_id"`

	// PackageId 包号
	PackageId int `orm:"package_id" json:"package_id"`

	// GameId 游戏前端ID
	GameId int `orm:"game_id" json:"game_id"`

	// PlatformId 客户端ID
	PlatformId int `orm:"platform_id" json:"platform_id"`

	// CountryId 国家ID
	CountryId int `orm:"country_id" json:"country_id"`

	// UpdateTime 更新时间
	UpdateTime CustomTime `orm:"update_time" json:"update_time"`

	// Ext creative_id原始日志
	Ext string `orm:"ext" json:"ext"`
}

type PagedResult struct {
	Records      []*AdCreativeConfModel `json:"list"`
	PageNum      int                    `json:"page"`
	PageSize     int                    `json:"page_size"`
	TotalPages   int                    `json:"total_pages"`
	TotalRecords int                    `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:           1,
	CreativeId:   2,
	CreativeName: 3,
	CampaignId:   4,
	PlanId:       5,
	PackageId:    6,
	GameId:       7,
	PlatformId:   8,
	CountryId:    9,
	UpdateTime:   10,
	Ext:          11,
}

func (m *AdCreativeConfModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdCreativeConfModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.CreativeId, m.CreativeName, m.CampaignId, m.PlanId, m.PackageId, m.GameId, m.PlatformId, m.CountryId, m.UpdateTime, m.Ext)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = uint32(lastInsertId)

	return nil

}

func (m *AdCreativeConfModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CreativeId, m.CreativeName, m.CampaignId, m.PlanId, m.PackageId, m.GameId, m.PlatformId, m.CountryId, m.UpdateTime, m.Ext)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *AdCreativeConfModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdCreativeConfModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CreativeId, m.CreativeName, m.CampaignId, m.PlanId, m.PackageId, m.GameId, m.PlatformId, m.CountryId, m.UpdateTime, m.Ext)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCreativeConfModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdCreativeConfModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CreativeId, m.CreativeName, m.CampaignId, m.PlanId, m.PackageId, m.GameId, m.PlatformId, m.CountryId, m.UpdateTime, m.Ext), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCreativeConfModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdCreativeConfModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.CreativeId, m.CreativeName, m.CampaignId, m.PlanId, m.PackageId, m.GameId, m.PlatformId, m.CountryId, m.UpdateTime, m.Ext), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CreativeId, m.CreativeName, m.CampaignId, m.PlanId, m.PackageId, m.GameId, m.PlatformId, m.CountryId, m.UpdateTime, m.Ext))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCreativeConfModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdCreativeConfModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdCreativeConfModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdCreativeConfModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdCreativeConfModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdCreativeConfModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdCreativeConfModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdCreativeConfModel, error) {

	modelList := make([]*AdCreativeConfModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCreativeConfModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CreativeId, &m.CreativeName, &m.CampaignId, &m.PlanId, &m.PackageId, &m.GameId, &m.PlatformId, &m.CountryId, &m.UpdateTime, &m.Ext)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint32) (*AdCreativeConfModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint32) (*AdCreativeConfModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint32) (*AdCreativeConfModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint32) (*AdCreativeConfModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdCreativeConfModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdCreativeConfModel, error) {

	m := &AdCreativeConfModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.CreativeId, &m.CreativeName, &m.CampaignId, &m.PlanId, &m.PackageId, &m.GameId, &m.PlatformId, &m.CountryId, &m.UpdateTime, &m.Ext)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdCreativeConfModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdCreativeConfModel, error) {

	modelList := make([]*AdCreativeConfModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCreativeConfModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CreativeId, &m.CreativeName, &m.CampaignId, &m.PlanId, &m.PackageId, &m.GameId, &m.PlatformId, &m.CountryId, &m.UpdateTime, &m.Ext)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdCreativeConfModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdCreativeConfModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdCreativeConfModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CreativeId, &m.CreativeName, &m.CampaignId, &m.PlanId, &m.PackageId, &m.GameId, &m.PlatformId, &m.CountryId, &m.UpdateTime, &m.Ext)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint32) (*AdCreativeConfModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint32) (*AdCreativeConfModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdCreativeConfModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdCreativeConfModel, error) {

	m := &AdCreativeConfModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdCreativeConfModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdCreativeConfModel, error) {

	modelList := make([]*AdCreativeConfModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdCreativeConfModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdCreativeConfModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdCreativeConfModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdCreativeConfModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdCreativeConfModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.CreativeId, d.CreativeName, d.CampaignId, d.PlanId, d.PackageId, d.GameId, d.PlatformId, d.CountryId, d.UpdateTime, d.Ext))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID",
		},
		{
			"dataIndex": "creative_name",
			"title":     "创意名",
		},
		{
			"dataIndex": "campaign_id",
			"title":     "广告组ID",
		},
		{
			"dataIndex": "plan_id",
			"title":     "计划ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏前端ID",
		},
		{
			"dataIndex": "platform_id",
			"title":     "客户端ID",
		},
		{
			"dataIndex": "country_id",
			"title":     "国家ID",
		},
		{
			"dataIndex": "update_time",
			"title":     "更新时间",
		},
		{
			"dataIndex": "ext",
			"title":     "creative_id原始日志",
		}}
}
