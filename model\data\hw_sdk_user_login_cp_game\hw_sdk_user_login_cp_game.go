package hw_sdk_user_login_cp_game

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("hw_sdk_user_login_cp_game")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增ID
	Id = property.Property("id")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏ID
	GameId = property.Property("game_id")
	// LoginAccount 登录账号
	LoginAccount = property.Property("login_account")
	// CoreAccount 用户ID(核心账号)
	CoreAccount = property.Property("core_account")
	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv = property.Property("aid_or_idfv")
	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa = property.Property("gid_or_idfa")
	// TimeLocal 行为发生本地时间
	TimeLocal = property.Property("time_local")
	// TimeBj 行为发生时间
	TimeBj = property.Property("time_bj")
	// TimeServer 上报到服务器时间
	TimeServer = property.Property("time_server")
	// DeviceCode md5(MAC+IMEI+IMSI)
	DeviceCode = property.Property("device_code")
	// Useragent 一个特殊字符串头,识别客户信息
	Useragent = property.Property("useragent")
	// DeviceType 设备机型
	DeviceType = property.Property("device_type")
	// Os 操作系统类型:1-android,2-iOS,3-WINPHONE
	Os = property.Property("os")
	// OsVersion 系统版本
	OsVersion = property.Property("os_version")
	// SdkVersion SDK版本号
	SdkVersion = property.Property("sdk_version")
	// GameVersion 游戏版本号
	GameVersion = property.Property("game_version")
	// NetworkType 网络环境:0-没有网络,1-WIFI,2-2G,3-3G,4-WAP,5-Other
	NetworkType = property.Property("network_type")
	// MobileType 移动网络类型:1-移动,2-联通,3-电信,4-其它
	MobileType = property.Property("mobile_type")
	// ScreenWidth 屏幕分辨率:宽
	ScreenWidth = property.Property("screen_width")
	// ScreenHeight 屏幕分辨率:高
	ScreenHeight = property.Property("screen_height")
	// Ip ip地址:原生IP码
	Ip = property.Property("ip")
	// Country 投放国家(地区)
	Country = property.Property("country")
	// Lbs 经纬度:经度x纬度
	Lbs = property.Property("lbs")
	// DeviceLanguage 设备语言
	DeviceLanguage = property.Property("device_language")
	// PackageId 游戏包ID
	PackageId = property.Property("package_id")
	// Mac MAC地址
	Mac = property.Property("mac")
	// ClickId 对应device_active_source表的ID
	ClickId = property.Property("click_id")
	// SourceId 媒体ID
	SourceId = property.Property("source_id")
	// CreativeId 创意ID
	CreativeId = property.Property("creative_id")
	// AdjustId adjust_id
	AdjustId = property.Property("adjust_id")
	// LoginType 登录方式 1MG账号 2.游客 3.facebook 4.google 5,苹果登录 6,联运渠道 7.line 8.twitter 9.邮箱登录 10 yahoo 11引继码 12 steam
	LoginType = property.Property("login_type")
)

var FieldsAll = Fields(Id, CpGameId, GameId, LoginAccount, CoreAccount, AidOrIdfv, GidOrIdfa, TimeLocal, TimeBj, TimeServer, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, ScreenWidth, ScreenHeight, Ip, Country, Lbs, DeviceLanguage, PackageId, Mac, ClickId, SourceId, CreativeId, AdjustId, LoginType)
var NonePrimaryFields = Fields(CpGameId, GameId, LoginAccount, CoreAccount, AidOrIdfv, GidOrIdfa, TimeLocal, TimeBj, TimeServer, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, ScreenWidth, ScreenHeight, Ip, Country, Lbs, DeviceLanguage, PackageId, Mac, ClickId, SourceId, CreativeId, AdjustId, LoginType)
var NoneAutoIncrementFields = Fields(CpGameId, GameId, LoginAccount, CoreAccount, AidOrIdfv, GidOrIdfa, TimeLocal, TimeBj, TimeServer, DeviceCode, Useragent, DeviceType, Os, OsVersion, SdkVersion, GameVersion, NetworkType, MobileType, ScreenWidth, ScreenHeight, Ip, Country, Lbs, DeviceLanguage, PackageId, Mac, ClickId, SourceId, CreativeId, AdjustId, LoginType)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// HwSdkUserLoginCpGameModel 海外_用户首登日志(游戏外_cp_game_id)
type HwSdkUserLoginCpGameModel struct {
	// Id 自增ID
	Id uint32 `orm:"id" json:"id"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏ID
	GameId int `orm:"game_id" json:"game_id"`

	// LoginAccount 登录账号
	LoginAccount string `orm:"login_account" json:"login_account"`

	// CoreAccount 用户ID(核心账号)
	CoreAccount string `orm:"core_account" json:"core_account"`

	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv string `orm:"aid_or_idfv" json:"aid_or_idfv"`

	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa string `orm:"gid_or_idfa" json:"gid_or_idfa"`

	// TimeLocal 行为发生本地时间
	TimeLocal CustomTime `orm:"time_local" json:"time_local"`

	// TimeBj 行为发生时间
	TimeBj CustomTime `orm:"time_bj" json:"time_bj"`

	// TimeServer 上报到服务器时间
	TimeServer CustomTime `orm:"time_server" json:"time_server"`

	// DeviceCode md5(MAC+IMEI+IMSI)
	DeviceCode string `orm:"device_code" json:"device_code"`

	// Useragent 一个特殊字符串头,识别客户信息
	Useragent string `orm:"useragent" json:"useragent"`

	// DeviceType 设备机型
	DeviceType string `orm:"device_type" json:"device_type"`

	// Os 操作系统类型:1-android,2-iOS,3-WINPHONE
	Os string `orm:"os" json:"os"`

	// OsVersion 系统版本
	OsVersion string `orm:"os_version" json:"os_version"`

	// SdkVersion SDK版本号
	SdkVersion string `orm:"sdk_version" json:"sdk_version"`

	// GameVersion 游戏版本号
	GameVersion string `orm:"game_version" json:"game_version"`

	// NetworkType 网络环境:0-没有网络,1-WIFI,2-2G,3-3G,4-WAP,5-Other
	NetworkType string `orm:"network_type" json:"network_type"`

	// MobileType 移动网络类型:1-移动,2-联通,3-电信,4-其它
	MobileType string `orm:"mobile_type" json:"mobile_type"`

	// ScreenWidth 屏幕分辨率:宽
	ScreenWidth string `orm:"screen_width" json:"screen_width"`

	// ScreenHeight 屏幕分辨率:高
	ScreenHeight string `orm:"screen_height" json:"screen_height"`

	// Ip ip地址:原生IP码
	Ip string `orm:"ip" json:"ip"`

	// Country 投放国家(地区)
	Country string `orm:"country" json:"country"`

	// Lbs 经纬度:经度x纬度
	Lbs string `orm:"lbs" json:"lbs"`

	// DeviceLanguage 设备语言
	DeviceLanguage string `orm:"device_language" json:"device_language"`

	// PackageId 游戏包ID
	PackageId int `orm:"package_id" json:"package_id"`

	// Mac MAC地址
	Mac string `orm:"mac" json:"mac"`

	// ClickId 对应device_active_source表的ID
	ClickId uint32 `orm:"click_id" json:"click_id"`

	// SourceId 媒体ID
	SourceId uint32 `orm:"source_id" json:"source_id"`

	// CreativeId 创意ID
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// AdjustId adjust_id
	AdjustId string `orm:"adjust_id" json:"adjust_id"`

	// LoginType 登录方式 1MG账号 2.游客 3.facebook 4.google 5,苹果登录 6,联运渠道 7.line 8.twitter 9.邮箱登录 10 yahoo 11引继码 12 steam
	LoginType string `orm:"login_type" json:"login_type"`
}

type PagedResult struct {
	Records      []*HwSdkUserLoginCpGameModel `json:"list"`
	PageNum      int                          `json:"page"`
	PageSize     int                          `json:"page_size"`
	TotalPages   int                          `json:"total_pages"`
	TotalRecords int                          `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:             1,
	CpGameId:       2,
	GameId:         3,
	LoginAccount:   4,
	CoreAccount:    5,
	AidOrIdfv:      6,
	GidOrIdfa:      7,
	TimeLocal:      8,
	TimeBj:         9,
	TimeServer:     10,
	DeviceCode:     11,
	Useragent:      12,
	DeviceType:     13,
	Os:             14,
	OsVersion:      15,
	SdkVersion:     16,
	GameVersion:    17,
	NetworkType:    18,
	MobileType:     19,
	ScreenWidth:    20,
	ScreenHeight:   21,
	Ip:             22,
	Country:        23,
	Lbs:            24,
	DeviceLanguage: 25,
	PackageId:      26,
	Mac:            27,
	ClickId:        28,
	SourceId:       29,
	CreativeId:     30,
	AdjustId:       31,
	LoginType:      32,
}

func (m *HwSdkUserLoginCpGameModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *HwSdkUserLoginCpGameModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.CpGameId, m.GameId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.TimeLocal, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.PackageId, m.Mac, m.ClickId, m.SourceId, m.CreativeId, m.AdjustId, m.LoginType)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = uint32(lastInsertId)

	return nil

}

func (m *HwSdkUserLoginCpGameModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.TimeLocal, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.PackageId, m.Mac, m.ClickId, m.SourceId, m.CreativeId, m.AdjustId, m.LoginType)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *HwSdkUserLoginCpGameModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *HwSdkUserLoginCpGameModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.TimeLocal, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.PackageId, m.Mac, m.ClickId, m.SourceId, m.CreativeId, m.AdjustId, m.LoginType)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkUserLoginCpGameModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *HwSdkUserLoginCpGameModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.TimeLocal, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.PackageId, m.Mac, m.ClickId, m.SourceId, m.CreativeId, m.AdjustId, m.LoginType), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkUserLoginCpGameModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *HwSdkUserLoginCpGameModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.CpGameId, m.GameId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.TimeLocal, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.PackageId, m.Mac, m.ClickId, m.SourceId, m.CreativeId, m.AdjustId, m.LoginType), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.TimeLocal, m.TimeBj, m.TimeServer, m.DeviceCode, m.Useragent, m.DeviceType, m.Os, m.OsVersion, m.SdkVersion, m.GameVersion, m.NetworkType, m.MobileType, m.ScreenWidth, m.ScreenHeight, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.PackageId, m.Mac, m.ClickId, m.SourceId, m.CreativeId, m.AdjustId, m.LoginType))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkUserLoginCpGameModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *HwSdkUserLoginCpGameModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *HwSdkUserLoginCpGameModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *HwSdkUserLoginCpGameModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *HwSdkUserLoginCpGameModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *HwSdkUserLoginCpGameModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*HwSdkUserLoginCpGameModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*HwSdkUserLoginCpGameModel, error) {

	modelList := make([]*HwSdkUserLoginCpGameModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkUserLoginCpGameModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.TimeLocal, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.PackageId, &m.Mac, &m.ClickId, &m.SourceId, &m.CreativeId, &m.AdjustId, &m.LoginType)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint32) (*HwSdkUserLoginCpGameModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint32) (*HwSdkUserLoginCpGameModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint32) (*HwSdkUserLoginCpGameModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint32) (*HwSdkUserLoginCpGameModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*HwSdkUserLoginCpGameModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*HwSdkUserLoginCpGameModel, error) {

	m := &HwSdkUserLoginCpGameModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.TimeLocal, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.PackageId, &m.Mac, &m.ClickId, &m.SourceId, &m.CreativeId, &m.AdjustId, &m.LoginType)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*HwSdkUserLoginCpGameModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkUserLoginCpGameModel, error) {

	modelList := make([]*HwSdkUserLoginCpGameModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkUserLoginCpGameModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.TimeLocal, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.PackageId, &m.Mac, &m.ClickId, &m.SourceId, &m.CreativeId, &m.AdjustId, &m.LoginType)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*HwSdkUserLoginCpGameModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*HwSdkUserLoginCpGameModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &HwSdkUserLoginCpGameModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.TimeLocal, &m.TimeBj, &m.TimeServer, &m.DeviceCode, &m.Useragent, &m.DeviceType, &m.Os, &m.OsVersion, &m.SdkVersion, &m.GameVersion, &m.NetworkType, &m.MobileType, &m.ScreenWidth, &m.ScreenHeight, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.PackageId, &m.Mac, &m.ClickId, &m.SourceId, &m.CreativeId, &m.AdjustId, &m.LoginType)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint32) (*HwSdkUserLoginCpGameModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint32) (*HwSdkUserLoginCpGameModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*HwSdkUserLoginCpGameModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*HwSdkUserLoginCpGameModel, error) {

	m := &HwSdkUserLoginCpGameModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*HwSdkUserLoginCpGameModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkUserLoginCpGameModel, error) {

	modelList := make([]*HwSdkUserLoginCpGameModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &HwSdkUserLoginCpGameModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*HwSdkUserLoginCpGameModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*HwSdkUserLoginCpGameModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*HwSdkUserLoginCpGameModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*HwSdkUserLoginCpGameModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.CpGameId, d.GameId, d.LoginAccount, d.CoreAccount, d.AidOrIdfv, d.GidOrIdfa, d.TimeLocal, d.TimeBj, d.TimeServer, d.DeviceCode, d.Useragent, d.DeviceType, d.Os, d.OsVersion, d.SdkVersion, d.GameVersion, d.NetworkType, d.MobileType, d.ScreenWidth, d.ScreenHeight, d.Ip, d.Country, d.Lbs, d.DeviceLanguage, d.PackageId, d.Mac, d.ClickId, d.SourceId, d.CreativeId, d.AdjustId, d.LoginType))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增ID",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏ID",
		},
		{
			"dataIndex": "login_account",
			"title":     "登录账号",
		},
		{
			"dataIndex": "core_account",
			"title":     "用户ID(核心账号)",
		},
		{
			"dataIndex": "aid_or_idfv",
			"title":     "安卓ID or idfv",
		},
		{
			"dataIndex": "gid_or_idfa",
			"title":     "谷歌ID or 苹果ID",
		},
		{
			"dataIndex": "time_local",
			"title":     "行为发生本地时间",
		},
		{
			"dataIndex": "time_bj",
			"title":     "行为发生时间",
		},
		{
			"dataIndex": "time_server",
			"title":     "上报到服务器时间",
		},
		{
			"dataIndex": "device_code",
			"title":     "md5(MAC+IMEI+IMSI)",
		},
		{
			"dataIndex": "useragent",
			"title":     "一个特殊字符串头,识别客户信息",
		},
		{
			"dataIndex": "device_type",
			"title":     "设备机型",
		},
		{
			"dataIndex": "os",
			"title":     "操作系统类型:1-android,2-iOS,3-WINPHONE",
		},
		{
			"dataIndex": "os_version",
			"title":     "系统版本",
		},
		{
			"dataIndex": "sdk_version",
			"title":     "SDK版本号",
		},
		{
			"dataIndex": "game_version",
			"title":     "游戏版本号",
		},
		{
			"dataIndex": "network_type",
			"title":     "网络环境:0-没有网络,1-WIFI,2-2G,3-3G,4-WAP,5-Other",
		},
		{
			"dataIndex": "mobile_type",
			"title":     "移动网络类型:1-移动,2-联通,3-电信,4-其它",
		},
		{
			"dataIndex": "screen_width",
			"title":     "屏幕分辨率:宽",
		},
		{
			"dataIndex": "screen_height",
			"title":     "屏幕分辨率:高",
		},
		{
			"dataIndex": "ip",
			"title":     "ip地址:原生IP码",
		},
		{
			"dataIndex": "country",
			"title":     "投放国家(地区)",
		},
		{
			"dataIndex": "lbs",
			"title":     "经纬度:经度x纬度",
		},
		{
			"dataIndex": "device_language",
			"title":     "设备语言",
		},
		{
			"dataIndex": "package_id",
			"title":     "游戏包ID",
		},
		{
			"dataIndex": "mac",
			"title":     "MAC地址",
		},
		{
			"dataIndex": "click_id",
			"title":     "对应device_active_source表的ID",
		},
		{
			"dataIndex": "source_id",
			"title":     "媒体ID",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID",
		},
		{
			"dataIndex": "adjust_id",
			"title":     "adjust_id",
		},
		{
			"dataIndex": "login_type",
			"title":     "登录方式 1MG账号 2.游客 3.facebook 4.google 5,苹果登录 6,联运渠道 7.line 8.twitter 9.邮箱登录 10 yahoo 11引继码 12 steam",
		}}
}
