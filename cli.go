package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/provider"
	_ "embed"
	"fmt"
	"github.com/joho/godotenv"
	"iaa_data/cmd"
	"log"
	"os"
	"runtime"
	"strings"
)

//go:embed config/config.yaml
var configMq string

func main() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	// 加载配置文件
	godotenv.Load()
	configStr := configMq

	_, err := plus.LoadServerConfigDefault(configStr)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}
	provider.Register()

	handler := cmd.NewHandler()
	// 手动检查操作类型
	if os.Args[1] != "-op" {
		fmt.Println("必须使用 -op 指定操作类型")
		os.Exit(1)
	}
	// 检查参数
	if len(os.Args) < 3 {
		fmt.Println("使用方法: -op <command> [options]")
		fmt.Println(fmt.Sprintf(
			"操作类型：[%s]",
			strings.Join(handler.GetAvailableCommands(), "/"),
		))
		os.Exit(1)
	}

	operation := os.Args[2]

	// 执行命令，传递剩余的参数
	if err := handler.Execute(operation, os.Args[3:]); err != nil {

		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("[%s:%d] %s\n", file, line, fmt.Sprintf("%+v", err))

		os.Exit(1)
	}
}
