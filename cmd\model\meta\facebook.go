package meta

type FacebookResponse struct {
	Data   []AdData `json:"data"`
	Paging Paging   `json:"paging"`
}

type AdData struct {
	AdID         string           `json:"ad_id"`
	AdName       string           `json:"ad_name"`
	AdsetID      string           `json:"adset_id"`
	AdsetName    string           `json:"adset_name"`
	CampaignID   string           `json:"campaign_id"`
	CampaignName string           `json:"campaign_name"`
	Clicks       string           `json:"clicks"`
	Country      string           `json:"country"`
	DateStart    string           `json:"date_start"`
	DateStop     string           `json:"date_stop"`
	Impressions  string           `json:"impressions"`
	Spend        string           `json:"spend"`
	Ctr          string           `json:"ctr"`
	Cpm          string           `json:"cpm"`
	Actions      []map[string]any `json:"actions"`
	Hourly       string           `json:"hourly_stats_aggregated_by_advertiser_time_zone"`
}

type Paging struct {
	Cursors Cursors `json:"cursors"`
	Next    string  `json:"next"`
}

type Cursors struct {
	After  string `json:"after"`
	Before string `json:"before"`
}
