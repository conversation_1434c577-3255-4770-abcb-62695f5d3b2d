package handlers

import (
	"context"
	amqp "github.com/rabbitmq/amqp091-go"
)

type Handler interface {
	Callback(amqp.Delivery, context.Context) error
}

type CkafkaHandlers struct {
	handlers map[string]Handler
}

func NewCkafkaHandlers() *CkafkaHandlers {
	h := &CkafkaHandlers{
		handlers: make(map[string]Handler),
	}
	h.RegisterHandlers()
	return h
}

func (this *CkafkaHandlers) RegisterHandlers() map[string]Handler {
	this.handlers["go_iaa_payment"] = &GoIaaPaymentHandler{}

	return this.handlers
}
