package provider

import (
	"iaa_data/provider/ckafka_mq"
	logger "log"

	"910.com/plus2.git/contract"
	"910.com/plus2.git/plus"
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/provider/cache/mem"
	"910.com/plus2.git/provider/cache/redis"
	"910.com/plus2.git/provider/crontab"
	"910.com/plus2.git/provider/db/mysql"
	"910.com/plus2.git/provider/invocation"
	"910.com/plus2.git/provider/log"
	"910.com/plus2.git/provider/worker"
)

// Register 注册服务提供者
func Register() {
	container := plus.AppContainer
	container.Register(&log.ServiceProvider{})
	container.Register(&invocation.ServiceProvider{})
	container.Register(&crontab.ServiceProvider{})
	container.Register(&worker.ServiceProvider{})

	// 从配置文件中读取日志配置
	logDir := plus.AppConfig.LogDir
	isStdOut := true
	if len(logDir) > 0 {
		isStdOut = false // 如果日志目录不为空，则不将日志输出到标准输出
	}
	obj, _ := container.Make(contract.LogKey, "default")
	obj.(contract.Log).SetConfig(logDir, plus.AppConfig.LogFormat, isStdOut)
	logConfig := plus.AppConfig.Get("log")
	if logConfig != nil {
		// 遍历 log 配置
		for id, v := range logConfig.(map[string]interface{}) {
			v := v.(map[string]interface{})
			container.Register(&log.ServiceProvider{Id: id})
			c, err := container.Make(contract.LogKey, id)
			if err != nil {
				logger.Fatal("log实例获取失败", err)
				return
			}
			isStdOut2, ok := v["isStdout"].(bool)
			if !ok {
				isStdOut2 = false
			}
			c.(contract.Log).SetConfig(v["log_dir"].(string), v["log_format"].(string), isStdOut2)
		}
	}

	cacheConfig := plus.AppConfig.Get("cache")
	if cacheConfig != nil {
		// 遍历 cache 配置
		for id, v := range cacheConfig.(map[string]interface{}) {
			v := v.(map[string]interface{})
			if v["type"].(string) == "mem" {
				container.Register(&mem.ServiceProvider{Id: id})
				_, err := container.Make(contract.CacheKey, id)
				if err != nil {
					logger.Fatal("cache实例获取失败", err)
				}
			}
			if v["type"].(string) == "redis" {
				r := &redis.ServiceProvider{Id: id}
				container.Register(r)
				c, _ := container.Make(contract.CacheKey, id)
				c.(contract.Cache).SetConfig(v)
			}
		}
	}

	dbConfig := plus.AppConfig.Get("db")
	if dbConfig != nil {
		// 遍历 db 配置
		for id, v := range dbConfig.(map[string]interface{}) {
			v := v.(map[string]interface{})
			container.Register(&mysql.ServiceProvider{Id: id})
			c, _ := container.Make(contract.DbKey, id)
			err := c.(contract.Db).SetConfig(id, v)
			if err != nil {
				plusQ.Logger().Alert("mysql", err)
				logger.Fatal("mysql", err)
			}
		}
	}

	mqConfig := plus.AppConfig.Get("mq")
	if mqConfig != nil {
		// 遍历 mq 配置
		for id, v := range mqConfig.(map[string]interface{}) {
			v := v.(map[string]interface{})
			var c interface{}
			var err error
			if v["type"].(string) == "ckafka" {
				container.Register(&ckafka_mq.ServiceProvider{Id: id})
				c, err = container.Make(contract.MQKey+"ckafka", id)
				if err != nil {
					logger.Fatal("ckafka", err)
				}
			}
			err = c.(contract.MQ).SetConfig(v)
			if err != nil {
				logger.Fatal(err)
			}
		}
	}
}
