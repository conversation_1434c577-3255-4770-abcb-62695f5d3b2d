package ckafka_mq

import (
	c "910.com/plus2.git/contract"
	p "910.com/plus2.git/plus"
)

type ServiceProvider struct {
	Id string
}

func (s *ServiceProvider) Register(container p.Container) p.NewInstance {
	return func(...interface{}) (interface{}, error) {
		return NewCKafkaMq(), nil
	}
}

func (s *ServiceProvider) Boot(p.Container) error {
	return nil
}

func (s *ServiceProvider) IsDefer() bool {
	return true
}

func (s *ServiceProvider) Params(p.Container) []interface{} {
	return nil
}

func (s *ServiceProvider) Name() string {
	return c.<PERSON> + "ckafka"
}

func (s *ServiceProvider) ID() string {
	if s.Id != "" {
		return s.Id
	}
	return "default"
}
