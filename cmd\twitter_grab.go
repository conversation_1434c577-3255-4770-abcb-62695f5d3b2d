package cmd

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/cmd/model/twitter"
	"iaa_data/model/data/ad_account_conf"
	"iaa_data/utils"
	"time"
)

type TwitterGrab struct {
}

const TT_CHANNEL_ID = 3

func (this *TwitterGrab) Run(params map[string]string) error {
	utils.Debug("命令行参数", params)

	var ok bool
	var startDate string
	var endDate string
	startDate, ok = params["start_date"]
	if !ok {
		startDate = time.Now().AddDate(0, 0, -3).Format(time.DateOnly)
	}
	endDate, ok = params["end_date"]
	if !ok {
		endDate = time.Now().Format(time.DateOnly)
	}

	accountId, ok := params["account_id"]
	if !ok {
		accountList, _ := ad_account_conf.ListBy(ad_account_conf.ChannelId.Eq(TT_CHANNEL_ID).And(ad_account_conf.Status.Eq(1)))
		for _, accountInfo := range accountList {
			utils.Debug("开始抓取", accountInfo.AdAccount)

			api := twitter.NewTwitterApi(accountId)

			data, err := api.ProcessTwitterAdsData(startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("twitter_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
			_ = this.SaveData(data)
		}
	} else {
		accountInfo, _ := ad_account_conf.GetBy(ad_account_conf.ChannelId.Eq(TT_CHANNEL_ID).And(ad_account_conf.AccountId.Eq(accountId)).And(ad_account_conf.Status.Eq(1)))
		if accountInfo != nil {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			api := twitter.NewTwitterApi(accountId)
			data, err := api.ProcessTwitterAdsData(startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("twitter_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
			_ = this.SaveData(data)
		}
	}

	return nil
}

func (this *TwitterGrab) SaveData(data []twitter.ResultRecord) error {
	// 入库处理，没数据，暂时不写
	return nil
}
