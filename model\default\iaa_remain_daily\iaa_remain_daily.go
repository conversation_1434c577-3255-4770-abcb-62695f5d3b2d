package iaa_remain_daily

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("default")
	Table    = orm.Table("iaa_remain_daily")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 主键
	Id = property.Property("id")
	// Tday 日期
	Tday = property.Property("tday")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏ID
	GameId = property.Property("game_id")
	// CountryId 国家ID
	CountryId = property.Property("country_id")
	// SourceId 媒体ID
	SourceId = property.Property("source_id")
	// PackageId 包号ID
	PackageId = property.Property("package_id")
	// CreativeId 创意ID
	CreativeId = property.Property("creative_id")
	// LoginNum 登录人数
	LoginNum = property.Property("login_num")
	// DayType 留存标识
	DayType = property.Property("day_type")
	// SendDate 传输数据记录时间
	SendDate = property.Property("send_date")
	// UpdateTime 更新时间
	UpdateTime = property.Property("update_time")
	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone = property.Property("time_zone")
	// Platform 客户端android、ios
	Platform = property.Property("platform")
)

var FieldsAll = Fields(Id, Tday, CpGameId, GameId, CountryId, SourceId, PackageId, CreativeId, LoginNum, DayType, SendDate, UpdateTime, TimeZone, Platform)
var NonePrimaryFields = Fields(Tday, CpGameId, GameId, CountryId, SourceId, PackageId, CreativeId, LoginNum, DayType, SendDate, UpdateTime, TimeZone, Platform)
var NoneAutoIncrementFields = Fields(Tday, CpGameId, GameId, CountryId, SourceId, PackageId, CreativeId, LoginNum, DayType, SendDate, UpdateTime, TimeZone, Platform)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// IaaRemainDailyModel 留存表
type IaaRemainDailyModel struct {
	// Id 主键
	Id uint64 `orm:"id" json:"id"`

	// Tday 日期
	Tday int `orm:"tday" json:"tday"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏ID
	GameId int `orm:"game_id" json:"game_id"`

	// CountryId 国家ID
	CountryId int `orm:"country_id" json:"country_id"`

	// SourceId 媒体ID
	SourceId int `orm:"source_id" json:"source_id"`

	// PackageId 包号ID
	PackageId int `orm:"package_id" json:"package_id"`

	// CreativeId 创意ID
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// LoginNum 登录人数
	LoginNum int `orm:"login_num" json:"login_num"`

	// DayType 留存标识
	DayType int `orm:"day_type" json:"day_type"`

	// SendDate 传输数据记录时间
	SendDate CustomTime `orm:"send_date" json:"send_date"`

	// UpdateTime 更新时间
	UpdateTime CustomTime `orm:"update_time" json:"update_time"`

	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone int `orm:"time_zone" json:"time_zone"`

	// Platform 客户端android、ios
	Platform string `orm:"platform" json:"platform"`
}

type PagedResult struct {
	Records      []*IaaRemainDailyModel `json:"list"`
	PageNum      int                    `json:"page"`
	PageSize     int                    `json:"page_size"`
	TotalPages   int                    `json:"total_pages"`
	TotalRecords int                    `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:         1,
	Tday:       2,
	CpGameId:   3,
	GameId:     4,
	CountryId:  5,
	SourceId:   6,
	PackageId:  7,
	CreativeId: 8,
	LoginNum:   9,
	DayType:    10,
	SendDate:   11,
	UpdateTime: 12,
	TimeZone:   13,
	Platform:   14,
}

func (m *IaaRemainDailyModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *IaaRemainDailyModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.LoginNum, m.DayType, m.SendDate, m.UpdateTime, m.TimeZone, m.Platform)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaRemainDailyModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.LoginNum, m.DayType, m.SendDate, m.UpdateTime, m.TimeZone, m.Platform)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaRemainDailyModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *IaaRemainDailyModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.LoginNum, m.DayType, m.SendDate, m.UpdateTime, m.TimeZone, m.Platform)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaRemainDailyModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *IaaRemainDailyModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.LoginNum, m.DayType, m.SendDate, m.UpdateTime, m.TimeZone, m.Platform), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaRemainDailyModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *IaaRemainDailyModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.LoginNum, m.DayType, m.SendDate, m.UpdateTime, m.TimeZone, m.Platform), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.LoginNum, m.DayType, m.SendDate, m.UpdateTime, m.TimeZone, m.Platform))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaRemainDailyModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *IaaRemainDailyModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *IaaRemainDailyModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *IaaRemainDailyModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *IaaRemainDailyModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *IaaRemainDailyModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*IaaRemainDailyModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*IaaRemainDailyModel, error) {

	modelList := make([]*IaaRemainDailyModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaRemainDailyModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.LoginNum, &m.DayType, &m.SendDate, &m.UpdateTime, &m.TimeZone, &m.Platform)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint64) (*IaaRemainDailyModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint64) (*IaaRemainDailyModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint64) (*IaaRemainDailyModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint64) (*IaaRemainDailyModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*IaaRemainDailyModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*IaaRemainDailyModel, error) {

	m := &IaaRemainDailyModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.LoginNum, &m.DayType, &m.SendDate, &m.UpdateTime, &m.TimeZone, &m.Platform)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*IaaRemainDailyModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*IaaRemainDailyModel, error) {

	modelList := make([]*IaaRemainDailyModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaRemainDailyModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.LoginNum, &m.DayType, &m.SendDate, &m.UpdateTime, &m.TimeZone, &m.Platform)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*IaaRemainDailyModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*IaaRemainDailyModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &IaaRemainDailyModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.LoginNum, &m.DayType, &m.SendDate, &m.UpdateTime, &m.TimeZone, &m.Platform)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint64) (*IaaRemainDailyModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint64) (*IaaRemainDailyModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*IaaRemainDailyModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*IaaRemainDailyModel, error) {

	m := &IaaRemainDailyModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*IaaRemainDailyModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*IaaRemainDailyModel, error) {

	modelList := make([]*IaaRemainDailyModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &IaaRemainDailyModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*IaaRemainDailyModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*IaaRemainDailyModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*IaaRemainDailyModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*IaaRemainDailyModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.Tday, d.CpGameId, d.GameId, d.CountryId, d.SourceId, d.PackageId, d.CreativeId, d.LoginNum, d.DayType, d.SendDate, d.UpdateTime, d.TimeZone, d.Platform))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "主键",
		},
		{
			"dataIndex": "tday",
			"title":     "日期",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏ID",
		},
		{
			"dataIndex": "country_id",
			"title":     "国家ID",
		},
		{
			"dataIndex": "source_id",
			"title":     "媒体ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号ID",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID",
		},
		{
			"dataIndex": "login_num",
			"title":     "登录人数",
		},
		{
			"dataIndex": "day_type",
			"title":     "留存标识",
		},
		{
			"dataIndex": "send_date",
			"title":     "传输数据记录时间",
		},
		{
			"dataIndex": "update_time",
			"title":     "更新时间",
		},
		{
			"dataIndex": "time_zone",
			"title":     "时区: 1=北京，2=欧洲，3=美国",
		},
		{
			"dataIndex": "platform",
			"title":     "客户端android、ios",
		}}
}
