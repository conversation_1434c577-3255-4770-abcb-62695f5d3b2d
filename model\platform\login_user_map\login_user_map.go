package login_user_map

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("platform")
	Table    = orm.Table("login_user_map")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id ID
	Id = property.Property("id")
	// GameId game_id
	GameId = property.Property("game_id")
	// PackageId 包号
	PackageId = property.Property("package_id")
	// LoginAccount 登录帐号
	LoginAccount = property.Property("login_account")
	// CoreAccount 核心帐号
	CoreAccount = property.Property("core_account")
	// IdentityType 身份类型：guest, google, apple
	IdentityType = property.Property("identity_type")
	// IdentityId 身份唯一标识：设备ID或第三方用户ID
	IdentityId = property.Property("identity_id")
	// IdentityData 第三方平台相关数据
	IdentityData = property.Property("identity_data")
	// IsPrimary 是否为主要身份：0-否，1-是
	IsPrimary = property.Property("is_primary")
	// CreatedAt 添加时间
	CreatedAt = property.Property("created_at")
	// UpdatedAt 更新时间
	UpdatedAt = property.Property("updated_at")
	// State 状态：0-禁用，1-正常
	State = property.Property("state")
)

var FieldsAll = Fields(Id, GameId, PackageId, LoginAccount, CoreAccount, IdentityType, IdentityId, IdentityData, IsPrimary, CreatedAt, UpdatedAt, State)
var NonePrimaryFields = Fields(GameId, PackageId, LoginAccount, CoreAccount, IdentityType, IdentityId, IdentityData, IsPrimary, CreatedAt, UpdatedAt, State)
var NoneAutoIncrementFields = Fields(GameId, PackageId, LoginAccount, CoreAccount, IdentityType, IdentityId, IdentityData, IsPrimary, CreatedAt, UpdatedAt, State)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// LoginUserMapModel 用户身份关联表
type LoginUserMapModel struct {
	// Id ID
	Id uint64 `orm:"id" json:"id"`

	// GameId game_id
	GameId int `orm:"game_id" json:"game_id"`

	// PackageId 包号
	PackageId int `orm:"package_id" json:"package_id"`

	// LoginAccount 登录帐号
	LoginAccount string `orm:"login_account" json:"login_account"`

	// CoreAccount 核心帐号
	CoreAccount string `orm:"core_account" json:"core_account"`

	// IdentityType 身份类型：guest, google, apple
	IdentityType string `orm:"identity_type" json:"identity_type"`

	// IdentityId 身份唯一标识：设备ID或第三方用户ID
	IdentityId string `orm:"identity_id" json:"identity_id"`

	// IdentityData 第三方平台相关数据
	IdentityData *string `orm:"identity_data" json:"identity_data"`

	// IsPrimary 是否为主要身份：0-否，1-是
	IsPrimary int `orm:"is_primary" json:"is_primary"`

	// CreatedAt 添加时间
	CreatedAt CustomTime `orm:"created_at" json:"created_at"`

	// UpdatedAt 更新时间
	UpdatedAt CustomTime `orm:"updated_at" json:"updated_at"`

	// State 状态：0-禁用，1-正常
	State int `orm:"state" json:"state"`
}

type PagedResult struct {
	Records      []*LoginUserMapModel `json:"list"`
	PageNum      int                  `json:"page"`
	PageSize     int                  `json:"page_size"`
	TotalPages   int                  `json:"total_pages"`
	TotalRecords int                  `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:           1,
	GameId:       2,
	PackageId:    3,
	LoginAccount: 4,
	CoreAccount:  5,
	IdentityType: 6,
	IdentityId:   7,
	IdentityData: 8,
	IsPrimary:    9,
	CreatedAt:    10,
	UpdatedAt:    11,
	State:        12,
}

func (m *LoginUserMapModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *LoginUserMapModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.IdentityType, m.IdentityId, m.IdentityData, m.IsPrimary, m.CreatedAt, m.UpdatedAt, m.State)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *LoginUserMapModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.IdentityType, m.IdentityId, m.IdentityData, m.IsPrimary, m.CreatedAt, m.UpdatedAt, m.State)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *LoginUserMapModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *LoginUserMapModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.IdentityType, m.IdentityId, m.IdentityData, m.IsPrimary, m.CreatedAt, m.UpdatedAt, m.State)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *LoginUserMapModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *LoginUserMapModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.IdentityType, m.IdentityId, m.IdentityData, m.IsPrimary, m.CreatedAt, m.UpdatedAt, m.State), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *LoginUserMapModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *LoginUserMapModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.IdentityType, m.IdentityId, m.IdentityData, m.IsPrimary, m.CreatedAt, m.UpdatedAt, m.State), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.IdentityType, m.IdentityId, m.IdentityData, m.IsPrimary, m.CreatedAt, m.UpdatedAt, m.State))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *LoginUserMapModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *LoginUserMapModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *LoginUserMapModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *LoginUserMapModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *LoginUserMapModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *LoginUserMapModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*LoginUserMapModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*LoginUserMapModel, error) {

	modelList := make([]*LoginUserMapModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &LoginUserMapModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.IdentityType, &m.IdentityId, &m.IdentityData, &m.IsPrimary, &m.CreatedAt, &m.UpdatedAt, &m.State)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint64) (*LoginUserMapModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint64) (*LoginUserMapModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint64) (*LoginUserMapModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint64) (*LoginUserMapModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*LoginUserMapModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*LoginUserMapModel, error) {

	m := &LoginUserMapModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.IdentityType, &m.IdentityId, &m.IdentityData, &m.IsPrimary, &m.CreatedAt, &m.UpdatedAt, &m.State)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*LoginUserMapModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*LoginUserMapModel, error) {

	modelList := make([]*LoginUserMapModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &LoginUserMapModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.IdentityType, &m.IdentityId, &m.IdentityData, &m.IsPrimary, &m.CreatedAt, &m.UpdatedAt, &m.State)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*LoginUserMapModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*LoginUserMapModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &LoginUserMapModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.IdentityType, &m.IdentityId, &m.IdentityData, &m.IsPrimary, &m.CreatedAt, &m.UpdatedAt, &m.State)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint64) (*LoginUserMapModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint64) (*LoginUserMapModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*LoginUserMapModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*LoginUserMapModel, error) {

	m := &LoginUserMapModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*LoginUserMapModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*LoginUserMapModel, error) {

	modelList := make([]*LoginUserMapModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &LoginUserMapModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*LoginUserMapModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*LoginUserMapModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*LoginUserMapModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*LoginUserMapModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.GameId, d.PackageId, d.LoginAccount, d.CoreAccount, d.IdentityType, d.IdentityId, d.IdentityData, d.IsPrimary, d.CreatedAt, d.UpdatedAt, d.State))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "game_id",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号",
		},
		{
			"dataIndex": "login_account",
			"title":     "登录帐号",
		},
		{
			"dataIndex": "core_account",
			"title":     "核心帐号",
		},
		{
			"dataIndex": "identity_type",
			"title":     "身份类型：guest, google, apple",
		},
		{
			"dataIndex": "identity_id",
			"title":     "身份唯一标识：设备ID或第三方用户ID",
		},
		{
			"dataIndex": "identity_data",
			"title":     "第三方平台相关数据",
		},
		{
			"dataIndex": "is_primary",
			"title":     "是否为主要身份：0-否，1-是",
		},
		{
			"dataIndex": "created_at",
			"title":     "添加时间",
		},
		{
			"dataIndex": "updated_at",
			"title":     "更新时间",
		},
		{
			"dataIndex": "state",
			"title":     "状态：0-禁用，1-正常",
		}}
}
