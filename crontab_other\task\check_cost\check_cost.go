package check_cost

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils/root_iaa"
	"log"
	"strconv"
	"time"
)

func InitFunc(args ...string) {
	checkI(1)
}

func checkI(checkCount int) {
	if time.Now().Hour() == 14 || time.Now().Hour() == 15 || time.Now().Hour() == 8 {
		return
	}

	nowTime := time.Now().Add(-24 * time.Hour)
	startDate := nowTime.Add(-4 * 24 * time.Hour)

	errMsg := ""

	for currentDate := startDate; !currentDate.After(nowTime); currentDate = currentDate.AddDate(0, 0, 1) {
		timeVal := currentDate.Format("2006-01-02")
		sql1 := "select sum(cost) as num from iaa_data.ad_cost where" +
			" `time` = '" + timeVal + "' "

		sql2 := "select sum(cost_sun) as num from  iaa_ad_roi_realtime  where " +
			" tday=" + currentDate.Format("20060102") +
			" and time_zone =2 "

		log.Println(sql1)
		log.Println(sql2)

		info1, err1 := plusQ.Db("data").Get(sql1)
		info2, err2 := plusQ.Db().Get(sql2)
		if err1 == nil && err2 == nil {
			num1, errNum1 := strconv.ParseFloat(fmt.Sprint(info1["num"]), 64)
			num2, errNum2 := strconv.ParseFloat(fmt.Sprint(info2["num"]), 64)
			if errNum1 == nil && errNum2 == nil {
				if num1-num2 > 30 {
					if errMsg == "" {
						errMsg = "[ " + timeVal + " 明细表：" + fmt.Sprint(num1) + " 汇总表：" + fmt.Sprint(num2) + " ]"
					} else {
						errMsg = errMsg + "、" + "[ " + timeVal + " 明细表：" + fmt.Sprint(num1) + " 汇总表：" + fmt.Sprint(num2) + " ]"
					}
					log.Println("数量不相等  ", timeVal, num1, num2)
				} else {
					log.Println("数量相等  ", timeVal, num1, num2)
				}
			}
		} else {
			log.Println(err1, err2)
		}
	}

	if errMsg != "" {
		if checkCount == 1 {
			time.Sleep(3 * 60 * time.Second)
			checkI(2)
		} else {
			root_iaa.RobotInstance().Send("消耗数据异常：" + errMsg)
		}
	}
}
