listen_addr: 0.0.0.0:8072
log_dir: ./elk_log
console_log_file: ./log_common/call_back/demo_go.log # 控制台日志文件路径， daemon 守护进程模式下有效, 配置了才能启用守护进程模式
config:
    # 缓存配置
    cache:
        default:
            type: mem
        c0:
            type: redis
            host: 127.0.0.1:6379
            password: ''
            db: 0
    # 数据库配置
    sql_print: false # 是否打印sql语句
    db:
        default:
            host: hw-iaa-mysql.mgamestore.com
            port: 3306
            username: hw_iaa
            password: 1A7c3dYaIiWCVp
            database_name: iaa_statistics
            charset: utf8mb4
            max_open_conns: 15
            max_idle_conns: 5
            max_lifetime: 300
        data:
            host: hw-iaa-mysql.mgamestore.com
            port: 3306
            username: hw_iaa
            password: 1A7c3dYaIiWCVp
            database_name: iaa_data
            charset: utf8mb4
            max_open_conns: 15
            max_idle_conns: 5
            max_lifetime: 300
    # 数据验证器配置
    validator:
        # 有且只有一个实例
        default:
            # 是否启用
            enable: true
    # 路由设置
    route:

    # 导出配置权限key
    export:
        authKey: 910app