package twitter

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

type CampaignsResponse struct {
	Data       []Campaign `json:"data"`
	NextCursor string     `json:"next_cursor"`
}

type Campaign struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type LineItemsResponse struct {
	Data       []LineItem `json:"data"`
	NextCursor string     `json:"next_cursor"`
}

type LineItem struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	CampaignID string `json:"campaign_id"`
}

type PromotedTweetsResponse struct {
	Data       []PromotedTweet `json:"data"`
	NextCursor string          `json:"next_cursor"`
}

type PromotedTweet struct {
	ID         string `json:"id"`
	TweetID    string `json:"tweet_id"`
	LineItemID string `json:"line_item_id"`
}

type AnalyticsResponse struct {
	Data []AnalyticsData `json:"data"`
}

type AnalyticsData struct {
	ID      string          `json:"id"`
	ID_Data AnalyticsIDData `json:"id_data"`
	Metrics Metrics         `json:"metrics"`
}

type AnalyticsIDData struct {
	PromotedTweetID string `json:"promoted_tweet_id"`
}

type Metrics struct {
	Impressions int `json:"impressions"`
	Clicks      int `json:"clicks"`
	Conversions int `json:"conversions"`
}

type ResultRecord struct {
	CampaignID      string
	CampaignName    string
	LineItemID      string
	LineItemName    string
	PromotedTweetID string
	TweetText       string
	Impressions     int
	Clicks          int
	Conversions     int
}

const (
	MaxIdsPerRequest   = 20  // Maximum number of entity_ids per request for analytics API
	MaxCampaignsPerReq = 200 // Maximum number of campaign_ids per request
	MaxLineItemsPerReq = 200 // Maximum number of line_item_ids per request
)

type TwitterApi struct {
	Client *OAuthClient
}

func NewTwitterApi(accountId string) *TwitterApi {
	return &TwitterApi{
		Client: NewOAuthClient(accountId),
	}
}

func (c *TwitterApi) GetCampaigns() ([]Campaign, error) {
	var allCampaigns []Campaign
	cursor := ""
	client := c.Client

	for {
		params := map[string]string{
			"count": "1000",
		}
		if cursor != "" {
			params["cursor"] = cursor
		}
		data, err := client.Request("campaigns", params, "GET")
		if err != nil {
			return nil, err
		}
		var response CampaignsResponse
		if err := json.Unmarshal(data, &response); err != nil {
			return nil, err
		}
		allCampaigns = append(allCampaigns, response.Data...)
		if response.NextCursor == "" {
			break
		}
		cursor = response.NextCursor
	}
	return allCampaigns, nil
}
func (c *TwitterApi) GetLineItems(campaignIDs []string) ([]LineItem, error) {
	var allLineItems []LineItem
	cursor := ""
	client := c.Client

	for {
		params := map[string]string{
			"campaign_ids": strings.Join(campaignIDs, ","),
			"count":        "1000",
		}
		if cursor != "" {
			params["cursor"] = cursor
		}
		data, err := client.Request("line_items", params, "GET")
		if err != nil {
			return nil, err
		}
		var response LineItemsResponse
		if err := json.Unmarshal(data, &response); err != nil {
			return nil, err
		}
		allLineItems = append(allLineItems, response.Data...)
		if response.NextCursor == "" {
			break
		}
		cursor = response.NextCursor
	}

	return allLineItems, nil
}
func (c *TwitterApi) GetPromotedTweets(lineItemIDs []string) ([]PromotedTweet, error) {
	var allPromotedTweets []PromotedTweet
	cursor := ""
	client := c.Client

	for {
		params := map[string]string{
			"line_item_ids": strings.Join(lineItemIDs, ","),
			"count":         "1000",
		}
		if cursor != "" {
			params["cursor"] = cursor
		}
		data, err := client.Request("promoted_tweets", params, "GET")
		if err != nil {
			return nil, err
		}
		var response PromotedTweetsResponse
		if err := json.Unmarshal(data, &response); err != nil {
			return nil, err
		}
		allPromotedTweets = append(allPromotedTweets, response.Data...)
		if response.NextCursor == "" {
			break
		}
		cursor = response.NextCursor
	}

	return allPromotedTweets, nil
}
func (c *TwitterApi) GetAnalytics(startDate string, endDate string, promotedTweetIDs []string) ([]AnalyticsData, error) {
	client := NewOAuthClient("18ce55pu153")

	params := map[string]string{
		"entity":        "PROMOTED_TWEET",
		"entity_ids":    strings.Join(promotedTweetIDs, ","),
		"metric_groups": "BILLING,ENGAGEMENT,MOBILE_CONVERSION",
		"granularity":   "DAY",
		"count":         "1000",
		"placement":     "ALL_ON_TWITTER",
		"start_time":    startDate,
		"end_time":      endDate,
	}
	data, err := client.Request("stats", params, "GET")
	if err != nil {
		return nil, err
	}
	var response AnalyticsResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	return response.Data, nil
}

func (c *TwitterApi) ProcessTwitterAdsData(startDate string, endDate string) ([]ResultRecord, error) {
	var results []ResultRecord

	campaigns, err := c.GetCampaigns()
	if err != nil {
		return nil, fmt.Errorf("获取campaigns失败: %v", err)
	}

	fmt.Printf("总共 %d 个campaigns\n", len(campaigns))

	campaignMap := make(map[string]Campaign)
	campaignIDs := make([]string, len(campaigns))
	for i, campaign := range campaigns {
		campaignMap[campaign.ID] = campaign
		campaignIDs[i] = campaign.ID
	}

	// 按200个分组
	campaignBatches := chunkArray(campaignIDs, MaxCampaignsPerReq)

	var allLineItems []LineItem
	lineItemMap := make(map[string]LineItem)
	lineItemIDs := []string{}

	for batchIndex, campaignBatch := range campaignBatches {
		lineItems, err := c.GetLineItems(campaignBatch)
		if err != nil {
			fmt.Printf("获取分组 %d 的 line items 失败: %v\n", batchIndex+1, err)
			continue
		}

		fmt.Printf("获取分组 %d 的 line items 数量 %d 个\n", batchIndex+1, len(lineItems))
		allLineItems = append(allLineItems, lineItems...)

		for _, item := range lineItems {
			lineItemMap[item.ID] = item
			lineItemIDs = append(lineItemIDs, item.ID)
		}
	}

	fmt.Printf("总共 %d 个 line items\n", len(allLineItems))

	// 按200个分组
	lineItemBatches := chunkArray(lineItemIDs, MaxLineItemsPerReq)

	var allPromotedTweets []PromotedTweet
	for batchIndex, lineItemBatch := range lineItemBatches {
		promotedTweets, err := c.GetPromotedTweets(lineItemBatch)
		if err != nil {
			fmt.Printf("获取分组 %d 的 promoted tweets 失败: %v\n", batchIndex+1, err)
			continue
		}

		fmt.Printf("获取分组 %d 的 promoted tweets 数量 %d 个\n", batchIndex+1, len(promotedTweets))
		allPromotedTweets = append(allPromotedTweets, promotedTweets...)
	}

	fmt.Printf("总共 %d 个 promoted tweets\n", len(allPromotedTweets))

	promotedTweetIDs := make([]string, len(allPromotedTweets))
	for i, tweet := range allPromotedTweets {
		promotedTweetIDs[i] = tweet.ID
	}

	// 按20个分组
	promotedTweetBatches := chunkArray(promotedTweetIDs, MaxIdsPerRequest)

	analyticsMap := make(map[string]Metrics)
	for batchIndex, tweetBatch := range promotedTweetBatches {
		analyticsData, err := c.GetAnalytics(startDate, endDate, tweetBatch)
		if err != nil {
			fmt.Printf("获取分组 %d 的 analytics 失败: %v\n", batchIndex+1, err)
			continue
		}
		for _, data := range analyticsData {
			analyticsMap[data.ID_Data.PromotedTweetID] = data.Metrics
		}
		time.Sleep(1 * time.Second)
	}

	// 构建响应结果
	for _, tweet := range allPromotedTweets {
		lineItem, ok := lineItemMap[tweet.LineItemID]
		if !ok {
			fmt.Printf("找不到 %s 的 line item 对应数据 \n", tweet.LineItemID)
			continue
		}

		campaignID := lineItem.CampaignID
		campaign, ok := campaignMap[campaignID]
		if !ok {
			fmt.Printf("找不到 %s 的 campaign 对应数据 \n", campaignID)
			continue
		}

		// Get metrics for this tweet
		metrics, ok := analyticsMap[tweet.ID]
		if !ok {
			fmt.Printf("找不到 %s 的 metrics 数据 \n", tweet.ID)
			continue
		}

		record := ResultRecord{
			CampaignID:      campaignID,
			CampaignName:    campaign.Name,
			LineItemID:      lineItem.ID,
			LineItemName:    lineItem.Name,
			PromotedTweetID: tweet.ID,
			TweetText:       tweet.TweetID, // Actual tweet text would require additional API call
			Impressions:     metrics.Impressions,
			Clicks:          metrics.Clicks,
			Conversions:     metrics.Conversions,
		}

		results = append(results, record)
	}

	return results, nil
}

func chunkArray(items []string, chunkSize int) [][]string {
	var chunks [][]string
	for i := 0; i < len(items); i += chunkSize {
		end := i + chunkSize
		if end > len(items) {
			end = len(items)
		}
		chunks = append(chunks, items[i:end])
	}
	return chunks
}
