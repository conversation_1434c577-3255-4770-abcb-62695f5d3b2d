package max_ad

import (
	"iaa_data/model/data/ad_report_rewarded"
	"log"
)

type MaxAdController struct{}

func InitMaxAdController() *MaxAdController {
	return &MaxAdController{}
}

func (m *MaxAdController) Add(r *AddRequest) error {
	log.Println("==========aa", r.Idfa)

	if len(r.Idfa) == 0 {
		return nil
	}
	if r.Idfv == "{IDFV}" {
		return nil
	}
	info := ad_report_rewarded.AdReportRewardedModel{
		Idfa:          &r.Idfa,
		AdUnitId:      &r.AdUnitId,
		AdUnitName:    &r.AdUnitName,
		Amount:        &r.Amount,
		Country:       &r.Country,
		Currency:      &r.<PERSON>rency,
		CustomData:    &r.CustomData,
		EventId:       &r.EventId,
		EventToken:    &r.EventToken,
		EventTokenAll: &r.EventToken<PERSON>ll,
		Idfv:          &r.Idfv,
		Ip:            &r.Ip,
		NetworkName:   &r.<PERSON>ame,
		PackageName:   &r.Package<PERSON>ame,
		Placement:     &r.Placement,
		Platform:      &r.Platform,
		Ts:            &r.Ts,
		UserId:        &r.UserId,
	}
	err := info.Insert()

	if err != nil {
		log.Println("insert error", err)
	}
	return nil
}
