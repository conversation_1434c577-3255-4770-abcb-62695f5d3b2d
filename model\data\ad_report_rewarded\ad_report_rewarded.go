package ad_report_rewarded

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_report_rewarded")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// AdUnitId 广告单元 ID
	AdUnitId = property.Property("ad_unit_id")
	// AdUnitName 广告单元名称
	AdUnitName = property.Property("ad_unit_name")
	// Amount 您选择奖励给用户的货币金额。您可以在每个广告单元的广告单元编辑页面中设置此金额
	Amount = property.Property("amount")
	// Country 国家代码
	Country = property.Property("country")
	// Currency 您选择奖励给用户的货币类型（网址编码）。您可以在每个广告单元的广告单元编辑页面中设置此设置
	Currency = property.Property("currency")
	// CustomData 自定义数据
	CustomData = property.Property("custom_data")
	// EventId 唯一事件 ID
	EventId = property.Property("event_id")
	// EventToken sha1( EVENT_ID+ 你的事件密钥 )
	EventToken = property.Property("event_token")
	// EventTokenAll sha256（按字母顺序排列所有1 个宏，URL 解码 + Your-Event-Key）
	EventTokenAll = property.Property("event_token_all")
	// Idfa iOS IDFA 或 Google 广告 ID
	Idfa = property.Property("idfa")
	// Idfv 标识符
	Idfv = property.Property("idfv")
	// Ip 用户的ip
	Ip = property.Property("ip")
	// NetworkName 广告网络名称
	NetworkName = property.Property("network_name")
	// PackageName 包名
	PackageName = property.Property("package_name")
	// Placement 广告展示位置名称
	Placement = property.Property("placement")
	// Platform 应用程序平台：android、fireos、 或ios
	Platform = property.Property("platform")
	// Ts 广告加载时间的时间戳（整数，自纪元以来的秒数）
	Ts = property.Property("ts")
	// UserId 用户 ID
	UserId = property.Property("user_id")
)

var FieldsAll = Fields(Id, AdUnitId, AdUnitName, Amount, Country, Currency, CustomData, EventId, EventToken, EventTokenAll, Idfa, Idfv, Ip, NetworkName, PackageName, Placement, Platform, Ts, UserId)
var NonePrimaryFields = Fields(AdUnitId, AdUnitName, Amount, Country, Currency, CustomData, EventId, EventToken, EventTokenAll, Idfa, Idfv, Ip, NetworkName, PackageName, Placement, Platform, Ts, UserId)
var NoneAutoIncrementFields = Fields(AdUnitId, AdUnitName, Amount, Country, Currency, CustomData, EventId, EventToken, EventTokenAll, Idfa, Idfv, Ip, NetworkName, PackageName, Placement, Platform, Ts, UserId)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdReportRewardedModel 激励视频奖励回调报表
type AdReportRewardedModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// AdUnitId 广告单元 ID
	AdUnitId *string `orm:"ad_unit_id" json:"ad_unit_id"`

	// AdUnitName 广告单元名称
	AdUnitName *string `orm:"ad_unit_name" json:"ad_unit_name"`

	// Amount 您选择奖励给用户的货币金额。您可以在每个广告单元的广告单元编辑页面中设置此金额
	Amount *string `orm:"amount" json:"amount"`

	// Country 国家代码
	Country *string `orm:"country" json:"country"`

	// Currency 您选择奖励给用户的货币类型（网址编码）。您可以在每个广告单元的广告单元编辑页面中设置此设置
	Currency *string `orm:"currency" json:"currency"`

	// CustomData 自定义数据
	CustomData *string `orm:"custom_data" json:"custom_data"`

	// EventId 唯一事件 ID
	EventId *string `orm:"event_id" json:"event_id"`

	// EventToken sha1( EVENT_ID+ 你的事件密钥 )
	EventToken *string `orm:"event_token" json:"event_token"`

	// EventTokenAll sha256（按字母顺序排列所有1 个宏，URL 解码 + Your-Event-Key）
	EventTokenAll *string `orm:"event_token_all" json:"event_token_all"`

	// Idfa iOS IDFA 或 Google 广告 ID
	Idfa *string `orm:"idfa" json:"idfa"`

	// Idfv 标识符
	Idfv *string `orm:"idfv" json:"idfv"`

	// Ip 用户的ip
	Ip *string `orm:"ip" json:"ip"`

	// NetworkName 广告网络名称
	NetworkName *string `orm:"network_name" json:"network_name"`

	// PackageName 包名
	PackageName *string `orm:"package_name" json:"package_name"`

	// Placement 广告展示位置名称
	Placement *string `orm:"placement" json:"placement"`

	// Platform 应用程序平台：android、fireos、 或ios
	Platform *string `orm:"platform" json:"platform"`

	// Ts 广告加载时间的时间戳（整数，自纪元以来的秒数）
	Ts *string `orm:"ts" json:"ts"`

	// UserId 用户 ID
	UserId *string `orm:"user_id" json:"user_id"`
}

type PagedResult struct {
	Records      []*AdReportRewardedModel `json:"list"`
	PageNum      int                      `json:"page"`
	PageSize     int                      `json:"page_size"`
	TotalPages   int                      `json:"total_pages"`
	TotalRecords int                      `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:            1,
	AdUnitId:      2,
	AdUnitName:    3,
	Amount:        4,
	Country:       5,
	Currency:      6,
	CustomData:    7,
	EventId:       8,
	EventToken:    9,
	EventTokenAll: 10,
	Idfa:          11,
	Idfv:          12,
	Ip:            13,
	NetworkName:   14,
	PackageName:   15,
	Placement:     16,
	Platform:      17,
	Ts:            18,
	UserId:        19,
}

func (m *AdReportRewardedModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdReportRewardedModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.AdUnitId, m.AdUnitName, m.Amount, m.Country, m.Currency, m.CustomData, m.EventId, m.EventToken, m.EventTokenAll, m.Idfa, m.Idfv, m.Ip, m.NetworkName, m.PackageName, m.Placement, m.Platform, m.Ts, m.UserId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdReportRewardedModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AdUnitId, m.AdUnitName, m.Amount, m.Country, m.Currency, m.CustomData, m.EventId, m.EventToken, m.EventTokenAll, m.Idfa, m.Idfv, m.Ip, m.NetworkName, m.PackageName, m.Placement, m.Platform, m.Ts, m.UserId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdReportRewardedModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdReportRewardedModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AdUnitId, m.AdUnitName, m.Amount, m.Country, m.Currency, m.CustomData, m.EventId, m.EventToken, m.EventTokenAll, m.Idfa, m.Idfv, m.Ip, m.NetworkName, m.PackageName, m.Placement, m.Platform, m.Ts, m.UserId)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportRewardedModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdReportRewardedModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AdUnitId, m.AdUnitName, m.Amount, m.Country, m.Currency, m.CustomData, m.EventId, m.EventToken, m.EventTokenAll, m.Idfa, m.Idfv, m.Ip, m.NetworkName, m.PackageName, m.Placement, m.Platform, m.Ts, m.UserId), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportRewardedModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdReportRewardedModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.AdUnitId, m.AdUnitName, m.Amount, m.Country, m.Currency, m.CustomData, m.EventId, m.EventToken, m.EventTokenAll, m.Idfa, m.Idfv, m.Ip, m.NetworkName, m.PackageName, m.Placement, m.Platform, m.Ts, m.UserId), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AdUnitId, m.AdUnitName, m.Amount, m.Country, m.Currency, m.CustomData, m.EventId, m.EventToken, m.EventTokenAll, m.Idfa, m.Idfv, m.Ip, m.NetworkName, m.PackageName, m.Placement, m.Platform, m.Ts, m.UserId))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportRewardedModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdReportRewardedModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdReportRewardedModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdReportRewardedModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdReportRewardedModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdReportRewardedModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdReportRewardedModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdReportRewardedModel, error) {

	modelList := make([]*AdReportRewardedModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdReportRewardedModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Amount, &m.Country, &m.Currency, &m.CustomData, &m.EventId, &m.EventToken, &m.EventTokenAll, &m.Idfa, &m.Idfv, &m.Ip, &m.NetworkName, &m.PackageName, &m.Placement, &m.Platform, &m.Ts, &m.UserId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdReportRewardedModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdReportRewardedModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdReportRewardedModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdReportRewardedModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdReportRewardedModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdReportRewardedModel, error) {

	m := &AdReportRewardedModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Amount, &m.Country, &m.Currency, &m.CustomData, &m.EventId, &m.EventToken, &m.EventTokenAll, &m.Idfa, &m.Idfv, &m.Ip, &m.NetworkName, &m.PackageName, &m.Placement, &m.Platform, &m.Ts, &m.UserId)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdReportRewardedModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdReportRewardedModel, error) {

	modelList := make([]*AdReportRewardedModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdReportRewardedModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Amount, &m.Country, &m.Currency, &m.CustomData, &m.EventId, &m.EventToken, &m.EventTokenAll, &m.Idfa, &m.Idfv, &m.Ip, &m.NetworkName, &m.PackageName, &m.Placement, &m.Platform, &m.Ts, &m.UserId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdReportRewardedModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdReportRewardedModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdReportRewardedModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Amount, &m.Country, &m.Currency, &m.CustomData, &m.EventId, &m.EventToken, &m.EventTokenAll, &m.Idfa, &m.Idfv, &m.Ip, &m.NetworkName, &m.PackageName, &m.Placement, &m.Platform, &m.Ts, &m.UserId)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdReportRewardedModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdReportRewardedModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdReportRewardedModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdReportRewardedModel, error) {

	m := &AdReportRewardedModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdReportRewardedModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdReportRewardedModel, error) {

	modelList := make([]*AdReportRewardedModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdReportRewardedModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdReportRewardedModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdReportRewardedModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdReportRewardedModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdReportRewardedModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.AdUnitId, d.AdUnitName, d.Amount, d.Country, d.Currency, d.CustomData, d.EventId, d.EventToken, d.EventTokenAll, d.Idfa, d.Idfv, d.Ip, d.NetworkName, d.PackageName, d.Placement, d.Platform, d.Ts, d.UserId))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "ad_unit_id",
			"title":     "广告单元 ID",
		},
		{
			"dataIndex": "ad_unit_name",
			"title":     "广告单元名称",
		},
		{
			"dataIndex": "amount",
			"title":     "您选择奖励给用户的货币金额。您可以在每个广告单元的广告单元编辑页面中设置此金额",
		},
		{
			"dataIndex": "country",
			"title":     "国家代码",
		},
		{
			"dataIndex": "currency",
			"title":     "您选择奖励给用户的货币类型（网址编码）。您可以在每个广告单元的广告单元编辑页面中设置此设置",
		},
		{
			"dataIndex": "custom_data",
			"title":     "自定义数据",
		},
		{
			"dataIndex": "event_id",
			"title":     "唯一事件 ID",
		},
		{
			"dataIndex": "event_token",
			"title":     "sha1( EVENT_ID+ 你的事件密钥 )",
		},
		{
			"dataIndex": "event_token_all",
			"title":     "sha256（按字母顺序排列所有1 个宏，URL 解码 + Your-Event-Key）",
		},
		{
			"dataIndex": "idfa",
			"title":     "iOS IDFA 或 Google 广告 ID",
		},
		{
			"dataIndex": "idfv",
			"title":     "标识符",
		},
		{
			"dataIndex": "ip",
			"title":     "用户的ip",
		},
		{
			"dataIndex": "network_name",
			"title":     "广告网络名称",
		},
		{
			"dataIndex": "package_name",
			"title":     "包名",
		},
		{
			"dataIndex": "placement",
			"title":     "广告展示位置名称",
		},
		{
			"dataIndex": "platform",
			"title":     "应用程序平台：android、fireos、 或ios",
		},
		{
			"dataIndex": "ts",
			"title":     "广告加载时间的时间戳（整数，自纪元以来的秒数）",
		},
		{
			"dataIndex": "user_id",
			"title":     "用户 ID",
		}}
}
