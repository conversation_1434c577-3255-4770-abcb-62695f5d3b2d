package sql_helper

import (
	"910.com/plus2.git/plusQ"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"iaa_data/utils"
	"log"
	"strings"
	"time"
)

type LogHelper struct {
	AllLogList map[string][]map[string]interface{}
	LevelTime  map[string]int64
}

var singleton *LogHelper

func LogHelperInstance() *LogHelper {
	if singleton == nil {
		singleton = &LogHelper{}
	}
	return singleton
}

func (o *LogHelper) HandleDataMapInsert(dbName, keyTag, tableName string, info any, count int, outTime int64) {
	infoJson, err := json.Marshal(info)
	if err != nil {
		return
	}
	var item map[string]interface{}
	err = json.Unmarshal(infoJson, &item)
	if err != nil {
		return
	}

	if o.AllLogList == nil {
		o.AllLogList = make(map[string][]map[string]interface{})
	}
	if o.LevelTime == nil {
		o.LevelTime = make(map[string]int64)
	}
	o.AllLogList[keyTag] = append(o.AllLogList[keyTag], item)

	if len(o.AllLogList[keyTag]) == count {
		o.SaveInfo(dbName, keyTag, tableName)
	}

	if o.LevelTime[keyTag] != 0 {
		tt := time.Now().Unix()
		if (tt - o.LevelTime[keyTag]) > outTime {
			o.SaveInfo(dbName, keyTag, tableName)
		}
	}
}

func (o *LogHelper) HandleDataMapEnd(dbName, keyTag, tableName string) {
	//_, _, err := plusQ.Db().InsertOrUpdateBatch(tableName, o.AllLogList[keyTag], true)
	aa := o.AllLogList[keyTag]
	c := context.Background()
	err := BatchInsert(&c, dbName, tableName, &aa)
	if err != nil {
		log.Println("批量入库失败"+tableName, err.Error())
	} else {
		//log.Println("批量入库成功"+tableName, len(o.AllLogList[keyTag]))
	}
}

func (o *LogHelper) SaveInfo(dbName, keyTag, tableName string) {
	//_, _, err := plusQ.Db().InsertOrUpdateBatch(tableName, o.AllLogList[keyTag], true)
	aa := o.AllLogList[keyTag]
	c := context.Background()
	err := BatchInsert(&c, dbName, tableName, &aa)
	if err != nil {
		log.Println("批量入库失败"+tableName, err.Error())
	} else {
		log.Println("批量入库成功"+tableName, len(o.AllLogList[keyTag]))
	}
	o.AllLogList[keyTag] = nil
	o.LevelTime[keyTag] = time.Now().Unix()
}

// BatchInsert 批量插入数据库
func BatchInsert(ctx *context.Context, dbName, tableName string, insertData *[]map[string]interface{}) error {
	//mlog.LogInfo("批量数量=====================："+tableName, len(*insertData))
	if len(dbName) == 0 {
		return plusQ.Db().Transaction(func(tx *sql.Tx) error {
			sqlQuery, values, err := plusQ.Db().InsertOrUpdateBatch(tableName, *insertData, false)
			if err != nil {
				log.Println("存在失败记录", tableName, err.Error())
				return err
			}
			_, err = tx.ExecContext(*ctx, sqlQuery, values...)
			if err != nil {
				log.Println("存在失败记录", tableName, err.Error())
				return err
			}
			*insertData = (*insertData)[:0] // Clear the slice
			return nil
		})
	} else {
		return plusQ.Db(dbName).Transaction(func(tx *sql.Tx) error {
			sqlQuery, values, err := plusQ.Db(dbName).InsertOrUpdateBatch(tableName, *insertData, false)
			if err != nil {
				log.Println("存在失败记录", tableName, err.Error())
				return err
			}
			_, err = tx.ExecContext(*ctx, sqlQuery, values...)
			if err != nil {
				log.Println("存在失败记录", tableName, err.Error())
				return err
			}
			*insertData = (*insertData)[:0] // Clear the slice
			return nil
		})
	}
}

func InsertOrUpdateAllFiled(dbName, tbName, uniKey string, info any) error {
	infoJson, err := json.Marshal(info)
	if err != nil {
		return err
	}
	var item map[string]interface{}
	err = json.Unmarshal(infoJson, &item)
	if err != nil {
		return err
	}

	sqlKey := ""
	sqlVal := ""
	sqlD := ""

	uniKeys := strings.Split(uniKey, "|")

	for key, val := range item {
		if sqlKey == "" {
			sqlKey = key
		} else {
			sqlKey = key + "," + sqlKey
		}

		changeVal, ok := val.(string)
		if val == nil {
			ok = true
		}
		if ok {
			val = strings.ReplaceAll(changeVal, "'", "\\'")
		}

		if sqlVal == "" {
			if ok {
				sqlVal = "'" + utils.ChangeString(val) + "'"
			} else {
				sqlVal = utils.ChangeString(val)
			}
		} else {
			if ok {
				sqlVal = "'" + utils.ChangeString(val) + "'" + "," + sqlVal
			} else {
				sqlVal = utils.ChangeString(val) + "," + sqlVal
			}
		}

		if sqlD == "" {
			if !isHaveKey(key, uniKeys) {
				sqlD = key + " = VALUES(" + key + ")"
			}
		} else {
			if !isHaveKey(key, uniKeys) {
				sqlD = sqlD + ", " + key + " = VALUES(" + key + ")"
			}
		}
	}

	sql := "INSERT INTO " + tbName + " (" + sqlKey + ")  " +
		"VALUES (" + sqlVal + ")  " +
		"ON DUPLICATE KEY UPDATE " + sqlD
	//log.Println("====", sql)

	var err2 error
	if len(dbName) == 0 {
		_, err2 = plusQ.Db().Insert(sql)
	} else {
		_, err2 = plusQ.Db(dbName).Insert(sql)
	}

	if err2 != nil {
		log.Println("insert fail :", err2.Error())
	}
	return err2
}

func InsertOrUpdateAllFiledSummary(dbName, tbName, uniKey string, info any) error {
	infoJson, err := json.Marshal(info)
	if err != nil {
		return err
	}
	var item map[string]interface{}
	err = json.Unmarshal(infoJson, &item)
	if err != nil {
		return err
	}

	sqlKey := ""
	sqlVal := ""
	sqlD := ""

	uniKeys := strings.Split(uniKey, "|")

	for key, val := range item {
		if sqlKey == "" {
			sqlKey = key
		} else {
			sqlKey = key + "," + sqlKey
		}

		changeVal, ok := val.(string)
		if val == nil {
			ok = true
		}
		if ok {
			val = strings.ReplaceAll(changeVal, "'", "\\'")
		}

		if sqlVal == "" {
			if ok {
				sqlVal = "'" + utils.ChangeStringSummary(val) + "'"
			} else {
				sqlVal = utils.ChangeStringSummary(val)
			}
		} else {
			if ok {
				sqlVal = "'" + utils.ChangeStringSummary(val) + "'" + "," + sqlVal
			} else {
				sqlVal = utils.ChangeStringSummary(val) + "," + sqlVal
			}
		}

		if sqlD == "" {
			if !isHaveKey(key, uniKeys) {
				sqlD = key + " = VALUES(" + key + ")"
			}
		} else {
			if !isHaveKey(key, uniKeys) {
				sqlD = sqlD + ", " + key + " = VALUES(" + key + ")"
			}
		}
	}

	sql := "INSERT INTO " + tbName + " (" + sqlKey + ")  " +
		"VALUES (" + sqlVal + ")  " +
		"ON DUPLICATE KEY UPDATE " + sqlD
	//log.Println("====", sql)

	var err2 error
	if len(dbName) == 0 {
		_, err2 = plusQ.Db().Insert(sql)
	} else {
		_, err2 = plusQ.Db(dbName).Insert(sql)
	}

	if err2 != nil {
		log.Println("insert fail :", err2.Error())
	}
	return err2
}

func isHaveKey(key string, keys []string) bool {
	for _, val := range keys {
		if val == key {
			return true
		}
	}
	return false
}

func InsertOrUpdate(dbName, tbName string, info any) {
	infoJson, err := json.Marshal(info)
	if err != nil {
		return
	}
	var item map[string]interface{}
	err = json.Unmarshal(infoJson, &item)
	if err != nil {
		return
	}

	sqlKey := ""
	sqlVal := ""
	sqlD := ""

	for key, val := range item {
		if sqlKey == "" {
			sqlKey = key
		} else {
			sqlKey = key + "," + sqlKey
		}

		changeVal, ok := val.(string)
		if val == nil {
			ok = true
		}
		if ok {
			val = strings.ReplaceAll(changeVal, "'", "\\'")
		}

		if sqlVal == "" {
			if ok {
				sqlVal = "'" + utils.ChangeString(val) + "'"
			} else {
				sqlVal = utils.ChangeString(val)
			}
		} else {
			if ok {
				sqlVal = "'" + utils.ChangeString(val) + "'" + "," + sqlVal
			} else {
				sqlVal = utils.ChangeString(val) + "," + sqlVal
			}
		}

	}
	sqlD = " TIME_BJ =  IF(VALUES(TIME_BJ) < TIME_BJ, VALUES(TIME_BJ), TIME_BJ) ," +
		" TIME_SERVER =  IF(VALUES(TIME_SERVER) < TIME_SERVER, VALUES(TIME_SERVER), TIME_SERVER) "

	sql := "INSERT INTO " + tbName + " (" + sqlKey + ")  " +
		"VALUES (" + sqlVal + ")  " +
		"ON DUPLICATE KEY UPDATE " + sqlD
	var err2 error
	if len(dbName) == 0 {
		_, err2 = plusQ.Db().Insert(sql)
	} else {
		_, err2 = plusQ.Db(dbName).Insert(sql)
	}

	//log.Println("=================", sql)

	if err2 != nil {
		log.Println(" save err"+sql, err2.Error())
	}
}

func GetUniqueLogs(tday, time_zone, table_name string) map[string]string {
	sql := "select key_val  from iaa_report_unique_log where tday = " + tday + " and time_zone = " + time_zone + " and table_name ='" + table_name + "'"

	list, _ := plusQ.Db("data").List(sql)
	mapInfo := make(map[string]string)
	if len(list) > 0 {
		for _, item := range list {
			keyVal := fmt.Sprint(item["key_val"])
			mapInfo[keyVal] = keyVal
		}
	}
	return mapInfo
}

func IsHaveKey(addList []string, key string) bool {
	isHave := false
	if len(addList) == 0 {
		return isHave
	}
	for _, val := range addList {
		if val == key {
			isHave = true
			break
		}
	}
	return isHave
}
