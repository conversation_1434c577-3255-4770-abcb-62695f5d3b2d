package summary_ad_user

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils"
	"iaa_data/utils/constant"
	"iaa_data/utils/country_helper"
	"iaa_data/utils/day_check_helper"
	"iaa_data/utils/game_helper"
	"iaa_data/utils/sql_helper"
	"log"
	"time"
)

func InitFunc(tagTimeZone string) {

	nowTime := time.Now()

	dayCheckTag := "summary_ad_user_" + tagTimeZone

	if constant.TIME_BJ == tagTimeZone {
		tdayTime := nowTime.Format(time.DateOnly)
		SummaryReport(tdayTime, tdayTime, tagTimeZone)
		if nowTime.Hour() >= 1 && nowTime.Hour() < 3 {
			if day_check_helper.CheckHour7(dayCheckTag) == "false" {
				start := nowTime.Add(-7 * 24 * time.Hour)
				endTime := nowTime.Add(24 * time.Hour).Format(time.DateOnly)
				log.Println("开始汇总", start.Format(time.DateOnly), endTime)
				SummaryReport(start.Format(time.DateOnly), endTime, tagTimeZone)
			}
		}
	} else if constant.TIME_ERU == tagTimeZone {
		nowTimeStr := utils.BjToERUTime(nowTime.Format(time.DateTime))
		da, _ := time.Parse(time.DateTime, nowTimeStr)
		tdayTime := da.Format(time.DateOnly)

		SummaryReport(tdayTime, tdayTime, tagTimeZone)
		if nowTime.Hour() >= 8 && nowTime.Hour() < 9 {
			if day_check_helper.CheckHour7(dayCheckTag) == "false" {
				start := nowTime.Add(-7 * 24 * time.Hour)
				endTime := nowTime.Add(24 * time.Hour).Format(time.DateOnly)
				log.Println("开始汇总", start.Format(time.DateOnly), endTime)
				SummaryReport(start.Format(time.DateOnly), endTime, tagTimeZone)
			}
		}
	} else if constant.TIME_US == tagTimeZone {
		nowTimeStr := utils.BjToUSTime(nowTime.Format(time.DateTime))
		da, _ := time.Parse(time.DateTime, nowTimeStr)
		tdayTime := da.Format(time.DateOnly)
		SummaryReport(tdayTime, tdayTime, tagTimeZone)
		if nowTime.Hour() >= 16 && nowTime.Hour() < 17 {
			if day_check_helper.CheckHour7(dayCheckTag) == "false" {
				start := nowTime.Add(-7 * 24 * time.Hour)
				endTime := nowTime.Add(24 * time.Hour).Format(time.DateOnly)
				log.Println("开始汇总", start.Format(time.DateOnly), endTime)
				SummaryReport(start.Format(time.DateOnly), endTime, tagTimeZone)
			}
		}
	}

}

func SummaryReport(startTime, endTime, timeZoneStr string) {
	if !utils.IsTimeZone(timeZoneStr) {
		return
	}
	startDate, err1 := utils.ParseBjTime(time.DateOnly, startTime)
	if err1 != nil {
		log.Println("时间格式有误startTime")
		return
	}
	endDate, err2 := utils.ParseBjTime(time.DateOnly, endTime)
	if err2 != nil {
		log.Println("时间格式有误endTime")
		return
	}

	for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {

		fmt.Println(currentDate.Format("2006-01-02 (Monday)"))
		timeVal := currentDate.Format("2006-01-02")
		sTime := timeVal + " 00:00:00"
		eTime := timeVal + " 23:59:59"
		if timeZoneStr == constant.TIME_ERU {
			sTime = utils.ERUtoBjTime(sTime)
			eTime = utils.ERUtoBjTime(eTime)
		} else if timeZoneStr == constant.TIME_US {
			sTime = utils.UStoBjTime(sTime)
			eTime = utils.UStoBjTime(eTime)
		}
		tday := currentDate.Format("********")

		getAllRevenueResultList(sTime, eTime, timeZoneStr, tday)

	}
}

func getAllRevenueResultList(start, end, timeZoneStr, tday string) {
	timeZone := utils.GeTimeZoneTag(timeZoneStr)
	delSql := "delete FROM ad_active_user_log where tday=" + tday + " and time_zone = " + fmt.Sprint(timeZone)
	selectSql := "SELECT cp_game_id,game_id,core_account,placement,ad_unit_id,ad_type,package_id,iaa_creative_id,os," +
		"country,source_id," +
		" sum(revenue) as revenue" +
		" FROM " + "hw_sdk_ad_revenue_log " +
		" where time_bj between '" + start + "' and '" + end + "'"
	selectSql = selectSql + " group by core_account,placement,ad_unit_id,iaa_creative_id"

	//`core_account`,`tday`,`placement`,`ad_unit_id`

	log.Println("==del==", delSql)

	list, err := plusQ.Db("data").List(selectSql)
	if err == nil {
		//查询成功才进行删除
		plusQ.Db("data").Delete(delSql)
	}

	if err == nil {
		for _, item := range list {

			info := make(map[string]interface{})
			packageId := utils.AnyToInt(item["package_id"])
			channelId := game_helper.GameHelInstant().GetChannelId(packageId)
			countryId := country_helper.CountryInstant().GetCountryId(fmt.Sprint(item["country"]))

			info["tday"] = tday
			info["time_zone"] = timeZone
			info["cp_game_id"] = item["cp_game_id"]
			info["game_id"] = item["game_id"]
			info["package_id"] = item["package_id"]
			info["country_id"] = countryId
			info["channel_id"] = channelId
			info["platform"] = item["os"]
			info["core_account"] = item["core_account"]
			info["placement"] = item["placement"]
			info["ad_unit_id"] = item["ad_unit_id"]
			info["ad_type"] = item["ad_type"]
			info["revenue"] = item["revenue"]
			info["creative_id"] = item["iaa_creative_id"]
			info["source_id"] = item["source_id"]
			sql_helper.LogHelperInstance().HandleDataMapInsert("data", "ad_active_user_log", "ad_active_user_log", info, 50, 1001)
			//sql_helper.InsertOrUpdateAllFiled("data", "ad_active_user_log", "", info)
		}
	}
	sql_helper.LogHelperInstance().HandleDataMapEnd("data", "ad_active_user_log", "ad_active_user_log")
}
