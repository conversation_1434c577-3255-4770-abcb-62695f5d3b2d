package item_helper

import (
	"fmt"
	"iaa_data/utils"
	"strconv"
	"strings"
)

var keyTypeMap = map[string]string{
	"account_id":     "string",
	"cp_game_id":     "int",
	"media":          "string",
	"country_rank":   "string",
	"platform_id":    "int",
	"new_users":      "double",
	"cost":           "double",
	"money":          "double",
	"placement_name": "string",
	"request":        "int",
	"impression":     "int",
	"click":          "int",
	"revenue":        "double",
	"core_center":    "string",
}

func ChangeData(item map[string]interface{}) map[string]interface{} {
	newItem := make(map[string]interface{})
	for key, val := range item {
		typeVal, ok := keyTypeMap[key]
		newVal := val
		if ok {
			if typeVal == "string" {
				newVal = utils.ChangeString(val)
			} else if typeVal == "int" {
				newVal = utils.AnyToInt(val)
			} else if typeVal == "double" {
				floatValue, err := strconv.ParseFloat(fmt.Sprint(val), 64)
				if err == nil {
					newVal = floatValue
				}
			}
		}
		newItem[key] = newVal

		if key == "tday" {
			newVal2 := strings.Replace(fmt.Sprint(val), " 00:00:00 +0800 CST", "", -1)
			newItem[key] = newVal2
		}

		if key == "add_date" {
			newVal2 := strings.Replace(fmt.Sprint(val), " 00:00:00 +0800 CST", "", -1)
			newItem[key] = newVal2
		}
		if key == "event_time" && strings.Contains(fmt.Sprint(val), "+0800 CST") {
			newVal2 := strings.Replace(fmt.Sprint(val), " +0800 CST", "", -1)
			newItem[key] = newVal2
		}

	}
	return newItem
}
