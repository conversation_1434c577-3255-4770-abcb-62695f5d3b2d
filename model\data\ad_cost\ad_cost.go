package ad_cost

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_cost")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// Time 日期
	Time = property.Property("time")
	// AddTime 添加时间
	AddTime = property.Property("add_time")
	// UpdateTime 更新时间
	UpdateTime = property.Property("update_time")
	// ChannelId 渠道id
	ChannelId = property.Property("channel_id")
	// CpGameId 游戏原名id
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏统计名id
	GameId = property.Property("game_id")
	// PackageId 包号
	PackageId = property.Property("package_id")
	// AccountId 推广账号id
	AccountId = property.Property("account_id")
	// AdAccount 推广账号名
	AdAccount = property.Property("ad_account")
	// CampaignId 广告组id
	CampaignId = property.Property("campaign_id")
	// CampaignName 广告组名
	CampaignName = property.Property("campaign_name")
	// PlanId 计划id
	PlanId = property.Property("plan_id")
	// PlanName 计划名
	PlanName = property.Property("plan_name")
	// CreativeId 创意id
	CreativeId = property.Property("creative_id")
	// CreativeName 创意名
	CreativeName = property.Property("creative_name")
	// Show 曝光数
	Show = property.Property("show")
	// Click 点击数
	Click = property.Property("click")
	// Install 安装数
	Install = property.Property("install")
	// Cost 消耗
	Cost = property.Property("cost")
	// CostDiscount 返点后消耗
	CostDiscount = property.Property("cost_discount")
	// CostRmb 消耗（rmb）
	CostRmb = property.Property("cost_rmb")
	// CostDiscountRmb 返点后消耗（rmb）
	CostDiscountRmb = property.Property("cost_discount_rmb")
	// CountryCode 国家code
	CountryCode = property.Property("country_code")
	// TimeZone 时区名称,utc-8,gmt-3
	TimeZone = property.Property("time_zone")
	// EffectiveStatus 有效状态 0 正常 1已归档或者已删除
	EffectiveStatus = property.Property("effective_status")
	// PlatformId 1安卓2苹果
	PlatformId = property.Property("platform_id")
	// Ctr ctr
	Ctr = property.Property("ctr")
	// Cpm cpm
	Cpm = property.Property("cpm")
	// InstallCost 媒体安装成本=install/spend
	InstallCost = property.Property("install_cost")
	// ClickInstallRate 点击安装率=install/clicks
	ClickInstallRate = property.Property("click_install_rate")
	// InstallPerShow 千次曝光安装量=(install/impressions)*1000
	InstallPerShow = property.Property("install_per_show")
	// CreativeIdOrigin creative_id
	CreativeIdOrigin = property.Property("creative_id_origin")
	// SourceId 关联base_conf_platform.hw_base_source_conf
	SourceId = property.Property("source_id")
	// CreativeIdMd5 creative_id_md5
	CreativeIdMd5 = property.Property("creative_id_md5")
)

var FieldsAll = Fields(Id, Time, AddTime, UpdateTime, ChannelId, CpGameId, GameId, PackageId, AccountId, AdAccount, CampaignId, CampaignName, PlanId, PlanName, CreativeId, CreativeName, Show, Click, Install, Cost, CostDiscount, CostRmb, CostDiscountRmb, CountryCode, TimeZone, EffectiveStatus, PlatformId, Ctr, Cpm, InstallCost, ClickInstallRate, InstallPerShow, CreativeIdOrigin, SourceId, CreativeIdMd5)
var NonePrimaryFields = Fields(Time, AddTime, UpdateTime, ChannelId, CpGameId, GameId, PackageId, AccountId, AdAccount, CampaignId, CampaignName, PlanId, PlanName, CreativeId, CreativeName, Show, Click, Install, Cost, CostDiscount, CostRmb, CostDiscountRmb, CountryCode, TimeZone, EffectiveStatus, PlatformId, Ctr, Cpm, InstallCost, ClickInstallRate, InstallPerShow, CreativeIdOrigin, SourceId, CreativeIdMd5)
var NoneAutoIncrementFields = Fields(Time, AddTime, UpdateTime, ChannelId, CpGameId, GameId, PackageId, AccountId, AdAccount, CampaignId, CampaignName, PlanId, PlanName, CreativeId, CreativeName, Show, Click, Install, Cost, CostDiscount, CostRmb, CostDiscountRmb, CountryCode, TimeZone, EffectiveStatus, PlatformId, Ctr, Cpm, InstallCost, ClickInstallRate, InstallPerShow, CreativeIdOrigin, SourceId, CreativeIdMd5)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdCostModel 广告消耗表
type AdCostModel struct {
	// Id 自增id
	Id uint32 `orm:"id" json:"id"`

	// Time 日期
	Time *CustomTime `orm:"time" json:"time"`

	// AddTime 添加时间
	AddTime *CustomTime `orm:"add_time" json:"add_time"`

	// UpdateTime 更新时间
	UpdateTime *CustomTime `orm:"update_time" json:"update_time"`

	// ChannelId 渠道id
	ChannelId uint32 `orm:"channel_id" json:"channel_id"`

	// CpGameId 游戏原名id
	CpGameId uint32 `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏统计名id
	GameId uint32 `orm:"game_id" json:"game_id"`

	// PackageId 包号
	PackageId uint32 `orm:"package_id" json:"package_id"`

	// AccountId 推广账号id
	AccountId string `orm:"account_id" json:"account_id"`

	// AdAccount 推广账号名
	AdAccount string `orm:"ad_account" json:"ad_account"`

	// CampaignId 广告组id
	CampaignId string `orm:"campaign_id" json:"campaign_id"`

	// CampaignName 广告组名
	CampaignName string `orm:"campaign_name" json:"campaign_name"`

	// PlanId 计划id
	PlanId string `orm:"plan_id" json:"plan_id"`

	// PlanName 计划名
	PlanName string `orm:"plan_name" json:"plan_name"`

	// CreativeId 创意id
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// CreativeName 创意名
	CreativeName string `orm:"creative_name" json:"creative_name"`

	// Show 曝光数
	Show uint32 `orm:"show" json:"show"`

	// Click 点击数
	Click uint32 `orm:"click" json:"click"`

	// Install 安装数
	Install uint32 `orm:"install" json:"install"`

	// Cost 消耗
	Cost float32 `orm:"cost" json:"cost"`

	// CostDiscount 返点后消耗
	CostDiscount float32 `orm:"cost_discount" json:"cost_discount"`

	// CostRmb 消耗（rmb）
	CostRmb float32 `orm:"cost_rmb" json:"cost_rmb"`

	// CostDiscountRmb 返点后消耗（rmb）
	CostDiscountRmb float32 `orm:"cost_discount_rmb" json:"cost_discount_rmb"`

	// CountryCode 国家code
	CountryCode string `orm:"country_code" json:"country_code"`

	// TimeZone 时区名称,utc-8,gmt-3
	TimeZone string `orm:"time_zone" json:"time_zone"`

	// EffectiveStatus 有效状态 0 正常 1已归档或者已删除
	EffectiveStatus int `orm:"effective_status" json:"effective_status"`

	// PlatformId 1安卓2苹果
	PlatformId int `orm:"platform_id" json:"platform_id"`

	// Ctr ctr
	Ctr float32 `orm:"ctr" json:"ctr"`

	// Cpm cpm
	Cpm float32 `orm:"cpm" json:"cpm"`

	// InstallCost 媒体安装成本=install/spend
	InstallCost float32 `orm:"install_cost" json:"install_cost"`

	// ClickInstallRate 点击安装率=install/clicks
	ClickInstallRate float32 `orm:"click_install_rate" json:"click_install_rate"`

	// InstallPerShow 千次曝光安装量=(install/impressions)*1000
	InstallPerShow float32 `orm:"install_per_show" json:"install_per_show"`

	// CreativeIdOrigin creative_id
	CreativeIdOrigin string `orm:"creative_id_origin" json:"creative_id_origin"`

	// SourceId 关联base_conf_platform.hw_base_source_conf
	SourceId int `orm:"source_id" json:"source_id"`

	// CreativeIdMd5 creative_id_md5
	CreativeIdMd5 string `orm:"creative_id_md5" json:"creative_id_md5"`
}

type PagedResult struct {
	Records      []*AdCostModel `json:"list"`
	PageNum      int            `json:"page"`
	PageSize     int            `json:"page_size"`
	TotalPages   int            `json:"total_pages"`
	TotalRecords int            `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:               1,
	Time:             2,
	AddTime:          3,
	UpdateTime:       4,
	ChannelId:        5,
	CpGameId:         6,
	GameId:           7,
	PackageId:        8,
	AccountId:        9,
	AdAccount:        10,
	CampaignId:       11,
	CampaignName:     12,
	PlanId:           13,
	PlanName:         14,
	CreativeId:       15,
	CreativeName:     16,
	Show:             17,
	Click:            18,
	Install:          19,
	Cost:             20,
	CostDiscount:     21,
	CostRmb:          22,
	CostDiscountRmb:  23,
	CountryCode:      24,
	TimeZone:         25,
	EffectiveStatus:  26,
	PlatformId:       27,
	Ctr:              28,
	Cpm:              29,
	InstallCost:      30,
	ClickInstallRate: 31,
	InstallPerShow:   32,
	CreativeIdOrigin: 33,
	SourceId:         34,
	CreativeIdMd5:    35,
}

func (m *AdCostModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdCostModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.Time, m.AddTime, m.UpdateTime, m.ChannelId, m.CpGameId, m.GameId, m.PackageId, m.AccountId, m.AdAccount, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.Show, m.Click, m.Install, m.Cost, m.CostDiscount, m.CostRmb, m.CostDiscountRmb, m.CountryCode, m.TimeZone, m.EffectiveStatus, m.PlatformId, m.Ctr, m.Cpm, m.InstallCost, m.ClickInstallRate, m.InstallPerShow, m.CreativeIdOrigin, m.SourceId, m.CreativeIdMd5)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = uint32(lastInsertId)

	return nil

}

func (m *AdCostModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Time, m.AddTime, m.UpdateTime, m.ChannelId, m.CpGameId, m.GameId, m.PackageId, m.AccountId, m.AdAccount, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.Show, m.Click, m.Install, m.Cost, m.CostDiscount, m.CostRmb, m.CostDiscountRmb, m.CountryCode, m.TimeZone, m.EffectiveStatus, m.PlatformId, m.Ctr, m.Cpm, m.InstallCost, m.ClickInstallRate, m.InstallPerShow, m.CreativeIdOrigin, m.SourceId, m.CreativeIdMd5)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *AdCostModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdCostModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Time, m.AddTime, m.UpdateTime, m.ChannelId, m.CpGameId, m.GameId, m.PackageId, m.AccountId, m.AdAccount, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.Show, m.Click, m.Install, m.Cost, m.CostDiscount, m.CostRmb, m.CostDiscountRmb, m.CountryCode, m.TimeZone, m.EffectiveStatus, m.PlatformId, m.Ctr, m.Cpm, m.InstallCost, m.ClickInstallRate, m.InstallPerShow, m.CreativeIdOrigin, m.SourceId, m.CreativeIdMd5)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdCostModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Time, m.AddTime, m.UpdateTime, m.ChannelId, m.CpGameId, m.GameId, m.PackageId, m.AccountId, m.AdAccount, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.Show, m.Click, m.Install, m.Cost, m.CostDiscount, m.CostRmb, m.CostDiscountRmb, m.CountryCode, m.TimeZone, m.EffectiveStatus, m.PlatformId, m.Ctr, m.Cpm, m.InstallCost, m.ClickInstallRate, m.InstallPerShow, m.CreativeIdOrigin, m.SourceId, m.CreativeIdMd5), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdCostModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.Time, m.AddTime, m.UpdateTime, m.ChannelId, m.CpGameId, m.GameId, m.PackageId, m.AccountId, m.AdAccount, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.Show, m.Click, m.Install, m.Cost, m.CostDiscount, m.CostRmb, m.CostDiscountRmb, m.CountryCode, m.TimeZone, m.EffectiveStatus, m.PlatformId, m.Ctr, m.Cpm, m.InstallCost, m.ClickInstallRate, m.InstallPerShow, m.CreativeIdOrigin, m.SourceId, m.CreativeIdMd5), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Time, m.AddTime, m.UpdateTime, m.ChannelId, m.CpGameId, m.GameId, m.PackageId, m.AccountId, m.AdAccount, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.Show, m.Click, m.Install, m.Cost, m.CostDiscount, m.CostRmb, m.CostDiscountRmb, m.CountryCode, m.TimeZone, m.EffectiveStatus, m.PlatformId, m.Ctr, m.Cpm, m.InstallCost, m.ClickInstallRate, m.InstallPerShow, m.CreativeIdOrigin, m.SourceId, m.CreativeIdMd5))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdCostModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdCostModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdCostModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdCostModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdCostModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdCostModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdCostModel, error) {

	modelList := make([]*AdCostModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCostModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Time, &m.AddTime, &m.UpdateTime, &m.ChannelId, &m.CpGameId, &m.GameId, &m.PackageId, &m.AccountId, &m.AdAccount, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.Show, &m.Click, &m.Install, &m.Cost, &m.CostDiscount, &m.CostRmb, &m.CostDiscountRmb, &m.CountryCode, &m.TimeZone, &m.EffectiveStatus, &m.PlatformId, &m.Ctr, &m.Cpm, &m.InstallCost, &m.ClickInstallRate, &m.InstallPerShow, &m.CreativeIdOrigin, &m.SourceId, &m.CreativeIdMd5)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint32) (*AdCostModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint32) (*AdCostModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint32) (*AdCostModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint32) (*AdCostModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdCostModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdCostModel, error) {

	m := &AdCostModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.Time, &m.AddTime, &m.UpdateTime, &m.ChannelId, &m.CpGameId, &m.GameId, &m.PackageId, &m.AccountId, &m.AdAccount, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.Show, &m.Click, &m.Install, &m.Cost, &m.CostDiscount, &m.CostRmb, &m.CostDiscountRmb, &m.CountryCode, &m.TimeZone, &m.EffectiveStatus, &m.PlatformId, &m.Ctr, &m.Cpm, &m.InstallCost, &m.ClickInstallRate, &m.InstallPerShow, &m.CreativeIdOrigin, &m.SourceId, &m.CreativeIdMd5)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdCostModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdCostModel, error) {

	modelList := make([]*AdCostModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCostModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Time, &m.AddTime, &m.UpdateTime, &m.ChannelId, &m.CpGameId, &m.GameId, &m.PackageId, &m.AccountId, &m.AdAccount, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.Show, &m.Click, &m.Install, &m.Cost, &m.CostDiscount, &m.CostRmb, &m.CostDiscountRmb, &m.CountryCode, &m.TimeZone, &m.EffectiveStatus, &m.PlatformId, &m.Ctr, &m.Cpm, &m.InstallCost, &m.ClickInstallRate, &m.InstallPerShow, &m.CreativeIdOrigin, &m.SourceId, &m.CreativeIdMd5)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdCostModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdCostModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdCostModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Time, &m.AddTime, &m.UpdateTime, &m.ChannelId, &m.CpGameId, &m.GameId, &m.PackageId, &m.AccountId, &m.AdAccount, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.Show, &m.Click, &m.Install, &m.Cost, &m.CostDiscount, &m.CostRmb, &m.CostDiscountRmb, &m.CountryCode, &m.TimeZone, &m.EffectiveStatus, &m.PlatformId, &m.Ctr, &m.Cpm, &m.InstallCost, &m.ClickInstallRate, &m.InstallPerShow, &m.CreativeIdOrigin, &m.SourceId, &m.CreativeIdMd5)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint32) (*AdCostModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint32) (*AdCostModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdCostModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdCostModel, error) {

	m := &AdCostModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdCostModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdCostModel, error) {

	modelList := make([]*AdCostModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdCostModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdCostModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdCostModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdCostModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdCostModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.Time, d.AddTime, d.UpdateTime, d.ChannelId, d.CpGameId, d.GameId, d.PackageId, d.AccountId, d.AdAccount, d.CampaignId, d.CampaignName, d.PlanId, d.PlanName, d.CreativeId, d.CreativeName, d.Show, d.Click, d.Install, d.Cost, d.CostDiscount, d.CostRmb, d.CostDiscountRmb, d.CountryCode, d.TimeZone, d.EffectiveStatus, d.PlatformId, d.Ctr, d.Cpm, d.InstallCost, d.ClickInstallRate, d.InstallPerShow, d.CreativeIdOrigin, d.SourceId, d.CreativeIdMd5))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "time",
			"title":     "日期",
		},
		{
			"dataIndex": "add_time",
			"title":     "添加时间",
		},
		{
			"dataIndex": "update_time",
			"title":     "更新时间",
		},
		{
			"dataIndex": "channel_id",
			"title":     "渠道id",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名id",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏统计名id",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号",
		},
		{
			"dataIndex": "account_id",
			"title":     "推广账号id",
		},
		{
			"dataIndex": "ad_account",
			"title":     "推广账号名",
		},
		{
			"dataIndex": "campaign_id",
			"title":     "广告组id",
		},
		{
			"dataIndex": "campaign_name",
			"title":     "广告组名",
		},
		{
			"dataIndex": "plan_id",
			"title":     "计划id",
		},
		{
			"dataIndex": "plan_name",
			"title":     "计划名",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意id",
		},
		{
			"dataIndex": "creative_name",
			"title":     "创意名",
		},
		{
			"dataIndex": "show",
			"title":     "曝光数",
		},
		{
			"dataIndex": "click",
			"title":     "点击数",
		},
		{
			"dataIndex": "install",
			"title":     "安装数",
		},
		{
			"dataIndex": "cost",
			"title":     "消耗",
		},
		{
			"dataIndex": "cost_discount",
			"title":     "返点后消耗",
		},
		{
			"dataIndex": "cost_rmb",
			"title":     "消耗（rmb）",
		},
		{
			"dataIndex": "cost_discount_rmb",
			"title":     "返点后消耗（rmb）",
		},
		{
			"dataIndex": "country_code",
			"title":     "国家code",
		},
		{
			"dataIndex": "time_zone",
			"title":     "时区名称,utc-8,gmt-3",
		},
		{
			"dataIndex": "effective_status",
			"title":     "有效状态 0 正常 1已归档或者已删除",
		},
		{
			"dataIndex": "platform_id",
			"title":     "1安卓2苹果",
		},
		{
			"dataIndex": "ctr",
			"title":     "ctr",
		},
		{
			"dataIndex": "cpm",
			"title":     "cpm",
		},
		{
			"dataIndex": "install_cost",
			"title":     "媒体安装成本=install/spend",
		},
		{
			"dataIndex": "click_install_rate",
			"title":     "点击安装率=install/clicks",
		},
		{
			"dataIndex": "install_per_show",
			"title":     "千次曝光安装量=(install/impressions)*1000",
		},
		{
			"dataIndex": "creative_id_origin",
			"title":     "creative_id",
		},
		{
			"dataIndex": "source_id",
			"title":     "关联base_conf_platform.hw_base_source_conf",
		},
		{
			"dataIndex": "creative_id_md5",
			"title":     "creative_id_md5",
		}}
}

type AdCostModelTableFields struct {
	Id uint32 `title:"自增id" json:"id"`

	Time *CustomTime `title:"日期" json:"time"`

	AddTime *CustomTime `title:"添加时间" json:"add_time"`

	UpdateTime *CustomTime `title:"更新时间" json:"update_time"`

	ChannelId uint32 `title:"渠道id" json:"channel_id"`

	CpGameId uint32 `title:"游戏原名id" json:"cp_game_id"`

	GameId uint32 `title:"游戏统计名id" json:"game_id"`

	PackageId uint32 `title:"包号" json:"package_id"`

	AccountId string `title:"推广账号id" json:"account_id"`

	AdAccount string `title:"推广账号名" json:"ad_account"`

	CampaignId string `title:"广告组id" json:"campaign_id"`

	CampaignName string `title:"广告组名" json:"campaign_name"`

	PlanId string `title:"计划id" json:"plan_id"`

	PlanName string `title:"计划名" json:"plan_name"`

	CreativeId string `title:"创意id" json:"creative_id"`

	CreativeName string `title:"创意名" json:"creative_name"`

	Show uint32 `title:"曝光数" json:"show"`

	Click uint32 `title:"点击数" json:"click"`

	Install uint32 `title:"安装数" json:"install"`

	Cost float32 `title:"消耗" json:"cost"`

	CostDiscount float32 `title:"返点后消耗" json:"cost_discount"`

	CostRmb float32 `title:"消耗（rmb）" json:"cost_rmb"`

	CostDiscountRmb float32 `title:"返点后消耗（rmb）" json:"cost_discount_rmb"`

	CountryCode string `title:"国家code" json:"country_code"`

	TimeZone string `title:"时区名称,utc-8,gmt-3" json:"time_zone"`

	EffectiveStatus int `title:"有效状态 0 正常 1已归档或者已删除" json:"effective_status"`

	PlatformId int `title:"1安卓2苹果" json:"platform_id"`

	Ctr float32 `title:"ctr" json:"ctr"`

	Cpm float32 `title:"cpm" json:"cpm"`

	InstallCost float32 `title:"媒体安装成本=install/spend" json:"install_cost"`

	ClickInstallRate float32 `title:"点击安装率=install/clicks" json:"click_install_rate"`

	InstallPerShow float32 `title:"千次曝光安装量=(install/impressions)*1000" json:"install_per_show"`

	CreativeIdOrigin string `title:"creative_id" json:"creative_id_origin"`

	SourceId int `title:"关联base_conf_platform.hw_base_source_conf" json:"source_id"`

	CreativeIdMd5 string `title:"creative_id_md5" json:"creative_id_md5"`
}
