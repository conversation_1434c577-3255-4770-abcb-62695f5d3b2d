package hw_sdk_user_active

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("hw_sdk_user_active")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增ID
	Id = property.Property("id")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏ID
	GameId = property.Property("game_id")
	// PackageId 游戏包ID
	PackageId = property.Property("package_id")
	// LoginAccount 登录账号
	LoginAccount = property.Property("login_account")
	// CoreAccount 用户ID(核心账号)
	CoreAccount = property.Property("core_account")
	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv = property.Property("aid_or_idfv")
	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa = property.Property("gid_or_idfa")
	// Tday 行为发生日期
	Tday = property.Property("tday")
	// LocalTday 本地行为发生日期
	LocalTday = property.Property("local_tday")
	// DeviceCode md5(MAC+IMEI+IMSI)
	DeviceCode = property.Property("device_code")
	// Useragent 一个特殊字符串头,识别客户信息
	Useragent = property.Property("useragent")
	// Os 操作系统类型:1-android,2-iOS,3-WINPHONE
	Os = property.Property("os")
	// Ip ip地址:原生IP码
	Ip = property.Property("ip")
	// Country 投放国家(地区)
	Country = property.Property("country")
	// Lbs 经纬度:经度x纬度
	Lbs = property.Property("lbs")
	// DeviceLanguage 设备语言
	DeviceLanguage = property.Property("device_language")
	// SourceId 媒体ID
	SourceId = property.Property("source_id")
	// ClickId 点击ID
	ClickId = property.Property("click_id")
	// CreativeId 创意ID
	CreativeId = property.Property("creative_id")
	// Mac MAC
	Mac = property.Property("mac")
	// AdjustId adjust_id
	AdjustId = property.Property("adjust_id")
	// TimeBj 行为发生北京时间
	TimeBj = property.Property("time_bj")
)

var FieldsAll = Fields(Id, CpGameId, GameId, PackageId, LoginAccount, CoreAccount, AidOrIdfv, GidOrIdfa, Tday, LocalTday, DeviceCode, Useragent, Os, Ip, Country, Lbs, DeviceLanguage, SourceId, ClickId, CreativeId, Mac, AdjustId, TimeBj)
var NonePrimaryFields = Fields(CpGameId, GameId, PackageId, LoginAccount, CoreAccount, AidOrIdfv, GidOrIdfa, Tday, LocalTday, DeviceCode, Useragent, Os, Ip, Country, Lbs, DeviceLanguage, SourceId, ClickId, CreativeId, Mac, AdjustId, TimeBj)
var NoneAutoIncrementFields = Fields(CpGameId, GameId, PackageId, LoginAccount, CoreAccount, AidOrIdfv, GidOrIdfa, Tday, LocalTday, DeviceCode, Useragent, Os, Ip, Country, Lbs, DeviceLanguage, SourceId, ClickId, CreativeId, Mac, AdjustId, TimeBj)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// HwSdkUserActiveModel 游戏用户活跃日志
type HwSdkUserActiveModel struct {
	// Id 自增ID
	Id uint32 `orm:"id" json:"id"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏ID
	GameId int `orm:"game_id" json:"game_id"`

	// PackageId 游戏包ID
	PackageId int `orm:"package_id" json:"package_id"`

	// LoginAccount 登录账号
	LoginAccount string `orm:"login_account" json:"login_account"`

	// CoreAccount 用户ID(核心账号)
	CoreAccount string `orm:"core_account" json:"core_account"`

	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv string `orm:"aid_or_idfv" json:"aid_or_idfv"`

	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa string `orm:"gid_or_idfa" json:"gid_or_idfa"`

	// Tday 行为发生日期
	Tday CustomTime `orm:"tday" json:"tday"`

	// LocalTday 本地行为发生日期
	LocalTday CustomTime `orm:"local_tday" json:"local_tday"`

	// DeviceCode md5(MAC+IMEI+IMSI)
	DeviceCode string `orm:"device_code" json:"device_code"`

	// Useragent 一个特殊字符串头,识别客户信息
	Useragent string `orm:"useragent" json:"useragent"`

	// Os 操作系统类型:1-android,2-iOS,3-WINPHONE
	Os string `orm:"os" json:"os"`

	// Ip ip地址:原生IP码
	Ip string `orm:"ip" json:"ip"`

	// Country 投放国家(地区)
	Country string `orm:"country" json:"country"`

	// Lbs 经纬度:经度x纬度
	Lbs string `orm:"lbs" json:"lbs"`

	// DeviceLanguage 设备语言
	DeviceLanguage string `orm:"device_language" json:"device_language"`

	// SourceId 媒体ID
	SourceId uint32 `orm:"source_id" json:"source_id"`

	// ClickId 点击ID
	ClickId int `orm:"click_id" json:"click_id"`

	// CreativeId 创意ID
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// Mac MAC
	Mac string `orm:"mac" json:"mac"`

	// AdjustId adjust_id
	AdjustId string `orm:"adjust_id" json:"adjust_id"`

	// TimeBj 行为发生北京时间
	TimeBj CustomTime `orm:"time_bj" json:"time_bj"`
}

type PagedResult struct {
	Records      []*HwSdkUserActiveModel `json:"list"`
	PageNum      int                     `json:"page"`
	PageSize     int                     `json:"page_size"`
	TotalPages   int                     `json:"total_pages"`
	TotalRecords int                     `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:             1,
	CpGameId:       2,
	GameId:         3,
	PackageId:      4,
	LoginAccount:   5,
	CoreAccount:    6,
	AidOrIdfv:      7,
	GidOrIdfa:      8,
	Tday:           9,
	LocalTday:      10,
	DeviceCode:     11,
	Useragent:      12,
	Os:             13,
	Ip:             14,
	Country:        15,
	Lbs:            16,
	DeviceLanguage: 17,
	SourceId:       18,
	ClickId:        19,
	CreativeId:     20,
	Mac:            21,
	AdjustId:       22,
	TimeBj:         23,
}

func (m *HwSdkUserActiveModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *HwSdkUserActiveModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.CpGameId, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.Tday, m.LocalTday, m.DeviceCode, m.Useragent, m.Os, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.SourceId, m.ClickId, m.CreativeId, m.Mac, m.AdjustId, m.TimeBj)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = uint32(lastInsertId)

	return nil

}

func (m *HwSdkUserActiveModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.Tday, m.LocalTday, m.DeviceCode, m.Useragent, m.Os, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.SourceId, m.ClickId, m.CreativeId, m.Mac, m.AdjustId, m.TimeBj)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *HwSdkUserActiveModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *HwSdkUserActiveModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.Tday, m.LocalTday, m.DeviceCode, m.Useragent, m.Os, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.SourceId, m.ClickId, m.CreativeId, m.Mac, m.AdjustId, m.TimeBj)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkUserActiveModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *HwSdkUserActiveModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.Tday, m.LocalTday, m.DeviceCode, m.Useragent, m.Os, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.SourceId, m.ClickId, m.CreativeId, m.Mac, m.AdjustId, m.TimeBj), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkUserActiveModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *HwSdkUserActiveModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.CpGameId, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.Tday, m.LocalTday, m.DeviceCode, m.Useragent, m.Os, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.SourceId, m.ClickId, m.CreativeId, m.Mac, m.AdjustId, m.TimeBj), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.GameId, m.PackageId, m.LoginAccount, m.CoreAccount, m.AidOrIdfv, m.GidOrIdfa, m.Tday, m.LocalTday, m.DeviceCode, m.Useragent, m.Os, m.Ip, m.Country, m.Lbs, m.DeviceLanguage, m.SourceId, m.ClickId, m.CreativeId, m.Mac, m.AdjustId, m.TimeBj))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwSdkUserActiveModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *HwSdkUserActiveModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *HwSdkUserActiveModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *HwSdkUserActiveModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *HwSdkUserActiveModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *HwSdkUserActiveModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*HwSdkUserActiveModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*HwSdkUserActiveModel, error) {

	modelList := make([]*HwSdkUserActiveModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkUserActiveModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.Tday, &m.LocalTday, &m.DeviceCode, &m.Useragent, &m.Os, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.SourceId, &m.ClickId, &m.CreativeId, &m.Mac, &m.AdjustId, &m.TimeBj)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint32) (*HwSdkUserActiveModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint32) (*HwSdkUserActiveModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint32) (*HwSdkUserActiveModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint32) (*HwSdkUserActiveModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*HwSdkUserActiveModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*HwSdkUserActiveModel, error) {

	m := &HwSdkUserActiveModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.Tday, &m.LocalTday, &m.DeviceCode, &m.Useragent, &m.Os, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.SourceId, &m.ClickId, &m.CreativeId, &m.Mac, &m.AdjustId, &m.TimeBj)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*HwSdkUserActiveModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkUserActiveModel, error) {

	modelList := make([]*HwSdkUserActiveModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwSdkUserActiveModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.Tday, &m.LocalTday, &m.DeviceCode, &m.Useragent, &m.Os, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.SourceId, &m.ClickId, &m.CreativeId, &m.Mac, &m.AdjustId, &m.TimeBj)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*HwSdkUserActiveModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*HwSdkUserActiveModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &HwSdkUserActiveModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.GameId, &m.PackageId, &m.LoginAccount, &m.CoreAccount, &m.AidOrIdfv, &m.GidOrIdfa, &m.Tday, &m.LocalTday, &m.DeviceCode, &m.Useragent, &m.Os, &m.Ip, &m.Country, &m.Lbs, &m.DeviceLanguage, &m.SourceId, &m.ClickId, &m.CreativeId, &m.Mac, &m.AdjustId, &m.TimeBj)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint32) (*HwSdkUserActiveModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint32) (*HwSdkUserActiveModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*HwSdkUserActiveModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*HwSdkUserActiveModel, error) {

	m := &HwSdkUserActiveModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*HwSdkUserActiveModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*HwSdkUserActiveModel, error) {

	modelList := make([]*HwSdkUserActiveModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &HwSdkUserActiveModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*HwSdkUserActiveModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*HwSdkUserActiveModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*HwSdkUserActiveModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*HwSdkUserActiveModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.CpGameId, d.GameId, d.PackageId, d.LoginAccount, d.CoreAccount, d.AidOrIdfv, d.GidOrIdfa, d.Tday, d.LocalTday, d.DeviceCode, d.Useragent, d.Os, d.Ip, d.Country, d.Lbs, d.DeviceLanguage, d.SourceId, d.ClickId, d.CreativeId, d.Mac, d.AdjustId, d.TimeBj))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增ID",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "游戏包ID",
		},
		{
			"dataIndex": "login_account",
			"title":     "登录账号",
		},
		{
			"dataIndex": "core_account",
			"title":     "用户ID(核心账号)",
		},
		{
			"dataIndex": "aid_or_idfv",
			"title":     "安卓ID or idfv",
		},
		{
			"dataIndex": "gid_or_idfa",
			"title":     "谷歌ID or 苹果ID",
		},
		{
			"dataIndex": "tday",
			"title":     "行为发生日期",
		},
		{
			"dataIndex": "local_tday",
			"title":     "本地行为发生日期",
		},
		{
			"dataIndex": "device_code",
			"title":     "md5(MAC+IMEI+IMSI)",
		},
		{
			"dataIndex": "useragent",
			"title":     "一个特殊字符串头,识别客户信息",
		},
		{
			"dataIndex": "os",
			"title":     "操作系统类型:1-android,2-iOS,3-WINPHONE",
		},
		{
			"dataIndex": "ip",
			"title":     "ip地址:原生IP码",
		},
		{
			"dataIndex": "country",
			"title":     "投放国家(地区)",
		},
		{
			"dataIndex": "lbs",
			"title":     "经纬度:经度x纬度",
		},
		{
			"dataIndex": "device_language",
			"title":     "设备语言",
		},
		{
			"dataIndex": "source_id",
			"title":     "媒体ID",
		},
		{
			"dataIndex": "click_id",
			"title":     "点击ID",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID",
		},
		{
			"dataIndex": "mac",
			"title":     "MAC",
		},
		{
			"dataIndex": "adjust_id",
			"title":     "adjust_id",
		},
		{
			"dataIndex": "time_bj",
			"title":     "行为发生北京时间",
		}}
}
