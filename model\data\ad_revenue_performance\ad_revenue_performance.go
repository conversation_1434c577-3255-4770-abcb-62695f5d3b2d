package ad_revenue_performance

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_revenue_performance")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// DayNum 对应x天后的值
	DayNum = property.Property("day_num")
	// AdsPubRevenue 安装后x天内通过广告产生的收入
	AdsPubRevenue = property.Property("ads_pub_revenue")
	// AdsRpi 安装后x 天内通过广告产生的每次安装收入
	AdsRpi = property.Property("ads_rpi")
	// Application 	应用程序的名称
	Application = property.Property("application")
	// BannerPubRevenue 安装后x 天内通过横幅广告产生的收入
	BannerPubRevenue = property.Property("banner_pub_revenue")
	// BannerRpi 安装后x 天内横幅广告产生的每次安装收入
	BannerRpi = property.Property("banner_rpi")
	// Country 展示的两字母国家/地区代码
	Country = property.Property("country")
	// Day 日期
	Day = property.Property("day")
	// IapPubRevenue 安装后x 天内横幅广告产生的每次安装收入
	IapPubRevenue = property.Property("iap_pub_revenue")
	// IapRpi 安装后x 天内通过 IAP 产生的每次安装收入
	IapRpi = property.Property("iap_rpi")
	// Installs 新安装者的数量
	Installs = property.Property("installs")
	// InterPubRevenue 安装后x 天内通过（非奖励）插页广告产生的收入
	InterPubRevenue = property.Property("inter_pub_revenue")
	// InterRpi 安装后x 天内通过（非奖励）插页广告产生的每次安装收入。
	InterRpi = property.Property("inter_rpi")
	// MrecPubRevenue 安装后x 天内通过 MREC 广告产生的收入。
	MrecPubRevenue = property.Property("mrec_pub_revenue")
	// MrecRpi 安装后x 天内由 MREC 广告产生的每次安装收入
	MrecRpi = property.Property("mrec_rpi")
	// PackageName 包名
	PackageName = property.Property("package_name")
	// Platform 应用程序平台：android、fireos、 或ios。	android
	Platform = property.Property("platform")
	// PubRevenue 安装后x 天内产生的收入
	PubRevenue = property.Property("pub_revenue")
	// RewardPubRevenue 安装后x 天内通过奖励广告产生的收入
	RewardPubRevenue = property.Property("reward_pub_revenue")
	// RewardRpi 安装后x 天内通过奖励广告产生的每次安装收入
	RewardRpi = property.Property("reward_rpi")
	// Rpi 安装后x 天内产生的每次安装收入
	Rpi = property.Property("rpi")
	// Utime 更新时间
	Utime = property.Property("utime")
)

var FieldsAll = Fields(Id, DayNum, AdsPubRevenue, AdsRpi, Application, BannerPubRevenue, BannerRpi, Country, Day, IapPubRevenue, IapRpi, Installs, InterPubRevenue, InterRpi, MrecPubRevenue, MrecRpi, PackageName, Platform, PubRevenue, RewardPubRevenue, RewardRpi, Rpi, Utime)
var NonePrimaryFields = Fields(DayNum, AdsPubRevenue, AdsRpi, Application, BannerPubRevenue, BannerRpi, Country, Day, IapPubRevenue, IapRpi, Installs, InterPubRevenue, InterRpi, MrecPubRevenue, MrecRpi, PackageName, Platform, PubRevenue, RewardPubRevenue, RewardRpi, Rpi, Utime)
var NoneAutoIncrementFields = Fields(DayNum, AdsPubRevenue, AdsRpi, Application, BannerPubRevenue, BannerRpi, Country, Day, IapPubRevenue, IapRpi, Installs, InterPubRevenue, InterRpi, MrecPubRevenue, MrecRpi, PackageName, Platform, PubRevenue, RewardPubRevenue, RewardRpi, Rpi, Utime)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdRevenuePerformanceModel 广告收入表现表
type AdRevenuePerformanceModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// DayNum 对应x天后的值
	DayNum *float32 `orm:"day_num" json:"day_num"`

	// AdsPubRevenue 安装后x天内通过广告产生的收入
	AdsPubRevenue *float32 `orm:"ads_pub_revenue" json:"ads_pub_revenue"`

	// AdsRpi 安装后x 天内通过广告产生的每次安装收入
	AdsRpi *float32 `orm:"ads_rpi" json:"ads_rpi"`

	// Application 	应用程序的名称
	Application *string `orm:"application" json:"application"`

	// BannerPubRevenue 安装后x 天内通过横幅广告产生的收入
	BannerPubRevenue *float32 `orm:"banner_pub_revenue" json:"banner_pub_revenue"`

	// BannerRpi 安装后x 天内横幅广告产生的每次安装收入
	BannerRpi *float32 `orm:"banner_rpi" json:"banner_rpi"`

	// Country 展示的两字母国家/地区代码
	Country *string `orm:"country" json:"country"`

	// Day 日期
	Day *CustomTime `orm:"day" json:"day"`

	// IapPubRevenue 安装后x 天内横幅广告产生的每次安装收入
	IapPubRevenue *float32 `orm:"iap_pub_revenue" json:"iap_pub_revenue"`

	// IapRpi 安装后x 天内通过 IAP 产生的每次安装收入
	IapRpi *float32 `orm:"iap_rpi" json:"iap_rpi"`

	// Installs 新安装者的数量
	Installs *uint32 `orm:"installs" json:"installs"`

	// InterPubRevenue 安装后x 天内通过（非奖励）插页广告产生的收入
	InterPubRevenue *uint32 `orm:"inter_pub_revenue" json:"inter_pub_revenue"`

	// InterRpi 安装后x 天内通过（非奖励）插页广告产生的每次安装收入。
	InterRpi *uint32 `orm:"inter_rpi" json:"inter_rpi"`

	// MrecPubRevenue 安装后x 天内通过 MREC 广告产生的收入。
	MrecPubRevenue *uint32 `orm:"mrec_pub_revenue" json:"mrec_pub_revenue"`

	// MrecRpi 安装后x 天内由 MREC 广告产生的每次安装收入
	MrecRpi *float32 `orm:"mrec_rpi" json:"mrec_rpi"`

	// PackageName 包名
	PackageName *string `orm:"package_name" json:"package_name"`

	// Platform 应用程序平台：android、fireos、 或ios。	android
	Platform *string `orm:"platform" json:"platform"`

	// PubRevenue 安装后x 天内产生的收入
	PubRevenue *float32 `orm:"pub_revenue" json:"pub_revenue"`

	// RewardPubRevenue 安装后x 天内通过奖励广告产生的收入
	RewardPubRevenue *float32 `orm:"reward_pub_revenue" json:"reward_pub_revenue"`

	// RewardRpi 安装后x 天内通过奖励广告产生的每次安装收入
	RewardRpi *float32 `orm:"reward_rpi" json:"reward_rpi"`

	// Rpi 安装后x 天内产生的每次安装收入
	Rpi *float32 `orm:"rpi" json:"rpi"`

	// Utime 更新时间
	Utime *CustomTime `orm:"utime" json:"utime"`
}

type PagedResult struct {
	Records      []*AdRevenuePerformanceModel `json:"list"`
	PageNum      int                          `json:"page"`
	PageSize     int                          `json:"page_size"`
	TotalPages   int                          `json:"total_pages"`
	TotalRecords int                          `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:               1,
	DayNum:           2,
	AdsPubRevenue:    3,
	AdsRpi:           4,
	Application:      5,
	BannerPubRevenue: 6,
	BannerRpi:        7,
	Country:          8,
	Day:              9,
	IapPubRevenue:    10,
	IapRpi:           11,
	Installs:         12,
	InterPubRevenue:  13,
	InterRpi:         14,
	MrecPubRevenue:   15,
	MrecRpi:          16,
	PackageName:      17,
	Platform:         18,
	PubRevenue:       19,
	RewardPubRevenue: 20,
	RewardRpi:        21,
	Rpi:              22,
	Utime:            23,
}

func (m *AdRevenuePerformanceModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdRevenuePerformanceModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.DayNum, m.AdsPubRevenue, m.AdsRpi, m.Application, m.BannerPubRevenue, m.BannerRpi, m.Country, m.Day, m.IapPubRevenue, m.IapRpi, m.Installs, m.InterPubRevenue, m.InterRpi, m.MrecPubRevenue, m.MrecRpi, m.PackageName, m.Platform, m.PubRevenue, m.RewardPubRevenue, m.RewardRpi, m.Rpi, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdRevenuePerformanceModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.DayNum, m.AdsPubRevenue, m.AdsRpi, m.Application, m.BannerPubRevenue, m.BannerRpi, m.Country, m.Day, m.IapPubRevenue, m.IapRpi, m.Installs, m.InterPubRevenue, m.InterRpi, m.MrecPubRevenue, m.MrecRpi, m.PackageName, m.Platform, m.PubRevenue, m.RewardPubRevenue, m.RewardRpi, m.Rpi, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdRevenuePerformanceModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdRevenuePerformanceModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.DayNum, m.AdsPubRevenue, m.AdsRpi, m.Application, m.BannerPubRevenue, m.BannerRpi, m.Country, m.Day, m.IapPubRevenue, m.IapRpi, m.Installs, m.InterPubRevenue, m.InterRpi, m.MrecPubRevenue, m.MrecRpi, m.PackageName, m.Platform, m.PubRevenue, m.RewardPubRevenue, m.RewardRpi, m.Rpi, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdRevenuePerformanceModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdRevenuePerformanceModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.DayNum, m.AdsPubRevenue, m.AdsRpi, m.Application, m.BannerPubRevenue, m.BannerRpi, m.Country, m.Day, m.IapPubRevenue, m.IapRpi, m.Installs, m.InterPubRevenue, m.InterRpi, m.MrecPubRevenue, m.MrecRpi, m.PackageName, m.Platform, m.PubRevenue, m.RewardPubRevenue, m.RewardRpi, m.Rpi, m.Utime), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdRevenuePerformanceModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdRevenuePerformanceModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.DayNum, m.AdsPubRevenue, m.AdsRpi, m.Application, m.BannerPubRevenue, m.BannerRpi, m.Country, m.Day, m.IapPubRevenue, m.IapRpi, m.Installs, m.InterPubRevenue, m.InterRpi, m.MrecPubRevenue, m.MrecRpi, m.PackageName, m.Platform, m.PubRevenue, m.RewardPubRevenue, m.RewardRpi, m.Rpi, m.Utime), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.DayNum, m.AdsPubRevenue, m.AdsRpi, m.Application, m.BannerPubRevenue, m.BannerRpi, m.Country, m.Day, m.IapPubRevenue, m.IapRpi, m.Installs, m.InterPubRevenue, m.InterRpi, m.MrecPubRevenue, m.MrecRpi, m.PackageName, m.Platform, m.PubRevenue, m.RewardPubRevenue, m.RewardRpi, m.Rpi, m.Utime))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdRevenuePerformanceModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdRevenuePerformanceModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdRevenuePerformanceModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdRevenuePerformanceModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdRevenuePerformanceModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdRevenuePerformanceModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdRevenuePerformanceModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdRevenuePerformanceModel, error) {

	modelList := make([]*AdRevenuePerformanceModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdRevenuePerformanceModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.AdsPubRevenue, &m.AdsRpi, &m.Application, &m.BannerPubRevenue, &m.BannerRpi, &m.Country, &m.Day, &m.IapPubRevenue, &m.IapRpi, &m.Installs, &m.InterPubRevenue, &m.InterRpi, &m.MrecPubRevenue, &m.MrecRpi, &m.PackageName, &m.Platform, &m.PubRevenue, &m.RewardPubRevenue, &m.RewardRpi, &m.Rpi, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdRevenuePerformanceModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdRevenuePerformanceModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdRevenuePerformanceModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdRevenuePerformanceModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdRevenuePerformanceModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdRevenuePerformanceModel, error) {

	m := &AdRevenuePerformanceModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.AdsPubRevenue, &m.AdsRpi, &m.Application, &m.BannerPubRevenue, &m.BannerRpi, &m.Country, &m.Day, &m.IapPubRevenue, &m.IapRpi, &m.Installs, &m.InterPubRevenue, &m.InterRpi, &m.MrecPubRevenue, &m.MrecRpi, &m.PackageName, &m.Platform, &m.PubRevenue, &m.RewardPubRevenue, &m.RewardRpi, &m.Rpi, &m.Utime)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdRevenuePerformanceModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdRevenuePerformanceModel, error) {

	modelList := make([]*AdRevenuePerformanceModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdRevenuePerformanceModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.AdsPubRevenue, &m.AdsRpi, &m.Application, &m.BannerPubRevenue, &m.BannerRpi, &m.Country, &m.Day, &m.IapPubRevenue, &m.IapRpi, &m.Installs, &m.InterPubRevenue, &m.InterRpi, &m.MrecPubRevenue, &m.MrecRpi, &m.PackageName, &m.Platform, &m.PubRevenue, &m.RewardPubRevenue, &m.RewardRpi, &m.Rpi, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdRevenuePerformanceModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdRevenuePerformanceModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdRevenuePerformanceModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.AdsPubRevenue, &m.AdsRpi, &m.Application, &m.BannerPubRevenue, &m.BannerRpi, &m.Country, &m.Day, &m.IapPubRevenue, &m.IapRpi, &m.Installs, &m.InterPubRevenue, &m.InterRpi, &m.MrecPubRevenue, &m.MrecRpi, &m.PackageName, &m.Platform, &m.PubRevenue, &m.RewardPubRevenue, &m.RewardRpi, &m.Rpi, &m.Utime)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdRevenuePerformanceModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdRevenuePerformanceModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdRevenuePerformanceModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdRevenuePerformanceModel, error) {

	m := &AdRevenuePerformanceModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdRevenuePerformanceModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdRevenuePerformanceModel, error) {

	modelList := make([]*AdRevenuePerformanceModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdRevenuePerformanceModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdRevenuePerformanceModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdRevenuePerformanceModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdRevenuePerformanceModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdRevenuePerformanceModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.DayNum, d.AdsPubRevenue, d.AdsRpi, d.Application, d.BannerPubRevenue, d.BannerRpi, d.Country, d.Day, d.IapPubRevenue, d.IapRpi, d.Installs, d.InterPubRevenue, d.InterRpi, d.MrecPubRevenue, d.MrecRpi, d.PackageName, d.Platform, d.PubRevenue, d.RewardPubRevenue, d.RewardRpi, d.Rpi, d.Utime))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "day_num",
			"title":     "对应x天后的值",
		},
		{
			"dataIndex": "ads_pub_revenue",
			"title":     "安装后x天内通过广告产生的收入",
		},
		{
			"dataIndex": "ads_rpi",
			"title":     "安装后x 天内通过广告产生的每次安装收入",
		},
		{
			"dataIndex": "application",
			"title":     "	应用程序的名称",
		},
		{
			"dataIndex": "banner_pub_revenue",
			"title":     "安装后x 天内通过横幅广告产生的收入",
		},
		{
			"dataIndex": "banner_rpi",
			"title":     "安装后x 天内横幅广告产生的每次安装收入",
		},
		{
			"dataIndex": "country",
			"title":     "展示的两字母国家/地区代码",
		},
		{
			"dataIndex": "day",
			"title":     "日期",
		},
		{
			"dataIndex": "iap_pub_revenue",
			"title":     "安装后x 天内横幅广告产生的每次安装收入",
		},
		{
			"dataIndex": "iap_rpi",
			"title":     "安装后x 天内通过 IAP 产生的每次安装收入",
		},
		{
			"dataIndex": "installs",
			"title":     "新安装者的数量",
		},
		{
			"dataIndex": "inter_pub_revenue",
			"title":     "安装后x 天内通过（非奖励）插页广告产生的收入",
		},
		{
			"dataIndex": "inter_rpi",
			"title":     "安装后x 天内通过（非奖励）插页广告产生的每次安装收入。",
		},
		{
			"dataIndex": "mrec_pub_revenue",
			"title":     "安装后x 天内通过 MREC 广告产生的收入。",
		},
		{
			"dataIndex": "mrec_rpi",
			"title":     "安装后x 天内由 MREC 广告产生的每次安装收入",
		},
		{
			"dataIndex": "package_name",
			"title":     "包名",
		},
		{
			"dataIndex": "platform",
			"title":     "应用程序平台：android、fireos、 或ios。	android",
		},
		{
			"dataIndex": "pub_revenue",
			"title":     "安装后x 天内产生的收入",
		},
		{
			"dataIndex": "reward_pub_revenue",
			"title":     "安装后x 天内通过奖励广告产生的收入",
		},
		{
			"dataIndex": "reward_rpi",
			"title":     "安装后x 天内通过奖励广告产生的每次安装收入",
		},
		{
			"dataIndex": "rpi",
			"title":     "安装后x 天内产生的每次安装收入",
		},
		{
			"dataIndex": "utime",
			"title":     "更新时间",
		}}
}
