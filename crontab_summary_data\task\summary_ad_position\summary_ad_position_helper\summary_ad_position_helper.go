package summary_ad_position_helper

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils"
	"iaa_data/utils/constant"
	"iaa_data/utils/country_helper"
	"iaa_data/utils/game_helper"
	"iaa_data/utils/sql_helper"
	"log"
	"strings"
	"time"
)

var keyValMap map[string]string
var addList []string

func SummaryReport(startTime, endTime, timeZoneStr string) {
	if !utils.IsTimeZone(timeZoneStr) {
		return
	}
	startDate, err1 := utils.ParseBjTime(time.DateOnly, startTime)
	if err1 != nil {
		log.Println("时间格式有误startTime")
		return
	}
	endDate, err2 := utils.ParseBjTime(time.DateOnly, endTime)
	if err2 != nil {
		log.Println("时间格式有误endTime")
		return
	}

	for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {

		timeVal := currentDate.Format("2006-01-02")
		sTime := timeVal + " 00:00:00"
		eTime := timeVal + " 23:59:59"
		if timeZoneStr == constant.TIME_ERU {
			sTime = utils.ERUtoBjTime(sTime)
			eTime = utils.ERUtoBjTime(eTime)
		} else if timeZoneStr == constant.TIME_US {
			sTime = utils.UStoBjTime(sTime)
			eTime = utils.UStoBjTime(eTime)
		}

		tday := currentDate.Format("********")
		timeZone := utils.GeTimeZoneTag(timeZoneStr)

		addList = nil
		keyValMap = nil
		keyValMap = sql_helper.GetUniqueLogs(tday, fmt.Sprint(timeZone), "iaa_ad_position_report")

		delSql := "delete FROM iaa_ad_position_report where tday=" + tday + " and time_zone = " + fmt.Sprint(timeZone)
		_, errDel := plusQ.Db().Delete(delSql)
		if errDel != nil {
			log.Println("删除失败", errDel)
		}

		summaryAdNum(sTime, eTime, timeZoneStr, tday, "hw_sdk_ad_revenue_log", "show_num")
		summaryAdNum(sTime, eTime, timeZoneStr, tday, "hw_sdk_ad_click_log", "click_num")
		summaryWatchNum(sTime, eTime, timeZoneStr, tday, "deu")
		summaryAdRevenue(sTime, eTime, timeZoneStr, tday, "estimate_revenue")

		if len(addList) > 0 {
			for _, keyVal := range addList {
				delete(keyValMap, keyVal)
				info := make(map[string]interface{})
				info["key_val"] = keyVal
				info["table_name"] = "iaa_ad_position_report"
				info["tday"] = tday
				info["time_zone"] = timeZone
				info["update_time"] = time.Now().Format("2006-01-02 15:04:05")
				sql_helper.LogHelperInstance().HandleDataMapInsert("data", "iaa_report_unique_log", "iaa_report_unique_log", info, 30, 2)
			}
			sql_helper.LogHelperInstance().HandleDataMapEnd("data", "iaa_report_unique_log", "iaa_report_unique_log")
		}
		if len(keyValMap) > 0 {
			for _, keyVal := range keyValMap {
				keyVals := strings.Split(keyVal, "||")
				if len(keyVals) == 6 {
					//`time_zone`,`tday`,`game_id`,`country_id`,`package_id`,`placement`
					info := make(map[string]interface{})
					info["time_zone"] = keyVals[0]
					info["tday"] = keyVals[1]
					info["game_id"] = keyVals[2]
					info["country_id"] = keyVals[3]
					info["package_id"] = keyVals[4]
					info["placement"] = keyVals[5]
					cpGameId := game_helper.GameHelInstant().GetCpGameId(utils.AnyToInt(keyVals[2]))
					channelId := game_helper.GameHelInstant().GetChannelId(utils.AnyToInt(keyVals[4]))
					info["cp_game_id"] = cpGameId
					info["channel_id"] = channelId

					info["show_num"] = 0
					info["click_num"] = 0
					info["deu"] = 0
					info["dau"] = 0
					info["real_revenue"] = 0
					info["estimate_revenue"] = 0
					info["revenue"] = 0
					info["iap_pay_money"] = 0
					info["update_time"] = time.Now().Format("2006-01-02 15:04:05")
					err := sql_helper.InsertOrUpdateAllFiledSummary("", "iaa_ad_position_report", "time_zone|tday|game_id|country_id|package_id|placement", info)
					if err != nil {
						log.Println("更新置0失败 iaa_ad_roi_realtime:", err)
					}
				}
			}
		}
	}
}

func summaryAdNum(start, end, timeZoneStr, tday, tableName, numTag string) {
	list, err := getResultList(start, end, tableName)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, numTag, list)
	}
}

func summaryWatchNum(start, end, timeZoneStr, tday, numTag string) {
	list, err := getAdWatchList(start, end)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, numTag, list)
	}
}

func summaryAdRevenue(start, end, timeZoneStr, tday, numTag string) {
	list, err := getAdRevenueList(start, end)
	if err == nil {
		summaryCommonNum(timeZoneStr, tday, numTag, list)
	}
}

func summaryCommonNum(timeZoneStr, tday, numTag string, list []map[string]interface{}) {

	for _, v := range list {
		gameId := fmt.Sprint(v["game_id"])
		cpGameId := fmt.Sprint(v["cp_game_id"])
		country := fmt.Sprint(v["country"])
		package_id := fmt.Sprint(v["package_id"])
		placement := fmt.Sprint(v["placement"])
		total_count := fmt.Sprint(v["total_count"])
		country_id := country_helper.CountryInstant().GetCountryId(country)
		//if country_id == 0 {
		//	continue
		//}

		timeZone := utils.GeTimeZoneTag(timeZoneStr)

		info := make(map[string]interface{})
		info["time_zone"] = timeZone
		info["tday"] = tday
		info["game_id"] = gameId
		info["cp_game_id"] = cpGameId
		info["country_id"] = country_id
		info["package_id"] = package_id
		info["placement"] = placement
		info["update_time"] = time.Now().Format(time.DateTime)
		channelId := game_helper.GameHelInstant().GetChannelId(utils.AnyToInt(package_id))
		info["channel_id"] = channelId
		info["platform"] = v["os"]
		if numTag == "estimate_revenue" {
			lastrevenue := lastRevenue(fmt.Sprint(timeZone), tday, gameId, fmt.Sprint(country_id), package_id, placement)
			val1, _ := utils.StringToFloat32(total_count)
			val2, _ := utils.StringToFloat32(fmt.Sprint(lastrevenue))
			allVal := val1 + val2
			info["revenue"] = allVal
		}

		info[numTag] = total_count

		//`time_zone`,`tday`,`game_id`,`country_id`,`package_id`,`placement`
		keyVal := fmt.Sprint(timeZone) + "||" + tday + "||" + gameId + "||" + fmt.Sprint(country_id) + "||" + package_id + "||" + placement
		if !sql_helper.IsHaveKey(addList, keyVal) {
			addList = append(addList, keyVal)
		}
		err := sql_helper.InsertOrUpdateAllFiledSummary("", "iaa_ad_position_report", "time_zone|tday|game_id|country_id|package_id|placement", info)
		if err != nil {
			log.Println("insert err:", err)
		}
	}
}

func getResultList(start, end, tableName string) ([]map[string]interface{}, error) {
	selectSql := "SELECT  game_id,cp_game_id, country,  package_id, placement,os,  COUNT(*) AS total_count " +
		" FROM " + tableName +
		" where time_bj between '" + start + "' and '" + end + "'"
	if tableName == "hw_sdk_ad_revenue_log" {
		selectSql = selectSql + " and revenue > 0 "
	}

	groupSql := " GROUP BY   game_id,country, package_id, placement "
	selectSql = selectSql + groupSql

	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("====hw_sdk_ad_revenue_log", selectSql)

	return list, err
}

func getAdWatchList(start, end string) ([]map[string]interface{}, error) {
	selectSql := "select game_id,cp_game_id, country, package_id,placement,os," +
		" count(distinct(core_account)) as total_count " +
		" FROM hw_sdk_ad_revenue_log" +
		" where time_bj between '" + start + "' and '" + end + "'"
	selectSql = selectSql + " and revenue > 0 "
	selectSql = selectSql + " GROUP BY game_id,country, package_id, placement"
	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("========ss", selectSql)
	return list, err
}

func getAdRevenueList(start, end string) ([]map[string]interface{}, error) {
	selectSql := "SELECT cp_game_id,game_id,country,package_id,placement,os," +
		" sum(revenue) as total_count" +
		" FROM hw_sdk_ad_revenue_log " +
		" where time_bj between '" + start + "' and '" + end + "'"
	selectSql = selectSql + " group by game_id,package_id,country,placement"
	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("getAdRevenueList=1", selectSql)
	return list, err
}

func lastRevenue(time_zone, tday, game_id, country_id, package_id, placement string) interface{} {
	mT, _ := time.Parse("********", tday)
	mT = mT.Add(-24 * time.Hour)
	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql := "select " +
		" revenue " +
		" FROM iaa_ad_position_report " +
		" where tday=" + mT.Format("********") +
		" and time_zone=" + time_zone +
		" and game_id=" + game_id +
		" and country_id=" + country_id +
		" and placement='" + placement + "'" +
		" and package_id=" + package_id

	selectSql = selectSql

	info, err := plusQ.Db().Get(selectSql)
	//log.Println(selectSql)

	if err == nil {
		return info["revenue"]
	}
	return "0"
}
