package ad_cost_conf

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_cost_conf")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// ChannelId 推广渠道id
	ChannelId = property.Property("channel_id")
	// AgentId 代理id
	AgentId = property.Property("agent_id")
	// OperateId 运营方式id
	OperateId = property.Property("operate_id")
	// ExplainId 区分说明id
	ExplainId = property.Property("explain_id")
	// StartTime 返点生效日期（添加时间后一天）
	StartTime = property.Property("start_time")
	// EndTime 返点失效日期
	EndTime = property.Property("end_time")
	// Rebate 返点
	Rebate = property.Property("rebate")
	// Remark 备注
	Remark = property.Property("remark")
	// Operator 创建人
	Operator = property.Property("operator")
	// UpdateTime 修改时间
	UpdateTime = property.Property("update_time")
)

var FieldsAll = Fields(Id, ChannelId, AgentId, OperateId, ExplainId, StartTime, EndTime, Rebate, Remark, Operator, UpdateTime)
var NonePrimaryFields = Fields(ChannelId, AgentId, OperateId, ExplainId, StartTime, EndTime, Rebate, Remark, Operator, UpdateTime)
var NoneAutoIncrementFields = Fields(ChannelId, AgentId, OperateId, ExplainId, StartTime, EndTime, Rebate, Remark, Operator, UpdateTime)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdCostConfModel 广告投放返点配置表
type AdCostConfModel struct {
	// Id 自增id
	Id uint32 `orm:"id" json:"id"`

	// ChannelId 推广渠道id
	ChannelId uint32 `orm:"channel_id" json:"channel_id"`

	// AgentId 代理id
	AgentId uint32 `orm:"agent_id" json:"agent_id"`

	// OperateId 运营方式id
	OperateId uint32 `orm:"operate_id" json:"operate_id"`

	// ExplainId 区分说明id
	ExplainId uint32 `orm:"explain_id" json:"explain_id"`

	// StartTime 返点生效日期（添加时间后一天）
	StartTime CustomTime `orm:"start_time" json:"start_time"`

	// EndTime 返点失效日期
	EndTime CustomTime `orm:"end_time" json:"end_time"`

	// Rebate 返点
	Rebate float32 `orm:"rebate" json:"rebate"`

	// Remark 备注
	Remark *string `orm:"remark" json:"remark"`

	// Operator 创建人
	Operator string `orm:"operator" json:"operator"`

	// UpdateTime 修改时间
	UpdateTime CustomTime `orm:"update_time" json:"update_time"`
}

type PagedResult struct {
	Records      []*AdCostConfModel `json:"list"`
	PageNum      int                `json:"page"`
	PageSize     int                `json:"page_size"`
	TotalPages   int                `json:"total_pages"`
	TotalRecords int                `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:         1,
	ChannelId:  2,
	AgentId:    3,
	OperateId:  4,
	ExplainId:  5,
	StartTime:  6,
	EndTime:    7,
	Rebate:     8,
	Remark:     9,
	Operator:   10,
	UpdateTime: 11,
}

func (m *AdCostConfModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdCostConfModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.ChannelId, m.AgentId, m.OperateId, m.ExplainId, m.StartTime, m.EndTime, m.Rebate, m.Remark, m.Operator, m.UpdateTime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = uint32(lastInsertId)

	return nil

}

func (m *AdCostConfModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.ChannelId, m.AgentId, m.OperateId, m.ExplainId, m.StartTime, m.EndTime, m.Rebate, m.Remark, m.Operator, m.UpdateTime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *AdCostConfModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdCostConfModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.ChannelId, m.AgentId, m.OperateId, m.ExplainId, m.StartTime, m.EndTime, m.Rebate, m.Remark, m.Operator, m.UpdateTime)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostConfModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdCostConfModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.ChannelId, m.AgentId, m.OperateId, m.ExplainId, m.StartTime, m.EndTime, m.Rebate, m.Remark, m.Operator, m.UpdateTime), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostConfModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdCostConfModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.ChannelId, m.AgentId, m.OperateId, m.ExplainId, m.StartTime, m.EndTime, m.Rebate, m.Remark, m.Operator, m.UpdateTime), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.ChannelId, m.AgentId, m.OperateId, m.ExplainId, m.StartTime, m.EndTime, m.Rebate, m.Remark, m.Operator, m.UpdateTime))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostConfModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdCostConfModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdCostConfModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdCostConfModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdCostConfModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdCostConfModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdCostConfModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdCostConfModel, error) {

	modelList := make([]*AdCostConfModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCostConfModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.ChannelId, &m.AgentId, &m.OperateId, &m.ExplainId, &m.StartTime, &m.EndTime, &m.Rebate, &m.Remark, &m.Operator, &m.UpdateTime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint32) (*AdCostConfModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint32) (*AdCostConfModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint32) (*AdCostConfModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint32) (*AdCostConfModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdCostConfModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdCostConfModel, error) {

	m := &AdCostConfModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.ChannelId, &m.AgentId, &m.OperateId, &m.ExplainId, &m.StartTime, &m.EndTime, &m.Rebate, &m.Remark, &m.Operator, &m.UpdateTime)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdCostConfModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdCostConfModel, error) {

	modelList := make([]*AdCostConfModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCostConfModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.ChannelId, &m.AgentId, &m.OperateId, &m.ExplainId, &m.StartTime, &m.EndTime, &m.Rebate, &m.Remark, &m.Operator, &m.UpdateTime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdCostConfModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdCostConfModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdCostConfModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.ChannelId, &m.AgentId, &m.OperateId, &m.ExplainId, &m.StartTime, &m.EndTime, &m.Rebate, &m.Remark, &m.Operator, &m.UpdateTime)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint32) (*AdCostConfModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint32) (*AdCostConfModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdCostConfModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdCostConfModel, error) {

	m := &AdCostConfModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdCostConfModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdCostConfModel, error) {

	modelList := make([]*AdCostConfModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdCostConfModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdCostConfModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdCostConfModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdCostConfModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdCostConfModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.ChannelId, d.AgentId, d.OperateId, d.ExplainId, d.StartTime, d.EndTime, d.Rebate, d.Remark, d.Operator, d.UpdateTime))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "channel_id",
			"title":     "推广渠道id",
		},
		{
			"dataIndex": "agent_id",
			"title":     "代理id",
		},
		{
			"dataIndex": "operate_id",
			"title":     "运营方式id",
		},
		{
			"dataIndex": "explain_id",
			"title":     "区分说明id",
		},
		{
			"dataIndex": "start_time",
			"title":     "返点生效日期（添加时间后一天）",
		},
		{
			"dataIndex": "end_time",
			"title":     "返点失效日期",
		},
		{
			"dataIndex": "rebate",
			"title":     "返点",
		},
		{
			"dataIndex": "remark",
			"title":     "备注",
		},
		{
			"dataIndex": "operator",
			"title":     "创建人",
		},
		{
			"dataIndex": "update_time",
			"title":     "修改时间",
		}}
}
