package cmd

/*
命令执行：/path/to/cli  -op mintegral_grab -start_date 2025-04-28 -end_date 2025-04-28
*/

import (
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/utils/system"
	"crypto/md5"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"iaa_data/cmd/helper"
	"iaa_data/cmd/model/mintegral"
	"iaa_data/model/data/ad_account_conf"
	"iaa_data/model/data/hw_device_active_source"
	"iaa_data/model/platform/game"
	"iaa_data/utils"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

const MINTEGRAL_CHANNEL_ID = 6

type MintegralGrab struct {
}

func (this *MintegralGrab) Run(params map[string]string) error {
	utils.Debug("命令行参数", params)

	var ok bool
	var startDate string
	var endDate string
	startDate, ok = params["start_date"]
	if !ok {
		startDate = time.Now().AddDate(0, 0, -1).Format(time.DateOnly)
	}
	endDate, ok = params["end_date"]
	if !ok {
		endDate = time.Now().Format(time.DateOnly)
	}

	accountId, ok := params["account_id"]
	if !ok {
		accountList, _ := ad_account_conf.ListBy(ad_account_conf.ChannelId.Eq(MINTEGRAL_CHANNEL_ID).And(ad_account_conf.Status.Eq(1)))
		for _, accountInfo := range accountList {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("mintegral_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
		}
	} else {
		accountInfo, _ := ad_account_conf.GetBy(ad_account_conf.ChannelId.Eq(MINTEGRAL_CHANNEL_ID).And(ad_account_conf.AccountId.Eq(accountId)).And(ad_account_conf.Status.Eq(1)))
		if accountInfo != nil {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("mintegral_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
		}
	}

	return nil
}

func (this *MintegralGrab) fetch(accountInfo *ad_account_conf.AdAccountConfModel, startDate string, endDate string) error {
	// 从账户配置中获取Token
	token := accountInfo.Token
	if token == "" {
		return fmt.Errorf("账户 %s 缺少Token配置", accountInfo.AdAccount)
	}

	// 第一步：发起type=1请求，生成数据
	err := this.requestDataGeneration(accountInfo, startDate, endDate, token)
	if err != nil {
		return fmt.Errorf("数据生成请求失败: %v", err)
	}

	// 等待数据生成完成
	err = this.waitForDataGeneration(accountInfo, startDate, endDate, token)
	if err != nil {
		return fmt.Errorf("等待数据生成失败: %v", err)
	}

	// 第二步：发起type=2请求，下载数据
	err = this.downloadAndProcessData(accountInfo, startDate, endDate, token)
	if err != nil {
		return fmt.Errorf("下载数据失败: %v", err)
	}

	return nil
}

func (this *MintegralGrab) requestDataGeneration(accountInfo *ad_account_conf.AdAccountConfModel, startDate string, endDate string, token string) error {
	params := url.Values{}
	params.Add("start_time", startDate)
	params.Add("end_time", endDate)
	params.Add("type", "1")
	params.Add("dimension_option", "Offer,Campaign,Creative,Location") // 根据需要调整维度
	params.Add("time_granularity", "daily")
	params.Add("timezone", "+8") // 根据账户时区调整

	apiURL := "https://ss-api.mintegral.com/api/v2/reports/data?" + params.Encode()

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	client := this.getHttpClient()
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != 200 {
		return fmt.Errorf("接口响应错误: %d, 响应内容: %s", resp.StatusCode, string(body))
	}

	var statusResp mintegral.MintegralStatusResponse
	if err := json.Unmarshal(body, &statusResp); err != nil {
		return fmt.Errorf("JSON解析失败: %v", err)
	}

	plusQ.Logger().Info("mintegral_grab_cron", fmt.Sprintf("数据生成请求响应: code=%d, msg=%s", statusResp.Code, statusResp.Msg))

	if statusResp.Code != 200 && statusResp.Code != 201 && statusResp.Code != 202 {
		return fmt.Errorf("数据生成失败: code=%d, msg=%s", statusResp.Code, statusResp.Msg)
	}

	return nil
}

func (this *MintegralGrab) waitForDataGeneration(accountInfo *ad_account_conf.AdAccountConfModel, startDate string, endDate string, token string) error {
	maxRetries := 30 // 最多等待30次，每次间隔10秒，总共5分钟
	retryInterval := 10 * time.Second

	for i := 0; i < maxRetries; i++ {
		time.Sleep(retryInterval)

		params := url.Values{}
		params.Add("start_time", startDate)
		params.Add("end_time", endDate)
		params.Add("type", "1")
		params.Add("dimension_option", "Offer,Campaign,Creative,Location")
		params.Add("time_granularity", "daily")
		params.Add("timezone", "+8")

		apiURL := "https://ss-api.mintegral.com/api/v2/reports/data?" + params.Encode()

		req, err := http.NewRequest("GET", apiURL, nil)
		if err != nil {
			continue
		}

		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")

		client := this.getHttpClient()
		resp, err := client.Do(req)
		if err != nil {
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}

		var statusResp mintegral.MintegralStatusResponse
		if err := json.Unmarshal(body, &statusResp); err != nil {
			continue
		}

		plusQ.Logger().Info("mintegral_grab_cron", fmt.Sprintf("检查数据生成状态: code=%d, msg=%s", statusResp.Code, statusResp.Msg))

		if statusResp.Code == 200 {
			utils.Debug("数据生成完成", fmt.Sprintf("hours=%d, is_complete=%t", statusResp.Data.Hours, statusResp.Data.IsComplete))
			return nil
		}

		if statusResp.Code != 201 && statusResp.Code != 202 {
			return fmt.Errorf("数据生成失败: code=%d, msg=%s", statusResp.Code, statusResp.Msg)
		}
	}

	return fmt.Errorf("等待数据生成超时")
}

func (this *MintegralGrab) downloadAndProcessData(accountInfo *ad_account_conf.AdAccountConfModel, startDate string, endDate string, token string) error {
	params := url.Values{}
	params.Add("start_time", startDate)
	params.Add("end_time", endDate)
	params.Add("type", "2")
	params.Add("dimension_option", "Offer,Campaign,Creative,Location")
	params.Add("time_granularity", "daily")
	params.Add("timezone", "+8")

	apiURL := "https://ss-api.mintegral.com/api/v2/reports/data?" + params.Encode()

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return fmt.Errorf("创建下载请求失败: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+token)

	client := this.getHttpClient()
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("下载请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("下载失败: %d, 响应内容: %s", resp.StatusCode, string(body))
	}

	// 检查Content-Type是否为文件流
	contentType := resp.Header.Get("Content-Type")
	if contentType != "application/octet-stream" {
		// 可能是错误响应
		body, _ := io.ReadAll(resp.Body)
		var errorResp mintegral.MintegralErrorResponse
		if err := json.Unmarshal(body, &errorResp); err == nil {
			return fmt.Errorf("下载失败: code=%d, msg=%s", errorResp.Code, errorResp.Msg)
		}
		return fmt.Errorf("下载失败: 未知错误, 响应内容: %s", string(body))
	}

	// 解析TSV数据
	return this.parseTSVData(resp.Body, accountInfo)
}

func (this *MintegralGrab) parseTSVData(reader io.Reader, accountInfo *ad_account_conf.AdAccountConfModel) error {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = '\t' // TSV使用制表符分隔

	// 读取表头
	headers, err := csvReader.Read()
	if err != nil {
		return fmt.Errorf("读取表头失败: %v", err)
	}

	utils.Debug("TSV表头", headers)

	insertData := make([]map[string]any, 0)

	// 读取数据行
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("读取数据行失败: %v", err)
		}

		// 解析每一行数据
		rowData := this.parseRowData(headers, record, accountInfo)
		if rowData != nil {
			insertData = append(insertData, rowData)
		}
	}

	utils.Debug("解析到的数据条数", len(insertData))

	// 批量插入数据
	if len(insertData) > 0 {
		_, _, e := plusQ.Db("data").InsertOrUpdateBatch("ad_cost", insertData, true)
		if e != nil {
			plusQ.Logger().Error("mintegral_grab_err", insertData)
			plusQ.Logger().Error("mintegral_grab_err", e)
			return e
		}
		this.insertConfData(insertData)
	}

	return nil
}

func (this *MintegralGrab) parseRowData(headers []string, record []string, accountInfo *ad_account_conf.AdAccountConfModel) map[string]any {
	if len(headers) != len(record) {
		utils.Debug("数据行长度不匹配", fmt.Sprintf("headers: %d, record: %d", len(headers), len(record)))
		return nil
	}

	// 创建字段映射
	fieldMap := make(map[string]string)
	for i, header := range headers {
		fieldMap[header] = record[i]
	}

	// 解析基本信息
	date := fieldMap["Date"]
	if date == "" {
		return nil
	}

	// 解析游戏信息
	platformId := 1 // 默认Android
	gameId := 0
	cpGameId := 0

	// 从Offer Name或Campaign Package解析游戏信息
	offerName := fieldMap["Offer Name"]
	campaignPackage := fieldMap["Campaign Package"]

	if offerName != "" {
		parts := strings.Split(offerName, "_")
		if len(parts) >= 2 {
			platform := strings.ToLower(parts[1])
			if platform == "ios" {
				platformId = 2
			}
			gameInfo, _ := game.GetBy(game.AppShowName.Eq(parts[0]))
			if gameInfo != nil {
				gameId = int(gameInfo.Id)
				cpGameId = gameInfo.CpGameId
			}
		}
	}

	// 解析国家代码
	countryCode := strings.ToUpper(fieldMap["Location"])
	if countryCode == "" {
		countryCode = "UNKNOWN"
	}

	// 创建插入数据
	inserted := make(map[string]any)

	// 基本信息
	inserted["source_id"] = 13 // Mintegral的source_id
	inserted["country_code"] = countryCode
	inserted["time"] = date
	inserted["channel_id"] = MINTEGRAL_CHANNEL_ID
	inserted["cp_game_id"] = cpGameId
	inserted["game_id"] = gameId

	// 查找package_id
	campaignId := fieldMap["Campaign Id"]
	actLog, _ := hw_device_active_source.GetBy(hw_device_active_source.CampaignId.Eq(campaignId).And(hw_device_active_source.GameId.Eq(gameId)))
	if actLog != nil {
		inserted["package_id"] = actLog.PackageId
	} else {
		inserted["package_id"] = 0
	}

	inserted["account_id"] = accountInfo.AccountId
	inserted["ad_account"] = accountInfo.AdAccount
	inserted["platform_id"] = platformId
	inserted["time_zone"] = strings.ToLower(accountInfo.TimeZone)
	inserted["update_time"] = time.Now().Format(time.DateTime)

	// 广告信息
	inserted["campaign_id"] = campaignId
	inserted["campaign_name"] = fieldMap["Offer Name"]

	// 生成plan_id和creative_id
	creativeIdStr := fieldMap["Creative Id"]
	offerIdStr := fieldMap["Offer Id"]

	hash := md5.Sum([]byte(campaignId + "_" + creativeIdStr))
	inserted["plan_id"] = hex.EncodeToString(hash[:])
	inserted["plan_name"] = fieldMap["Offer Name"]
	inserted["creative_id_origin"] = hex.EncodeToString(hash[:])

	inserted["creative_id"] = fmt.Sprintf("%s-%s-%s", campaignId, inserted["plan_id"].(string), inserted["creative_id_origin"].(string))
	hash2 := md5.Sum([]byte(inserted["creative_id"].(string)))
	inserted["creative_id_md5"] = hex.EncodeToString(hash2[:])

	inserted["creative_name"] = fieldMap["Creative Name"]

	// 广告指标
	impression, _ := strconv.ParseFloat(fieldMap["Impression"], 64)
	click, _ := strconv.ParseFloat(fieldMap["Click"], 64)
	conversion, _ := strconv.ParseFloat(fieldMap["Conversion"], 64)
	spend, _ := strconv.ParseFloat(fieldMap["Spend"], 64)
	ctr, _ := strconv.ParseFloat(fieldMap["Ctr"], 64)

	inserted["show"] = int64(impression)
	inserted["click"] = int64(click)
	inserted["install"] = int64(conversion)
	inserted["cost"] = spend

	// 返点后金额
	inserted["cost_discount"] = helper.GetDiscount(spend, date, accountInfo)
	inserted["ctr"] = ctr

	// 计算衍生指标
	// 媒体安装成本
	if conversion > 0 {
		inserted["install_cost"] = spend / conversion
	} else {
		inserted["install_cost"] = 0
	}

	// 点击安装率
	if click > 0 {
		inserted["click_install_rate"] = conversion / click
	} else {
		inserted["click_install_rate"] = 0
	}

	// 千次曝光安装量
	if impression > 0 {
		inserted["install_per_show"] = conversion / impression * 1000
	} else {
		inserted["install_per_show"] = 0
	}

	// CPM
	if impression > 0 {
		inserted["cpm"] = spend / impression * 1000
	} else {
		inserted["cpm"] = 0
	}

	return inserted
}

func (this *MintegralGrab) getHttpClient() *http.Client {
	if os.Getenv("APP_ENV") == "dev" {
		proxyURL := "http://127.0.0.1:33210"
		proxy, _ := url.Parse(proxyURL)
		return &http.Client{
			Timeout: 180 * time.Second,
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxy),
			},
		}
	} else {
		return &http.Client{
			Timeout: 180 * time.Second,
		}
	}
}

func (this *MintegralGrab) insertConfData(data []map[string]any) {
	insertDataCampaign := make([]map[string]any, 0)
	insertDataPlan := make([]map[string]any, 0)
	insertDataCreative := make([]map[string]any, 0)

	for _, inserted := range data {
		insertDataCampaign = append(insertDataCampaign, map[string]any{
			"campaign_id":   inserted["campaign_id"],
			"campaign_name": inserted["campaign_name"],
			"update_time":   time.Now().Format(time.DateTime),
		})
		insertDataPlan = append(insertDataPlan, map[string]any{
			"campaign_id": inserted["campaign_id"],
			"plan_id":     inserted["plan_id"],
			"plan_name":   inserted["plan_name"],
			"update_time": time.Now().Format(time.DateTime),
		})
		insertDataCreative = append(insertDataCreative, map[string]any{
			"campaign_id":   inserted["campaign_id"],
			"plan_id":       inserted["plan_id"],
			"creative_id":   inserted["creative_id_md5"],
			"creative_name": inserted["creative_name"],
			"update_time":   time.Now().Format(time.DateTime),
			"country_id":    0,
			"os":            system.If(inserted["platform_id"].(int) == 2, "ios", "android"),
			"ext":           inserted["creative_id_origin"],
			"game_id":       inserted["game_id"],
			"package_id":    inserted["package_id"],
		})
	}

	if len(insertDataCampaign) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_campaign_conf", insertDataCampaign, true)
	}
	if len(insertDataPlan) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_plan_conf", insertDataPlan, true)
	}
	if len(insertDataCreative) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_creative_conf", insertDataCreative, true)
	}
}
