package cmd

import (
	"iaa_data/model/data/ad_account_conf"
	"iaa_data/utils"
	"time"
)

const MINTEGRAL_CHANNEL_ID = 6

type MintegralGrab struct {
}

func (this *MintegralGrab) Run(params map[string]string) error {
	utils.Debug("命令行参数", params)

	var ok bool
	var startDate string
	var endDate string
	startDate, ok = params["start_date"]
	if !ok {
		startDate = time.Now().AddDate(0, 0, -1).Format(time.DateOnly)
	}
	endDate, ok = params["end_date"]
	if !ok {
		endDate = time.Now().Format(time.DateOnly)
	}
	dateArr, err := utils.GetDateRange(startDate, endDate)
	if err != nil {
		return err
	}
	utils.Debug("日期范围", dateArr)

	accountId, ok := params["account_id"]
	if !ok {
		accountList, _ := ad_account_conf.ListBy(ad_account_conf.ChannelId.Eq(MINTEGRAL_CHANNEL_ID).And(ad_account_conf.Status.Eq(1)))
		for _, accountInfo := range accountList {
			utils.Debug("开始抓取", accountInfo.AdAccount)

		}
	} else {
		accountInfo, _ := ad_account_conf.GetBy(ad_account_conf.ChannelId.Eq(MINTEGRAL_CHANNEL_ID).And(ad_account_conf.AccountId.Eq(accountId)).And(ad_account_conf.Status.Eq(1)))
		if accountInfo != nil {

			utils.Debug("开始抓取", accountInfo.AdAccount)

		}
	}

	return nil
}
