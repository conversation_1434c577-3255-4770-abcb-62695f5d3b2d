package helper

import (
	"910.com/plus2.git/plusQ"
	"iaa_data/model/data/ad_account_conf"
	"strconv"
	"time"
)

func GetDiscount(cost float64, date string, model *ad_account_conf.AdAccountConfModel) float64 {
	layout := []string{
		time.DateOnly,
		time.DateTime,
	}

	var rdate string
	for _, v := range layout {
		parse, err := time.Parse(v, date)
		if err == nil {
			rdate = parse.Format(time.DateTime)
		}
	}
	if rdate == "" {
		return cost
	}

	sql := `select * from ad_cost_conf acc where ?<ADDTIME(end_time,'23:59:59') and ?>=ADDTIME(start_time ,'00:00:00') and channel_id=? and agent_id=? and operate_id=?`
	row, err := plusQ.Db("data").Get(sql, rdate, rdate, model.ChannelId, model.AgentId, model.OperateId)
	if err != nil {
		return cost
	}
	if row != nil {
		rebate, _ := strconv.ParseFloat(row["rebate"].(string), 64)
		cost = cost * ((100 - rebate) / 100)
	}
	return cost

}
