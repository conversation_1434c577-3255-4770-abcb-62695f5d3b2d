package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/plusQ"
	"context"
	_ "embed"
	"github.com/joho/godotenv"
	"iaa_data/log_bus"
	"iaa_data/provider"
	"iaa_data/server"
	"log"
	"os"
	"os/signal"
	"syscall"
)

//go:embed config/config_mq.yaml
var configMq string

// http api 服务
func main() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	// 加载配置文件
	godotenv.Load()
	configStr := configMq

	_, err := plus.LoadServerConfigDefault(configStr)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}

	provider.Register() //注册服务
	// 关闭信号
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM, os.Kill)
	defer stop()
	//注册接口
	services := server.NewWorkerServer(ctx,
		log_bus.NewService(),
	)
	//启动服务
	err = plus.New(ctx, services).Run("iaa_data_log_bus")
	if err != nil {
		plusQ.Logger().Alert("服务关闭", err)
	}
}
