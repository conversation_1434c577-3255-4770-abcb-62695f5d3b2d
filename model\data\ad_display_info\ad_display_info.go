package ad_display_info

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_display_info")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// DayNum 对应x天后的值
	DayNum = property.Property("day_num")
	// BannerImp 安装后x天内用户的横幅展示次数
	BannerImp = property.Property("banner_imp")
	// BannerImpPerUser banner_imp_«x»÷ user_count_«x»
	BannerImpPerUser = property.Property("banner_imp_per_user")
	// Country 展示的两字母国家/地区代码
	Country = property.Property("country")
	// Application 应用程序名称
	Application = property.Property("application")
	// Day 日期
	Day = property.Property("day")
	// Imp 安装后x天内用户的展示次数
	Imp = property.Property("imp")
	// ImpPerUser 安装后x天每个用户的展示次数： imp_«x»÷ user_count_«x»
	ImpPerUser = property.Property("imp_per_user")
	// Installs 新安装者的数量
	Installs = property.Property("installs")
	// InterImp 安装后x天内用户的（非奖励）插页式广告展示次数
	InterImp = property.Property("inter_imp")
	// InterImpPerUser inter_imp_«x»÷ user_count_«x»
	InterImpPerUser = property.Property("inter_imp_per_user")
	// MrecImp 安装后x天用户的MREC 印象数
	MrecImp = property.Property("mrec_imp")
	// MrecImpPerUser mrec_imp_«x»÷ user_count_«x»
	MrecImpPerUser = property.Property("mrec_imp_per_user")
	// PackageName 包名
	PackageName = property.Property("package_name")
	// Platform 应用程序平台：android、fireos、 或ios。	android
	Platform = property.Property("platform")
	// RewardImp 安装后x天来自用户的奖励展示次数
	RewardImp = property.Property("reward_imp")
	// RewardImpPerUser reward_imp_«x»÷ user_count_«x»
	RewardImpPerUser = property.Property("reward_imp_per_user")
	// UserCount 安装后x天内活跃用户数
	UserCount = property.Property("user_count")
	// Utime 更新时间
	Utime = property.Property("utime")
)

var FieldsAll = Fields(Id, DayNum, BannerImp, BannerImpPerUser, Country, Application, Day, Imp, ImpPerUser, Installs, InterImp, InterImpPerUser, MrecImp, MrecImpPerUser, PackageName, Platform, RewardImp, RewardImpPerUser, UserCount, Utime)
var NonePrimaryFields = Fields(DayNum, BannerImp, BannerImpPerUser, Country, Application, Day, Imp, ImpPerUser, Installs, InterImp, InterImpPerUser, MrecImp, MrecImpPerUser, PackageName, Platform, RewardImp, RewardImpPerUser, UserCount, Utime)
var NoneAutoIncrementFields = Fields(DayNum, BannerImp, BannerImpPerUser, Country, Application, Day, Imp, ImpPerUser, Installs, InterImp, InterImpPerUser, MrecImp, MrecImpPerUser, PackageName, Platform, RewardImp, RewardImpPerUser, UserCount, Utime)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdDisplayInfoModel 广告展示信息表
type AdDisplayInfoModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// DayNum 对应x天后的值
	DayNum *float32 `orm:"day_num" json:"day_num"`

	// BannerImp 安装后x天内用户的横幅展示次数
	BannerImp uint32 `orm:"banner_imp" json:"banner_imp"`

	// BannerImpPerUser banner_imp_«x»÷ user_count_«x»
	BannerImpPerUser uint32 `orm:"banner_imp_per_user" json:"banner_imp_per_user"`

	// Country 展示的两字母国家/地区代码
	Country *string `orm:"country" json:"country"`

	// Application 应用程序名称
	Application *string `orm:"application" json:"application"`

	// Day 日期
	Day *CustomTime `orm:"day" json:"day"`

	// Imp 安装后x天内用户的展示次数
	Imp uint32 `orm:"imp" json:"imp"`

	// ImpPerUser 安装后x天每个用户的展示次数： imp_«x»÷ user_count_«x»
	ImpPerUser uint32 `orm:"imp_per_user" json:"imp_per_user"`

	// Installs 新安装者的数量
	Installs *uint32 `orm:"installs" json:"installs"`

	// InterImp 安装后x天内用户的（非奖励）插页式广告展示次数
	InterImp *uint32 `orm:"inter_imp" json:"inter_imp"`

	// InterImpPerUser inter_imp_«x»÷ user_count_«x»
	InterImpPerUser uint32 `orm:"inter_imp_per_user" json:"inter_imp_per_user"`

	// MrecImp 安装后x天用户的MREC 印象数
	MrecImp uint32 `orm:"mrec_imp" json:"mrec_imp"`

	// MrecImpPerUser mrec_imp_«x»÷ user_count_«x»
	MrecImpPerUser uint32 `orm:"mrec_imp_per_user" json:"mrec_imp_per_user"`

	// PackageName 包名
	PackageName *string `orm:"package_name" json:"package_name"`

	// Platform 应用程序平台：android、fireos、 或ios。	android
	Platform *string `orm:"platform" json:"platform"`

	// RewardImp 安装后x天来自用户的奖励展示次数
	RewardImp uint32 `orm:"reward_imp" json:"reward_imp"`

	// RewardImpPerUser reward_imp_«x»÷ user_count_«x»
	RewardImpPerUser uint32 `orm:"reward_imp_per_user" json:"reward_imp_per_user"`

	// UserCount 安装后x天内活跃用户数
	UserCount uint32 `orm:"user_count" json:"user_count"`

	// Utime 更新时间
	Utime *CustomTime `orm:"utime" json:"utime"`
}

type PagedResult struct {
	Records      []*AdDisplayInfoModel `json:"list"`
	PageNum      int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
	TotalPages   int                   `json:"total_pages"`
	TotalRecords int                   `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:               1,
	DayNum:           2,
	BannerImp:        3,
	BannerImpPerUser: 4,
	Country:          5,
	Application:      6,
	Day:              7,
	Imp:              8,
	ImpPerUser:       9,
	Installs:         10,
	InterImp:         11,
	InterImpPerUser:  12,
	MrecImp:          13,
	MrecImpPerUser:   14,
	PackageName:      15,
	Platform:         16,
	RewardImp:        17,
	RewardImpPerUser: 18,
	UserCount:        19,
	Utime:            20,
}

func (m *AdDisplayInfoModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdDisplayInfoModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.DayNum, m.BannerImp, m.BannerImpPerUser, m.Country, m.Application, m.Day, m.Imp, m.ImpPerUser, m.Installs, m.InterImp, m.InterImpPerUser, m.MrecImp, m.MrecImpPerUser, m.PackageName, m.Platform, m.RewardImp, m.RewardImpPerUser, m.UserCount, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdDisplayInfoModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.DayNum, m.BannerImp, m.BannerImpPerUser, m.Country, m.Application, m.Day, m.Imp, m.ImpPerUser, m.Installs, m.InterImp, m.InterImpPerUser, m.MrecImp, m.MrecImpPerUser, m.PackageName, m.Platform, m.RewardImp, m.RewardImpPerUser, m.UserCount, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdDisplayInfoModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdDisplayInfoModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.DayNum, m.BannerImp, m.BannerImpPerUser, m.Country, m.Application, m.Day, m.Imp, m.ImpPerUser, m.Installs, m.InterImp, m.InterImpPerUser, m.MrecImp, m.MrecImpPerUser, m.PackageName, m.Platform, m.RewardImp, m.RewardImpPerUser, m.UserCount, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdDisplayInfoModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdDisplayInfoModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.DayNum, m.BannerImp, m.BannerImpPerUser, m.Country, m.Application, m.Day, m.Imp, m.ImpPerUser, m.Installs, m.InterImp, m.InterImpPerUser, m.MrecImp, m.MrecImpPerUser, m.PackageName, m.Platform, m.RewardImp, m.RewardImpPerUser, m.UserCount, m.Utime), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdDisplayInfoModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdDisplayInfoModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.DayNum, m.BannerImp, m.BannerImpPerUser, m.Country, m.Application, m.Day, m.Imp, m.ImpPerUser, m.Installs, m.InterImp, m.InterImpPerUser, m.MrecImp, m.MrecImpPerUser, m.PackageName, m.Platform, m.RewardImp, m.RewardImpPerUser, m.UserCount, m.Utime), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.DayNum, m.BannerImp, m.BannerImpPerUser, m.Country, m.Application, m.Day, m.Imp, m.ImpPerUser, m.Installs, m.InterImp, m.InterImpPerUser, m.MrecImp, m.MrecImpPerUser, m.PackageName, m.Platform, m.RewardImp, m.RewardImpPerUser, m.UserCount, m.Utime))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdDisplayInfoModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdDisplayInfoModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdDisplayInfoModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdDisplayInfoModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdDisplayInfoModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdDisplayInfoModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdDisplayInfoModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdDisplayInfoModel, error) {

	modelList := make([]*AdDisplayInfoModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdDisplayInfoModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.BannerImp, &m.BannerImpPerUser, &m.Country, &m.Application, &m.Day, &m.Imp, &m.ImpPerUser, &m.Installs, &m.InterImp, &m.InterImpPerUser, &m.MrecImp, &m.MrecImpPerUser, &m.PackageName, &m.Platform, &m.RewardImp, &m.RewardImpPerUser, &m.UserCount, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdDisplayInfoModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdDisplayInfoModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdDisplayInfoModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdDisplayInfoModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdDisplayInfoModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdDisplayInfoModel, error) {

	m := &AdDisplayInfoModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.BannerImp, &m.BannerImpPerUser, &m.Country, &m.Application, &m.Day, &m.Imp, &m.ImpPerUser, &m.Installs, &m.InterImp, &m.InterImpPerUser, &m.MrecImp, &m.MrecImpPerUser, &m.PackageName, &m.Platform, &m.RewardImp, &m.RewardImpPerUser, &m.UserCount, &m.Utime)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdDisplayInfoModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdDisplayInfoModel, error) {

	modelList := make([]*AdDisplayInfoModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdDisplayInfoModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.BannerImp, &m.BannerImpPerUser, &m.Country, &m.Application, &m.Day, &m.Imp, &m.ImpPerUser, &m.Installs, &m.InterImp, &m.InterImpPerUser, &m.MrecImp, &m.MrecImpPerUser, &m.PackageName, &m.Platform, &m.RewardImp, &m.RewardImpPerUser, &m.UserCount, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdDisplayInfoModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdDisplayInfoModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdDisplayInfoModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.BannerImp, &m.BannerImpPerUser, &m.Country, &m.Application, &m.Day, &m.Imp, &m.ImpPerUser, &m.Installs, &m.InterImp, &m.InterImpPerUser, &m.MrecImp, &m.MrecImpPerUser, &m.PackageName, &m.Platform, &m.RewardImp, &m.RewardImpPerUser, &m.UserCount, &m.Utime)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdDisplayInfoModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdDisplayInfoModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdDisplayInfoModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdDisplayInfoModel, error) {

	m := &AdDisplayInfoModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdDisplayInfoModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdDisplayInfoModel, error) {

	modelList := make([]*AdDisplayInfoModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdDisplayInfoModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdDisplayInfoModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdDisplayInfoModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdDisplayInfoModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdDisplayInfoModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.DayNum, d.BannerImp, d.BannerImpPerUser, d.Country, d.Application, d.Day, d.Imp, d.ImpPerUser, d.Installs, d.InterImp, d.InterImpPerUser, d.MrecImp, d.MrecImpPerUser, d.PackageName, d.Platform, d.RewardImp, d.RewardImpPerUser, d.UserCount, d.Utime))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "day_num",
			"title":     "对应x天后的值",
		},
		{
			"dataIndex": "banner_imp",
			"title":     "安装后x天内用户的横幅展示次数",
		},
		{
			"dataIndex": "banner_imp_per_user",
			"title":     "banner_imp_«x»÷ user_count_«x»",
		},
		{
			"dataIndex": "country",
			"title":     "展示的两字母国家/地区代码",
		},
		{
			"dataIndex": "application",
			"title":     "应用程序名称",
		},
		{
			"dataIndex": "day",
			"title":     "日期",
		},
		{
			"dataIndex": "imp",
			"title":     "安装后x天内用户的展示次数",
		},
		{
			"dataIndex": "imp_per_user",
			"title":     "安装后x天每个用户的展示次数： imp_«x»÷ user_count_«x»",
		},
		{
			"dataIndex": "installs",
			"title":     "新安装者的数量",
		},
		{
			"dataIndex": "inter_imp",
			"title":     "安装后x天内用户的（非奖励）插页式广告展示次数",
		},
		{
			"dataIndex": "inter_imp_per_user",
			"title":     "inter_imp_«x»÷ user_count_«x»",
		},
		{
			"dataIndex": "mrec_imp",
			"title":     "安装后x天用户的MREC 印象数",
		},
		{
			"dataIndex": "mrec_imp_per_user",
			"title":     "mrec_imp_«x»÷ user_count_«x»",
		},
		{
			"dataIndex": "package_name",
			"title":     "包名",
		},
		{
			"dataIndex": "platform",
			"title":     "应用程序平台：android、fireos、 或ios。	android",
		},
		{
			"dataIndex": "reward_imp",
			"title":     "安装后x天来自用户的奖励展示次数",
		},
		{
			"dataIndex": "reward_imp_per_user",
			"title":     "reward_imp_«x»÷ user_count_«x»",
		},
		{
			"dataIndex": "user_count",
			"title":     "安装后x天内活跃用户数",
		},
		{
			"dataIndex": "utime",
			"title":     "更新时间",
		}}
}
