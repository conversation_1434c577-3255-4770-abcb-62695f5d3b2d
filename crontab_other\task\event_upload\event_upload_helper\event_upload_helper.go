package event_upload_helper

import (
	"910.com/plus2.git/plusQ"
	"encoding/json"
	"fmt"
	"iaa_data/crontab_other/task/event_upload/event_upload_helper/upload_fail_helper"
	"iaa_data/utils"
	"iaa_data/utils/game_helper"
	"iaa_data/utils/http_utils"
	"log"
	"time"
)

const CHANNEL_ADJUST = "adjust"
const CHANNEL_FIREBASE = "google"
const CHANNEL_FACEBOOK = "facebook"

func UploadEvent(eventName, channel string, itemParams map[string]interface{}) error {
	gameId := utils.AnyToInt(itemParams["game_id"])
	info := EventConfInstant().GetInfo(eventName, channel, fmt.Sprint(gameId))
	if info == nil {
		log.Println("=info == nil", eventName, channel, gameId)
		return fmt.Errorf("<UNK>")
	}
	if fmt.Sprint(info["event_state"]) != "1" {
		return fmt.Errorf("<UNK>2")
	}
	appId := fmt.Sprint(info["app_id"])
	apiToken := fmt.Sprint(info["api_token"])
	eventToken := fmt.Sprint(info["event_token"])
	//apiUrl := fmt.Sprint(info["api_url"])
	gid_or_idfa := fmt.Sprint(itemParams["gid_or_idfa"])
	aid_or_idfv := fmt.Sprint(itemParams["aid_or_idfv"])
	coreAccount := fmt.Sprint(itemParams["core_account"])
	os := fmt.Sprint(itemParams["os"])
	log.Println("eventName=", eventName, "channel=", channel)
	if channel == CHANNEL_ADJUST {
		err := postAdjustEvent(eventToken, appId, gid_or_idfa, aid_or_idfv, os, coreAccount)
		return err
	} else if channel == CHANNEL_FIREBASE {
		if len(apiToken) > 5 {
			postFirebaseEvent(eventName, appId, apiToken, itemParams)
		} else {
			return fmt.Errorf("<UNK>3")
		}
	} else if channel == CHANNEL_FACEBOOK {
		postFacebookEvent(eventName, appId, apiToken, eventToken, coreAccount, itemParams)
	}
	return nil
}

func postAdjustEvent(eventToken, appToken, gid_or_idfa, aid_or_idfv, os, coreAccount string) error {
	url := "https://app.adjust.io/event"
	params := make(map[string]interface{})
	params["event_token"] = eventToken
	params["app_token"] = appToken
	params["s2s"] = "1"
	if os == "android" {
		if len(gid_or_idfa) > 0 && gid_or_idfa != "<nil>" && gid_or_idfa != "********-0000-0000-0000-********0000" {
			params["gps_adid"] = gid_or_idfa
		} else {
			params["android_id"] = aid_or_idfv
		}
	} else {
		if len(gid_or_idfa) > 0 && gid_or_idfa != "<nil>" && gid_or_idfa != "********-0000-0000-0000-********0000" {
			params["idfa"] = gid_or_idfa
		} else {
			params["idfv"] = aid_or_idfv
		}
	}
	callback_params := make(map[string]interface{})
	callback_params["core_account"] = coreAccount
	cal, _ := json.Marshal(callback_params)
	callbackStr := utils.UrlEncoded(string(cal))
	params["created_at_unix"] = time.Now().Unix()
	params["callback_params"] = callbackStr
	url = url + "?" + getParamsUrl(params)
	log.Println("adjust:" + url)
	h := make(map[string]string)
	h["Content-Type"] = "application/x-www-form-urlencoded"
	_, err := http_utils.HttpRequest(url, "POST", params, h)
	if err != nil {
		log.Println(err)
	}
	return err
}

func postFirebaseEvent(eventName, firebase_app_id, api_secret string, itemParams map[string]interface{}) {
	url := "https://www.google-analytics.com/mp/collect?firebase_app_id=" + firebase_app_id + "&api_secret=" + api_secret
	params := make(map[string]interface{})
	eventParams := make(map[string]interface{})
	var events = []map[string]interface{}{}
	event := make(map[string]interface{})

	appInstanceId := ""

	key := "AppInstanceId_" + fmt.Sprint(utils.AnyToInt(itemParams["package_id"])) + "_" + fmt.Sprint(itemParams["device_code"])

	val, err2 := plusQ.Cache("ssdb").Get(key)

	if err2 == nil && val != nil {
		appInstanceId = fmt.Sprint(val)
	} else {
		upload_fail_helper.Save(CHANNEL_FIREBASE, eventName, "找不到AppInstanceId", 0, itemParams)
		return
	}

	params["app_instance_id"] = appInstanceId
	params["user_id"] = fmt.Sprint(itemParams["core_account"])
	event["name"] = eventName
	eventParams["core_account"] = fmt.Sprint(itemParams["core_account"])
	event["params"] = eventParams

	events = append(events, event)

	params["events"] = events

	aa, _ := json.Marshal(params)
	log.Println("firebase:", string(aa))

	log.Println(url)
	h := make(map[string]string)
	h["Content-Type"] = "application/json"
	str, err := http_utils.HttpRequest(url, "POST", params, h)
	log.Println("firebase=="+eventName+"=="+str, err)
}

func postFacebookEvent(eventConfName, setId, access_token, eventName, coreAccount string, item map[string]interface{}) error {
	// 构建请求体数据
	event := buildFbEvent(eventName, coreAccount, item)
	requestBody := RequestBody{Data: []Event{event}}
	aa, _ := json.Marshal(requestBody)
	log.Println("face_book:", string(aa))

	// 创建请求
	url := "https://graph.facebook.com/v22.0/" + setId + "/events?access_token=" + access_token

	h := make(map[string]string)
	h["Content-Type"] = "application/json"
	str, err := http_utils.HttpRequest(url, "POST", requestBody, h)

	if err != nil {
		upload_fail_helper.Save(CHANNEL_FACEBOOK, eventConfName, err.Error(), 0, item)
	} else {
		log.Println("facebook_"+eventName+fmt.Sprint(item["os"])+fmt.Sprint(item["game_id"])+"==="+str, err)
	}
	log.Println("facebook:"+eventName+"==="+str, err)
	return err
}

func buildFbEvent(eventName, coreAccount string, item map[string]interface{}) Event {
	currentTime := time.Now().Unix()
	oneTag := "a2"
	if fmt.Sprint(item["os"]) == "ios" {
		oneTag = "i2"
	}

	madid := ""
	gid_or_idfa := fmt.Sprint(item["gid_or_idfa"])
	aid_or_idfv := fmt.Sprint(item["aid_or_idfv"])
	if fmt.Sprint(item["os"]) == "ios" {
		if len(gid_or_idfa) > 0 && gid_or_idfa != "<nil>" && gid_or_idfa != "********-0000-0000-0000-********0000" {
			madid = gid_or_idfa
		} else {
			madid = aid_or_idfv
		}
	} else {
		if len(gid_or_idfa) > 0 && gid_or_idfa != "<nil>" && gid_or_idfa != "********-0000-0000-0000-********0000" {
			madid = gid_or_idfa
		} else {
			madid = aid_or_idfv
		}
	}

	return Event{
		EventName:    eventName,
		EventTime:    currentTime,
		ActionSource: "app",
		UserData: UserData{
			AnonId: coreAccount,
			Madid:  madid,
		},
		CustomData: CustomData{
			//Value:    1.99,
		},
		AppData: AppData{
			AdvertiserTrackingEnabled:  false,
			ApplicationTrackingEnabled: false,
			CampaignIDs:                "",
			Extinfo: []string{
				oneTag,
				game_helper.GameHelInstant().GetPackageName(utils.AnyToInt(item["package_id"])),
				"",
				fmt.Sprint(item["game_version"]),
				fmt.Sprint(item["os_version"]),
				fmt.Sprint(item["device_type"]),
				"",
				"",
				"",
				fmt.Sprint(item["screen_height"]),
				fmt.Sprint(item["screen_width"]),
				"",
				"",
				"",
				"",
				"",
			},
		},
	}
}

func getParamsUrl(params map[string]interface{}) string {
	paramsUrl := ""
	for key, value := range params {
		if paramsUrl == "" {
			paramsUrl = key + "=" + fmt.Sprint(value)
		} else {
			paramsUrl = paramsUrl + "&" + key + "=" + fmt.Sprint(value)
		}
	}
	return paramsUrl
}
