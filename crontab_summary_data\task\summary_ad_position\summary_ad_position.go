package summary_ad_position

import (
	"iaa_data/crontab_summary_data/task/summary_ad_position/summary_ad_position_helper"
	"iaa_data/utils"
	"iaa_data/utils/constant"
	"iaa_data/utils/day_check_helper"
	"log"
	"time"
)

func InitFunc(tagTimeZone string) {

	nowTime := time.Now()

	dayCheckTag := "summary_ad_position_" + tagTimeZone

	if constant.TIME_BJ == tagTimeZone {
		tdayTime := nowTime.Format(time.DateOnly)
		summary_ad_position_helper.SummaryReport(tdayTime, tdayTime, tagTimeZone)
		if nowTime.Hour() >= 1 && nowTime.Hour() < 3 {
			if day_check_helper.CheckHour7(dayCheckTag) == "false" {
				start := nowTime.Add(-7 * 24 * time.Hour)
				endTime := nowTime.Add(24 * time.Hour).Format(time.DateOnly)
				log.Println("开始汇总", start.Format(time.DateOnly), endTime)
				summary_ad_position_helper.SummaryReport(start.Format(time.DateOnly), endTime, tagTimeZone)
			}
		}
	} else if constant.TIME_ERU == tagTimeZone {
		nowTimeStr := utils.BjToERUTime(nowTime.Format(time.DateTime))
		da, _ := time.Parse(time.DateTime, nowTimeStr)
		tdayTime := da.Format(time.DateOnly)

		summary_ad_position_helper.SummaryReport(tdayTime, tdayTime, tagTimeZone)
		if nowTime.Hour() >= 8 && nowTime.Hour() < 9 {
			if day_check_helper.CheckHour7(dayCheckTag) == "false" {
				start := nowTime.Add(-7 * 24 * time.Hour)
				endTime := nowTime.Add(24 * time.Hour).Format(time.DateOnly)
				log.Println("开始汇总", start.Format(time.DateOnly), endTime)
				summary_ad_position_helper.SummaryReport(start.Format(time.DateOnly), endTime, tagTimeZone)
			}
		}
	} else if constant.TIME_US == tagTimeZone {
		nowTimeStr := utils.BjToUSTime(nowTime.Format(time.DateTime))
		da, _ := time.Parse(time.DateTime, nowTimeStr)
		tdayTime := da.Format(time.DateOnly)
		summary_ad_position_helper.SummaryReport(tdayTime, tdayTime, tagTimeZone)
		if nowTime.Hour() >= 16 && nowTime.Hour() < 17 {
			if day_check_helper.CheckHour7(dayCheckTag) == "false" {
				start := nowTime.Add(-7 * 24 * time.Hour)
				endTime := nowTime.Add(24 * time.Hour).Format(time.DateOnly)
				log.Println("开始汇总", start.Format(time.DateOnly), endTime)
				summary_ad_position_helper.SummaryReport(start.Format(time.DateOnly), endTime, tagTimeZone)
			}
		}
	}
}
