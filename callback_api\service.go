package callback_api

import (
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/routing"
	"910.com/plus2.git/routing/content"
	"910.com/plus2.git/routing/cors"
	"iaa_data/callback_api/max_ad"
)

type Service struct{}

func NewService() *Service {
	return &Service{}
}

// InitHandler 初始化路由
func (s Service) InitHandler(router *routing.HostedRouters) error {
	opts := cors.Options{
		AllowOrigins: "*",
		AllowMethods: "POST, GET, OPTIONS",
		AllowHeaders: "access-control-allow-origin, authority, content-type, version-info, X-Requested-With, access-token",
	}
	router.Use(cors.Handler(opts))
	registry := plusQ.Invocation()
	group := router.Group("/api")
	group.Use(content.TypeNegotiator(content.JSON))
	//标签
	registry.Register("max_ad", max_ad.InitMaxAdController())
	group.Any("/max_ad/*", registry.HandleRequest)
	return nil
}
