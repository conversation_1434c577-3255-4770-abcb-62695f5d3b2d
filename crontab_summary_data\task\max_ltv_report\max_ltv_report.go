package max_ltv_report

import (
	"fmt"
	"iaa_data/utils/constant"
	"iaa_data/utils/day_check_helper"
	"iaa_data/utils/report_helper"
	"iaa_data/utils/sql_helper"
	"strings"
	"time"
)

func InitFunc(args ...string) {
	if day_check_helper.CheckDay("max_ltv_report1") {
		return
	}

	for _, key := range constant.MAX_KEY_LIST {
		for i := 0; i < 46; i++ {
			nowTime := time.Now()
			startTime := nowTime.Add(-24 * 45 * time.Hour).Format("2006-01-02")
			PostData(fmt.Sprint(i), startTime, key)
			PostDisplayData(fmt.Sprint(i), startTime, key)
			PostSessionData(fmt.Sprint(i), startTime, key)
		}
	}
}
func Replay() {
	for _, key := range constant.MAX_KEY_LIST {
		for i := 0; i < 46; i++ {
			nowTime := time.Now()
			startTime := nowTime.Add(-24 * 45 * time.Hour).Format("2006-01-02")
			PostData(fmt.Sprint(i), startTime, key)
			PostDisplayData(fmt.Sprint(i), startTime, key)
			PostSessionData(fmt.Sprint(i), startTime, key)
		}
	}
}

func PostData(dayNum, startTime, apiKey string) {
	nowTime := time.Now()
	params := make(map[string]interface{})
	params["api_key"] = apiKey
	params["columns"] = getColumns(dayNum)
	params["end"] = nowTime.Format("2006-01-02")
	params["start"] = startTime
	url := constant.AD_REVENUE_PERFORMANCE_URL
	resultData := report_helper.GetReport(url, params)
	if resultData != nil {
		for _, item := range resultData.MaxReportItems {
			for key, val := range item {
				newKey := strings.Replace(key, "_"+dayNum, "", 1)
				delete(item, key)
				item[newKey] = val
			}

			item["day_num"] = dayNum
			item["utime"] = time.Now().Format(time.DateTime)
			sql_helper.LogHelperInstance().HandleDataMapInsert("data", "ad_revenue_performance", "ad_revenue_performance", item, 1, 1)
		}
	}
}

func getColumns(count string) string {
	var columns []string

	columns = append(columns, "ads_pub_revenue_"+count)
	columns = append(columns, "ads_rpi_"+count)
	columns = append(columns, "application")
	columns = append(columns, "banner_pub_revenue_"+count)
	columns = append(columns, "banner_rpi_"+count)
	columns = append(columns, "country")
	columns = append(columns, "package_name")
	columns = append(columns, "platform")
	columns = append(columns, "day")
	columns = append(columns, "iap_pub_revenue_"+count)
	columns = append(columns, "iap_rpi_"+count)
	columns = append(columns, "installs")
	columns = append(columns, "inter_pub_revenue_"+count)
	columns = append(columns, "inter_rpi_"+count)
	columns = append(columns, "mrec_pub_revenue_"+count)
	columns = append(columns, "mrec_rpi_"+count)
	columns = append(columns, "pub_revenue_"+count)
	columns = append(columns, "reward_pub_revenue_"+count)
	columns = append(columns, "reward_rpi_"+count)
	columns = append(columns, "rpi_"+count)

	columnsStr := ""
	for _, val := range columns {
		if columnsStr == "" {
			columnsStr = val
		} else {
			columnsStr = columnsStr + "," + val
		}
	}
	return columnsStr
}

func PostDisplayData(dayNum, startTime, apiKey string) {
	nowTime := time.Now()
	params := make(map[string]interface{})
	params["api_key"] = apiKey
	params["columns"] = getDisplayColumns(dayNum)
	params["end"] = nowTime.Format("2006-01-02")
	params["start"] = startTime
	url := constant.AD_DISPLAY_INFO_URL
	resultData := report_helper.GetReport(url, params)
	if resultData != nil {
		for _, item := range resultData.MaxReportItems {
			for key, val := range item {
				newKey := strings.Replace(key, "_"+dayNum, "", 1)
				delete(item, key)
				item[newKey] = val
			}

			item["day_num"] = dayNum
			item["utime"] = time.Now().Format(time.DateTime)
			sql_helper.LogHelperInstance().HandleDataMapInsert("data", "ad_display_info", "ad_display_info", item, 1, 1)
		}
	}
}

func getDisplayColumns(count string) string {
	var columns []string

	columns = append(columns, "application")
	columns = append(columns, "banner_imp_"+count)
	columns = append(columns, "banner_imp_per_user_"+count)
	columns = append(columns, "country")
	columns = append(columns, "day")
	columns = append(columns, "imp_"+count)
	columns = append(columns, "package_name")
	columns = append(columns, "platform")
	columns = append(columns, "installs")
	columns = append(columns, "imp_per_user_"+count)
	columns = append(columns, "inter_imp_"+count)
	columns = append(columns, "inter_imp_per_user_"+count)
	columns = append(columns, "mrec_imp_"+count)
	columns = append(columns, "mrec_imp_per_user_"+count)
	columns = append(columns, "reward_imp_"+count)
	columns = append(columns, "reward_imp_per_user_"+count)
	columns = append(columns, "user_count_"+count)

	columnsStr := ""
	for _, val := range columns {
		if columnsStr == "" {
			columnsStr = val
		} else {
			columnsStr = columnsStr + "," + val
		}
	}
	return columnsStr
}

func PostSessionData(dayNum, startTime, apiKey string) {
	nowTime := time.Now()
	params := make(map[string]interface{})
	params["api_key"] = apiKey
	params["columns"] = getSessionColumns(dayNum)
	params["end"] = nowTime.Format("2006-01-02")
	params["start"] = startTime
	url := constant.AD_SESSION_INFO_URL
	resultData := report_helper.GetReport(url, params)
	if resultData != nil {
		for _, item := range resultData.MaxReportItems {
			for key, val := range item {
				newKey := strings.Replace(key, "_"+dayNum, "", 1)
				delete(item, key)
				item[newKey] = val
			}
			item["day_num"] = dayNum
			item["utime"] = time.Now().Format(time.DateTime)
			sql_helper.LogHelperInstance().HandleDataMapInsert("data", "ad_session_info", "ad_session_info", item, 1, 1)
		}
	}
}

func getSessionColumns(count string) string {
	var columns []string

	columns = append(columns, "day")
	columns = append(columns, "application")
	columns = append(columns, "package_name")
	columns = append(columns, "platform")
	columns = append(columns, "country")
	columns = append(columns, "installs")

	columns = append(columns, "daily_usage_"+count)
	columns = append(columns, "session_count_"+count)
	columns = append(columns, "user_count_"+count)
	columns = append(columns, "session_length_"+count)
	columns = append(columns, "retention_"+count)

	columnsStr := ""
	for _, val := range columns {
		if columnsStr == "" {
			columnsStr = val
		} else {
			columnsStr = columnsStr + "," + val
		}
	}
	return columnsStr
}
