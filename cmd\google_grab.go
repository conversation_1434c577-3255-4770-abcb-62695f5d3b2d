package cmd

/*
命令执行：/path/to/cli  -op google_grab -start_date 2025-04-25 -end_date 2025-04-25
*/

import (
	"910.com/plus2.git/plusQ"
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"iaa_data/cmd/helper"
	"iaa_data/cmd/model/google"
	"iaa_data/model/data/ad_account_conf"
	"iaa_data/model/platform/game"
	"iaa_data/utils"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"
)

const GG_CHANNEL_ID = 2

type GoogleGrab struct {
}

func (this *GoogleGrab) Run(params map[string]string) error {
	utils.Debug("命令行参数", params)

	var ok bool
	var startDate string
	var endDate string
	startDate, ok = params["start_date"]
	if !ok {
		startDate = time.Now().AddDate(0, 0, -3).Format(time.DateOnly)
	}
	endDate, ok = params["end_date"]
	if !ok {
		endDate = time.Now().Format(time.DateOnly)
	}

	accountId, ok := params["account_id"]
	if !ok {
		accountList, _ := ad_account_conf.ListBy(ad_account_conf.ChannelId.Eq(GG_CHANNEL_ID).And(ad_account_conf.Status.Eq(1)))
		for _, accountInfo := range accountList {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("google_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}

		}
	} else {
		accountInfo, _ := ad_account_conf.GetBy(ad_account_conf.ChannelId.Eq(GG_CHANNEL_ID).And(ad_account_conf.AccountId.Eq(accountId)).And(ad_account_conf.Status.Eq(1)))
		if accountInfo != nil {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("google_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
		}
	}

	return nil
}

func (this *GoogleGrab) getHttpClient() *http.Client {
	var client *http.Client
	if os.Getenv("APP_ENV") == "dev" {
		proxyURL := "http://127.0.0.1:33210"
		proxy, _ := url.Parse(proxyURL)
		client = &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxy),
			},
		}
	} else {
		client = &http.Client{
			Timeout: 30 * time.Second,
		}
	}

	return client
}

func (this *GoogleGrab) fetch(accountInfo *ad_account_conf.AdAccountConfModel, timeStart string, timeEnd string) error {
	requestBody := map[string]string{"query": fmt.Sprintf(`
SELECT
  segments.date,
  campaign.id,
  campaign.name,
  ad_group.id,
  ad_group.name,
  ad_group_ad.ad.id,
  ad_group_ad.ad.name,
  ad_group_ad.ad.type,
  ad_group_ad.status,
  metrics.impressions,
  metrics.clicks,
  metrics.cost_micros,
  metrics.conversions,
  metrics.ctr,
  metrics.average_cpm
FROM ad_group_ad
WHERE 
  segments.date BETWEEN '%s' AND '%s'
`, timeStart, timeEnd)}
	jsonData, _ := json.Marshal(requestBody)
	res := make([]google.Response, 0)

	// 创建请求
	accountId := strings.ReplaceAll(accountInfo.AccountId, "-", "")

	uri := "https://googleads.googleapis.com/v19/customers/" + accountId + "/googleAds:searchStream"
	req, _ := http.NewRequest("POST", uri, bytes.NewBuffer(jsonData))
	plusQ.Logger().Info("google_grab_cron", "发送请求："+uri)

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	tokenRes, err := this.getToken()
	if err != nil {
		return err
	}
	req.Header.Set("Authorization", "Bearer "+tokenRes.Data.Token)
	req.Header.Set("developer-token", "QprRkq94ArcLv2HoGnM6ew")
	req.Header.Set("login-customer-id", "**********")

	client := this.getHttpClient()
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("接口响应错误: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	utils.Debug("body", string(body))

	err = json.Unmarshal(body, &res)
	if err != nil {
		return errors.New("解析失败")
	}

	// 入库处理
	insertData := make([]map[string]any, 0)
	for _, row := range res {
		for _, result := range row.Results {

			platformId := 1
			gameId := 0
			cpGameId := 0
			countryCode := ""

			// M03_And-Android-US-MAIA-45岁+女性-0419
			parts := strings.Split(result.Campaign.Name, "-")
			if len(parts) >= 2 {
				platform := strings.ToLower(parts[1])
				if platform == "ios" {
					platformId = 2
				}
				gameInfo, _ := game.GetBy(game.AppShowName.Eq(parts[0]))
				if gameInfo != nil {
					gameId = int(gameInfo.Id)
					cpGameId = gameInfo.CpGameId
				}
				countryCode = parts[2]
			}

			inserted := make(map[string]any)
			// 基本信息
            inserted["source_id"] = 70
			inserted["country_code"] = countryCode
			inserted["time"] = result.Segments.Date
			inserted["channel_id"] = GG_CHANNEL_ID
			inserted["cp_game_id"] = cpGameId
			inserted["game_id"] = gameId
			inserted["package_id"] = 0
			inserted["account_id"] = accountId
			inserted["ad_account"] = accountInfo.AdAccount
			inserted["platform_id"] = platformId
			inserted["time_zone"] = strings.ToLower(accountInfo.TimeZone)
			inserted["update_time"] = time.Now().Format(time.DateTime)

			// 广告信息
			inserted["campaign_id"] = result.Campaign.ID
			inserted["campaign_name"] = result.Campaign.Name
			inserted["plan_id"] = result.AdGroup.ID
			inserted["plan_name"] = result.AdGroup.Name
			inserted["creative_id_origin"] = result.AdGroupAd.Ad.ID
			inserted["creative_id"] = fmt.Sprintf("%s-%s-%s", result.Campaign.ID, result.AdGroup.ID, result.AdGroupAd.Ad.ID)

			// 广告指标
			inserted["show"], _ = result.Metrics.Impressions.Int64()
			inserted["click"], _ = result.Metrics.Clicks.Int64()
			inserted["install"], _ = result.Metrics.Conversions.Float64()
			cost, _ := result.Metrics.CostMicros.Float64()
			inserted["cost"] = cost / 1000000
			// 返点后金额
			inserted["cost_discount"] = helper.GetDiscount(cost, result.Segments.Date, accountInfo)

			cpm, _ := result.Metrics.AverageCpm.Float64()
			inserted["cpm"] = cpm / 1000000
			inserted["ctr"], _ = result.Metrics.Ctr.Float64()

			//媒体安装成本
			install, _ := result.Metrics.Conversions.Float64()
			if install > 0 {
				inserted["install_cost"] = (cost / 1000000) / install
			} else {
				inserted["install_cost"] = 0
			}

			//点击安装率
			clicks, _ := result.Metrics.Clicks.Float64()
			if clicks > 0 {
				inserted["click_install_rate"] = install / clicks
			} else {
				inserted["click_install_rate"] = 0
			}

			//千次曝光安装量
			show, _ := result.Metrics.Impressions.Float64()
			if show > 0 {
				inserted["install_per_show"] = install / show * 1000
			} else {
				inserted["install_per_show"] = 0
			}

			insertData = append(insertData, inserted)
		}
	}

	if len(insertData) > 0 {
		_, _, e := plusQ.Db("data").InsertOrUpdateBatch("ad_cost", insertData, true)
		if e != nil {
			plusQ.Logger().Error("google_grab_err", uri)
			plusQ.Logger().Error("google_grab_err", insertData)
			plusQ.Logger().Error("google_grab_err", e)
		}
	}

	return nil
}

func (this *GoogleGrab) getToken() (*google.HwtjRes, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	uri := "https://hwtj.mgamestore.com/index/token"
	resp, err := client.Get(uri)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var res google.HwtjRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return nil, err
	}
	return &res, nil
}
