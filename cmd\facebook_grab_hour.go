package cmd

/*
命令执行：/path/to/cli  -op facebook_grab_hour -account_id **************** -start_date 2025-03-27 -end_date 2025-03-27
*/

import (
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/utils/system"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"iaa_data/cmd/helper"
	"iaa_data/cmd/model/meta"
	"iaa_data/model/data/ad_account_conf"
	"iaa_data/model/data/hw_device_active_source"
	"iaa_data/model/platform/game"
	"iaa_data/utils"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

type FacebookGrabHour struct {
}

func (this *FacebookGrabHour) Run(params map[string]string) error {
	utils.Debug("命令行参数", params)

	var ok bool
	var startDate string
	var endDate string
	startDate, ok = params["start_date"]
	if !ok {
		startDate = time.Now().AddDate(0, 0, -3).Format(time.DateOnly)
	}
	endDate, ok = params["end_date"]
	if !ok {
		endDate = time.Now().Format(time.DateOnly)
	}
	dateArr, err := utils.GetDateRange(startDate, endDate)
	if err != nil {
		return err
	}
	utils.Debug("日期范围", dateArr)

	accountId, ok := params["account_id"]
	if !ok {
		accountList, _ := ad_account_conf.ListBy(ad_account_conf.ChannelId.Eq(FB_CHANNEL_ID).And(ad_account_conf.Status.Eq(1)))
		for _, accountInfo := range accountList {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			for _, date := range dateArr {
				this.fetch(accountInfo.AccountId, accountInfo, date)
			}
		}
	} else {
		accountInfo, _ := ad_account_conf.GetBy(ad_account_conf.ChannelId.Eq(FB_CHANNEL_ID).And(ad_account_conf.AccountId.Eq(accountId)).And(ad_account_conf.Status.Eq(1)))
		if accountInfo != nil {
			for _, date := range dateArr {
				utils.Debug("开始抓取", accountInfo.AdAccount)
				this.fetch(accountId, accountInfo, date)
			}
		}
	}

	return nil
}

func (this *FacebookGrabHour) fetch(accountId string, accountInfo *ad_account_conf.AdAccountConfModel, timeFormat string) error {
	// 构建请求URL
	baseURL := fmt.Sprintf("https://graph.facebook.com/v20.0/%s/insights", "act_"+accountId)

	// 设置查询参数
	query := url.Values{}

	query.Add("access_token", FB_ACCESS_TOKEN)
	query.Add("level", "ad")
	query.Add("breakdowns", "hourly_stats_aggregated_by_advertiser_time_zone")
	query.Add("fields", "campaign_id,campaign_name,adset_id,adset_name,ad_id,ad_name,spend,impressions,clicks,actions,ctr,cpm")

	attributionWindows := []string{"1d_view", "1d_click"}
	for i, window := range attributionWindows {
		key := fmt.Sprintf("action_attribution_windows[%d]", i)
		query.Add(key, window)
	}
	query.Add("time_range", fmt.Sprintf("{'since':'%s','until':'%s'}", timeFormat, timeFormat))
	query.Add("limit", "100")

	uri := baseURL + "?" + query.Encode()
	finish := false
	for !finish {
		res, _ := this.sendRequest(uri)

		// 入库处理
		insertData := make([]map[string]any, 0)
		for _, row := range res.Data {

			platformId := 1
			gameId := 0
			cpGameId := 0
			countryCode := ""

			parts := strings.Split(row.CampaignName, "-")
			if len(parts) >= 3 {
				platform := strings.ToLower(parts[1])
				if platform == "ios" {
					platformId = 2
				}
				// 特殊处理
				if parts[0] == "M03_iOS" {
					parts[0] = "M003_iOS"
				}
				if parts[0] == "M03_And" {
					parts[0] = "M003_And"
				}
				gameInfo, _ := game.GetBy(game.AppShowName.Eq(parts[0]))
				if gameInfo != nil {
					gameId = int(gameInfo.Id)
					cpGameId = gameInfo.CpGameId
				}
				countryCode = parts[2]

			}

			//投放账户时区 转为 北京时区
			timeParts := strings.Split(row.Hourly, "-")
			hour := strings.TrimSpace(timeParts[0])

			beforeTimezone := strings.ToLower(accountInfo.TimeZone)
			afterTimezone := "utc+0"
			beforeTime := timeFormat + " " + hour
			afterTime, err := utils.TimeZoneConvert(beforeTime, beforeTimezone, afterTimezone)

			if err != nil {
				utils.Debug("时区转换错误", err)
				continue
			}

			inserted := make(map[string]any)
			inserted["time"] = afterTime
			inserted["country_code"] = countryCode
			inserted["time_zone"] = afterTimezone

			inserted["channel_id"] = FB_CHANNEL_ID
			inserted["cp_game_id"] = cpGameId
			inserted["game_id"] = gameId
			inserted["account_id"] = accountId
			inserted["ad_account"] = accountInfo.AdAccount
			inserted["campaign_id"] = row.CampaignID
			inserted["campaign_name"] = row.CampaignName
			inserted["plan_id"] = row.AdsetID
			inserted["plan_name"] = row.AdsetName
			inserted["creative_id_origin"] = row.AdID
			inserted["creative_id"] = fmt.Sprintf("%s-%s-%s", row.CampaignID, row.AdsetID, row.AdID)

			hash := md5.Sum([]byte(inserted["creative_id"].(string)))
			inserted["creative_id_md5"] = hex.EncodeToString(hash[:])

			inserted["source_id"] = 4 // facebook
			actLog, _ := hw_device_active_source.GetBy(hw_device_active_source.CampaignId.Eq(inserted["campaign_id"].(string)).And(hw_device_active_source.GameId.Eq(inserted["game_id"].(int))))
			if actLog != nil {
				inserted["package_id"] = actLog.PackageId
			} else {
				inserted["package_id"] = 0
			}

			inserted["creative_name"] = row.AdName

			if row.Impressions == "" {
				row.Impressions = "0"
			}
			if row.Clicks == "" {
				row.Clicks = "0"
			}
			inserted["show"] = row.Impressions
			inserted["click"] = row.Clicks

			//安装量
			var install int
			install = 0

			if row.Actions != nil {
				for _, action := range row.Actions {
					actionType := action["action_type"].(string)
					if actionType == "mobile_app_install" && action["1d_click"] != nil {
						actionValue := action["1d_click"].(string)
						installValue, _ := strconv.Atoi(actionValue)
						install = install + installValue
					}
				}
			}
			inserted["install"] = install

			//媒体安装成本
			spendFloat, _ := strconv.ParseFloat(row.Spend, 64)
			if install > 0 {
				installCost := fmt.Sprintf("%.3f", spendFloat/float64(install))
				installCostF, _ := strconv.ParseFloat(installCost, 64)
				inserted["install_cost"] = installCostF
			} else {
				inserted["install_cost"] = 0
			}

			//点击安装率
			clicksFloat, _ := strconv.ParseFloat(row.Clicks, 64)
			if clicksFloat > 0 {
				inserted["click_install_rate"] = fmt.Sprintf("%.3f", float64(install)/clicksFloat)
			} else {
				inserted["click_install_rate"] = 0
			}

			//千次曝光安装量
			ImpressionsFloat, _ := strconv.ParseFloat(row.Impressions, 64)
			if ImpressionsFloat > 0 {
				inserted["install_per_show"] = fmt.Sprintf("%.3f", float64(install)/ImpressionsFloat*1000)
			} else {
				inserted["install_per_show"] = 0
			}

			inserted["update_time"] = time.Now().Format(time.DateTime)
			inserted["platform_id"] = platformId

			ctr, _ := strconv.ParseFloat(row.Ctr, 64)
			cpm, _ := strconv.ParseFloat(row.Cpm, 64)
			cost, _ := strconv.ParseFloat(row.Spend, 64)

			inserted["cost"] = cost
			// 返点后金额
			inserted["cost_discount"] = helper.GetDiscount(cost, afterTime, accountInfo)

			inserted["ctr"] = ctr
			inserted["cpm"] = cpm

			insertData = append(insertData, inserted)
		}

		if len(insertData) > 0 {
			_, _, e := plusQ.Db("data").InsertOrUpdateBatch("ad_cost_hour", insertData, true)
			if e != nil {
				plusQ.Logger().Error("facebook_grab_hour_err", uri)
				plusQ.Logger().Error("facebook_grab_hour_err", insertData)
				plusQ.Logger().Error("facebook_grab_hour_err", e)
			}
			this.insertConfData(insertData)
		}

		if res.Paging.Next == "" {
			finish = true
		} else {
			uri = res.Paging.Next
		}
	}

	return nil
}

func (this *FacebookGrabHour) sendRequest(url string) (*meta.FacebookResponse, error) {
	plusQ.Logger().Info("facebook_grab_cron", "发送请求："+url)

	result := &meta.FacebookResponse{}
	resp, err := this.getWithProxy(url)
	if err != nil {
		return result, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return result, err
	}
	utils.Debug("状态码", resp.StatusCode)
	if resp.StatusCode != http.StatusOK {
		return result, errors.New("请求失败")
	}
	if err := json.Unmarshal(body, result); err != nil {
		return result, err
	}

	fmt.Println(string(body))

	return result, nil
}

func (this *FacebookGrabHour) getWithProxy(targetURL string) (*http.Response, error) {
	var client *http.Client
	if os.Getenv("APP_ENV") == "dev" {
		proxyURL := "http://127.0.0.1:33210"
		proxy, err := url.Parse(proxyURL)
		if err != nil {
			return nil, err
		}
		client = &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxy),
			},
		}
	} else {
		client = &http.Client{
			Timeout: 30 * time.Second,
		}
	}

	return client.Get(targetURL)
}

func (this *FacebookGrabHour) insertConfData(data []map[string]any) {
	insertDataCampaign := make([]map[string]any, 0)
	insertDataPlan := make([]map[string]any, 0)
	insertDataCreative := make([]map[string]any, 0)

	for _, inserted := range data {
		insertDataCampaign = append(insertDataCampaign, map[string]any{
			"campaign_id":   inserted["campaign_id"],
			"campaign_name": inserted["campaign_name"],
			"update_time":   time.Now().Format(time.DateTime),
		})
		insertDataPlan = append(insertDataPlan, map[string]any{
			"campaign_id": inserted["campaign_id"],
			"plan_id":     inserted["plan_id"],
			"plan_name":   inserted["plan_name"],
			"update_time": time.Now().Format(time.DateTime),
		})
		insertDataCreative = append(insertDataCreative, map[string]any{
			"campaign_id":   inserted["campaign_id"],
			"plan_id":       inserted["plan_id"],
			"creative_id":   inserted["creative_id_md5"],
			"creative_name": inserted["creative_name"],
			"update_time":   time.Now().Format(time.DateTime),
			"country_id":    0,
			"os":            system.If(inserted["platform_id"].(int) == 2, "ios", "android"),
			"ext":           inserted["creative_id_origin"],
			"game_id":       inserted["game_id"],
			"package_id":    inserted["package_id"],
		})
	}
	if len(insertDataCampaign) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_campaign_conf", insertDataCampaign, true)
	}
	if len(insertDataPlan) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_plan_conf", insertDataPlan, true)
	}
	if len(insertDataCreative) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_creative_conf", insertDataCreative, true)
	}
}
