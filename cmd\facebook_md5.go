package cmd

import (
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/utils/system"
	"iaa_data/model/data/ad_cost"
	"time"
)

type FacebookMd5 struct {
}

func (this *FacebookMd5) Run(params map[string]string) error {
	list, _ := ad_cost.ListAll()
	insertConfData(list)
	return nil
}

func insertConfData(data []*ad_cost.AdCostModel) {
	insertDataCampaign := make([]map[string]any, 0)
	insertDataPlan := make([]map[string]any, 0)
	insertDataCreative := make([]map[string]any, 0)

	for _, inserted := range data {
		insertDataCampaign = append(insertDataCampaign, map[string]any{
			"campaign_id":   inserted.CampaignId,
			"campaign_name": inserted.CampaignName,
			"update_time":   time.Now().Format(time.DateTime),
		})
		insertDataPlan = append(insertDataPlan, map[string]any{
			"campaign_id": inserted.CampaignId,
			"plan_id":     inserted.PlanId,
			"plan_name":   inserted.PlanName,
			"update_time": time.Now().Format(time.DateTime),
		})
		insertDataCreative = append(insertDataCreative, map[string]any{
			"campaign_id":   inserted.CampaignId,
			"plan_id":       inserted.PlanId,
			"creative_id":   inserted.CreativeIdMd5,
			"creative_name": inserted.CreativeName,
			"update_time":   time.Now().Format(time.DateTime),
			"country_id":    0,
			"os":            system.If(inserted.PlatformId == 2, "ios", "android"),
			"ext":           inserted.CreativeIdOrigin,
			"game_id":       inserted.GameId,
			"package_id":    inserted.PackageId,
		})
	}
	if len(insertDataCampaign) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_campaign_conf", insertDataCampaign, true)
	}
	if len(insertDataPlan) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_plan_conf", insertDataPlan, true)
	}
	if len(insertDataCreative) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_creative_conf", insertDataCreative, true)
	}
}
