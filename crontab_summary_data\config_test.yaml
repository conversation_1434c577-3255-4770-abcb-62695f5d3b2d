listen_addr: 0.0.0.0:8083
log_dir: ./elk_log
console_log_file: ./iaa_data.log # 控制台日志文件路径， daemon 守护进程模式下有效, 配置了才能启用守护进程模式
config:
    # 缓存配置
    cache:
        default:
            type: redis
            host: **************:6379
            password: ''
            db: 0
    # 数据库配置
    sql_print: false # 是否打印sql语句
    db:
        default:
            host: **************
            port: 3307
            username: root
            password: 123xiaoshuo
            database_name: iaa_statistics
            charset: utf8mb4
            max_open_conns: 15
            max_idle_conns: 5
            max_lifetime: 300
        platform:
            host: **************
            port: 3307
            username: root
            password: 123xiaoshuo
            database_name: iaa_platform
            charset: utf8mb4
            max_open_conns: 15
            max_idle_conns: 5
            max_lifetime: 300
        data:
            host: **************
            port: 3307
            username: root
            password: 123xiaoshuo
            database_name: iaa_data
            charset: utf8mb4
            max_open_conns: 15
            max_idle_conns: 5
            max_lifetime: 300
    # 数据验证器配置
    validator:
        # 有且只有一个实例
        default:
            # 是否启用
            enable: true
    # 路由设置
    route:
        # 非权限控制路由
        public:
            - /api/sys/sys_user/login
            - /api/sys/sys_user/logout
        # 登录后所有用户可访问的路由
        login:
            - /api/sys/sys_user/update_password
            - /api/sys/sys_user/logout
            - /api/sys/sys_user/menu
            - /api/common/table_fields
            - /api/export/list
            - /api/common/options
    # 导出配置权限key
    export:
        authKey: 910app
        filePath: ./export_files
