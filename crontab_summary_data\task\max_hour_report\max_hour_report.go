package max_hour_report

import (
	"iaa_data/utils/constant"
	"iaa_data/utils/report_helper"
	"iaa_data/utils/sql_helper"
	"log"
	"time"
)

func InitFunc(args ...string) {
	GetHourReport()
}

// 获取max实时报表数据
func GetHourReport() {
	nowTime := time.Now()
	for _, key := range constant.MAX_KEY_LIST {
		params := make(map[string]interface{})
		params["api_key"] = key
		params["columns"] = getColumns()
		params["end"] = nowTime.Format("2006-01-02")
		params["start"] = nowTime.Add(-24 * time.Hour).Format("2006-01-02")
		url := constant.MAX_REPORT_HOUR_URL
		resultData := report_helper.GetReport(url, params)
		if resultData != nil {
			for _, item := range resultData.MaxReportItems {
				sql_helper.LogHelperInstance().HandleDataMapInsert("data", "ad_report_api", "ad_report_api", item, 30, 1)
			}
		}
	}
}

func Replay(start, end string) {
	for _, key := range constant.MAX_KEY_LIST {
		//startDate, _ := time.Parse(time.DateOnly, start)
		//endDate, _ := time.Parse(time.DateOnly, end)
		//for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {
		params := make(map[string]interface{})
		params["api_key"] = key
		params["columns"] = getColumns()
		params["start"] = start
		params["end"] = end
		url := constant.MAX_REPORT_HOUR_URL
		resultData := report_helper.GetReport(url, params)
		if resultData != nil {
			for _, item := range resultData.MaxReportItems {
				sql_helper.LogHelperInstance().HandleDataMapInsert("data", "ad_report_api", "ad_report_api", item, 150, 2000)
			}
		} else {
			log.Println("没有数据")
		}
		//}
	}
	sql_helper.LogHelperInstance().HandleDataMapEnd("data", "ad_report_api", "ad_report_api")
}

func getColumns() string {
	var columns []string
	columns = append(columns, "ad_format")
	columns = append(columns, "ad_unit_waterfall_name")
	columns = append(columns, "application")
	columns = append(columns, "country")
	columns = append(columns, "custom_network_name")
	columns = append(columns, "day")
	columns = append(columns, "ecpm")
	columns = append(columns, "estimated_revenue")
	columns = append(columns, "hour")
	columns = append(columns, "impressions")
	columns = append(columns, "max_ad_unit_id")
	columns = append(columns, "max_ad_unit_test")
	columns = append(columns, "max_placement")
	columns = append(columns, "network")
	columns = append(columns, "network_placement")
	columns = append(columns, "package_name")
	columns = append(columns, "platform")
	columns = append(columns, "store_id")

	columnsStr := ""
	for _, val := range columns {
		if columnsStr == "" {
			columnsStr = val
		} else {
			columnsStr = columnsStr + "," + val
		}
	}
	return columnsStr
}
