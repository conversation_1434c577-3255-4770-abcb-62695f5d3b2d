package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/plus/server"
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/provider"
	"context"
	"iaa_data/callback_api"
	"log"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	// 加载配置文件
	configFile2 := "config/config_call.yaml"
	_, err := plus.LoadServerConfigDefault(configFile2)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}

	provider.Register() //注册服务

	// 关闭信号
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM, os.Kill)
	defer stop()
	//注册接口
	services := server.NewHttpServer(ctx,
		callback_api.NewService(),
	)
	//启动服务
	err = plus.New(ctx, services).Run("api_project_call")
	if err != nil {
		plusQ.Logger().<PERSON>ert("服务关闭", err)
	}
}
