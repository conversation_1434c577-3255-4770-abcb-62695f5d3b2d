package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/provider"
	"fmt"
	"iaa_data/utils/sql_helper"
	"log"
	"time"
)

func main() {
	configFile := "config/config_hw.yaml"
	_, err := plus.LoadServerConfigDefault(configFile)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}
	plus.AppConfig.ConsoleLogFile = "./log_common/crontab_other/demo.log"
	provider.Register() //注册服务

	//sql := "UPDATE hw_sdk_active_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//
	//sql = "UPDATE hw_sdk_ad_click_log SET source_id = 2 WHERE source_id = 0"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_ad_close_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_ad_error_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_ad_loaded_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_ad_request_log  SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_ad_revenue_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_ad_reward_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_ad_show_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_start_log SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_user_active SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_user_login SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_user_login_cp_game  SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql = "UPDATE hw_sdk_user_login_package SET source_id = 2 WHERE source_id = 0;"
	//plusQ.Db("data").Update(sql)
	//sql := "delete from ad_active_user_log  WHERE tday >= 20250515;"
	//plusQ.Db("data").Update(sql)

	//sql = "delete from iaa_ad_position_report where tday =********"
	//plusQ.Db().Update(sql)
	//
	//sql = "delete from iaa_ad_roi_realtime where tday =********"
	//plusQ.Db().Update(sql)
	//
	//sql = "delete from iaa_report_operation where tday =********"
	//plusQ.Db().Update(sql)
	//nowTime := time.Now()
	//if nowTime.Hour() >= 9 && nowTime.Hour() < 10 {
	//	if day_check_helper.CheckHour7("test==========") {
	//		log.Println("开始汇总")
	//	} else {
	//		log.Println("<UNK>")
	//	}
	//}
	//if 1 == 1 {
	//	return
	//}

	log.Println("==============kai")
	sql := "SELECT r.*  FROM hw_sdk_user_reg r LEFT JOIN hw_sdk_user_login l ON r.core_account = l.core_account WHERE l.core_account IS NULL;"

	list, err := plusQ.Db("data").List(sql)
	if err != nil {
		log.Println("==============kai", err.Error())
		return
	}
	for _, v := range list {
		//log.Println("==============00")
		//2025-05-14T18:34:36+08:00
		//2006-01-02T15:04:05+08:00
		//2006-01-02 15:04:05 +0800 CST
		//2025-05-14 18:34:37 +0800 CST
		t, err := time.Parse("2006-01-02 15:04:05 +0800 CST", fmt.Sprint(v["time_bj"]))
		if err != nil {
			log.Println("==============00", err.Error())
			return
		}
		insertUserLoginLog(v, t.Format(time.DateTime))
		insertUserNewLoginPackageLog(v, t.Format(time.DateTime))
		insertUserNewLoginCpGameLog(v, t.Format(time.DateTime))
		insertUserActivateLog(v, t.Format(time.DateTime))
		//break
	}
}

// 数据修复
func fixRma() {
	nowTime := time.Now()
	startTime := nowTime.Format("2006-01-02") + " 00:00:00"
	endTime := nowTime.Format("2006-01-02") + " 23:59:59"

	sql := "select B.* , B.TIME_BJ as login_time, A.time_bj as revenue_time" +
		" from hw_sdk_ad_revenue_log as A join hw_sdk_user_login_package as B using(game_id," +
		" PACKAGE_ID, CORE_ACCOUNT) where " +
		" A.time_bj  between '" + startTime + "' and '" + endTime + "' " +
		" and B.time_bj between '" + startTime + "' and '" + endTime + "' " +
		" and B.cp_game_id =16  group by A.core_account  " +
		" having " +
		" A.time_bj<B.time_bj"
	list, _ := plusQ.Db("data").List(sql)
	if len(list) > 0 {
		for _, v := range list {
			newTime := fmt.Sprint(v["revenue_time"])
			v["time_bj"] = newTime
			delete(v, "revenue_time")
			delete(v, "login_time")
			sql_helper.InsertOrUpdate("data", "hw_sdk_user_login_package", v)
		}

	}
}

func insertUserLoginLog(item map[string]interface{}, timeBj string) error {
	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["time_local"] = timeBj
	info["time_bj"] = timeBj
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_server"] = timeBj
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["device_type"] = item["device_type"]
	info["os"] = item["os"]
	info["os_version"] = item["os_version"]
	info["sdk_version"] = item["sdk_version"]
	info["game_version"] = item["game_version"]
	info["network_type"] = item["network_type"]
	info["mobile_type"] = item["mobile_type"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	info["login_type"] = item["login_type"]
	sql_helper.InsertOrUpdate("data", "hw_sdk_user_login", info)
	return nil
}

func insertUserNewLoginPackageLog(item map[string]interface{}, timeBj string) {
	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["time_local"] = timeBj
	info["time_bj"] = timeBj
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_server"] = timeBj
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["device_type"] = item["device_type"]
	info["os"] = item["os"]
	info["os_version"] = item["os_version"]
	info["sdk_version"] = item["sdk_version"]
	info["game_version"] = item["game_version"]
	info["network_type"] = item["network_type"]
	info["mobile_type"] = item["mobile_type"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	info["login_type"] = item["login_type"]
	sql_helper.InsertOrUpdate("data", "hw_sdk_user_login_package", info)
}

func insertUserNewLoginCpGameLog(item map[string]interface{}, timeBj string) {

	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["time_local"] = timeBj
	info["time_bj"] = timeBj
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_server"] = timeBj
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["device_type"] = item["device_type"]
	info["os"] = item["os"]
	info["os_version"] = item["os_version"]
	info["sdk_version"] = item["sdk_version"]
	info["game_version"] = item["game_version"]
	info["network_type"] = item["network_type"]
	info["mobile_type"] = item["mobile_type"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	info["login_type"] = item["login_type"]
	sql_helper.InsertOrUpdate("data", "hw_sdk_user_login_cp_game", info)
}

func insertUserActivateLog(item map[string]interface{}, timeBj string) {
	t, err := time.Parse(time.DateTime, timeBj)
	if err != nil {
		return
	}
	tDayStr := t.Format(time.DateOnly)
	tDay, _ := time.Parse(time.DateOnly, tDayStr)

	tDayLocalStr := tDayStr
	tDayLocal, _ := time.Parse(time.DateOnly, tDayLocalStr)

	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_bj"] = timeBj
	info["tday"] = tDay.Format(time.DateOnly)
	info["local_tday"] = tDayLocal.Format(time.DateOnly)
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["os"] = item["os"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	sql_helper.InsertOrUpdateAllFiled("data", "hw_sdk_user_active", "tday|package_id|core_account", info)
}
