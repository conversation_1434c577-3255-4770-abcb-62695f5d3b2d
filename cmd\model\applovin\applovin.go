package applovin

import "encoding/json"

type AppLovinReport struct {
	Code    int          `json:"code"`
	Count   int          `json:"count"`
	Results []ResultItem `json:"results"`
}
type ResultItem struct {
	Ad                 string      `json:"ad"`
	AdID               string      `json:"ad_id"`
	Campaign           string      `json:"campaign"`
	CampaignIDExternal string      `json:"campaign_id_external"`
	Country            string      `json:"country"`
	CreativeSet        string      `json:"creative_set"`
	CreativeSetID      string      `json:"creative_set_id"`
	Day                string      `json:"day"`
	Hour               string      `json:"hour"`
	Clicks             json.Number `json:"clicks"`
	Conversions        json.Number `json:"conversions"`
	Cost               json.Number `json:"cost"`
	Ctr                json.Number `json:"ctr"`
	Impressions        json.Number `json:"impressions"`
}
