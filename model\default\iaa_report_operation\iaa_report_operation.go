package iaa_report_operation

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("default")
	Table    = orm.Table("iaa_report_operation")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 主键
	Id = property.Property("id")
	// Tday 统计日期
	Tday = property.Property("tday")
	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone = property.Property("time_zone")
	// CpGameId 游戏原名id
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏id
	GameId = property.Property("game_id")
	// PackageId 包号
	PackageId = property.Property("package_id")
	// SourceId 媒体id
	SourceId = property.Property("source_id")
	// ChannelId 推广渠道ID
	ChannelId = property.Property("channel_id")
	// AdType 广告类型
	AdType = property.Property("ad_type")
	// NewUserNum 新增用户
	NewUserNum = property.Property("new_user_num")
	// ActiveNum 活跃用户
	ActiveNum = property.Property("active_num")
	// NewloginUserYesterdayRemain 昨日新增次留
	NewloginUserYesterdayRemain = property.Property("newlogin_user_yesterday_remain")
	// AdUser DEU，每日广告观看人数
	AdUser = property.Property("ad_user")
	// AdShowNum 广告展示数
	AdShowNum = property.Property("ad_show_num")
	// AdRequestNum 广告请求数
	AdRequestNum = property.Property("ad_request_num")
	// AdFillNum 广告填充数
	AdFillNum = property.Property("ad_fill_num")
	// CountryId 国家
	CountryId = property.Property("country_id")
	// Ecpm 广告ecpm
	Ecpm = property.Property("ecpm")
	// Revenue 广告收入
	Revenue = property.Property("revenue")
	// NewUserRevenue 新用户收入
	NewUserRevenue = property.Property("new_user_revenue")
	// OldUserRevenue 老用户收入
	OldUserRevenue = property.Property("old_user_revenue")
	// IapPayMoney IAP付费金额
	IapPayMoney = property.Property("iap_pay_money")
	// NewIapPayMoney 新用户IAP付费金额
	NewIapPayMoney = property.Property("new_iap_pay_money")
	// Utime 更新时间
	Utime = property.Property("utime")
)

var FieldsAll = Fields(Id, Tday, TimeZone, CpGameId, GameId, PackageId, SourceId, ChannelId, AdType, NewUserNum, ActiveNum, NewloginUserYesterdayRemain, AdUser, AdShowNum, AdRequestNum, AdFillNum, CountryId, Ecpm, Revenue, NewUserRevenue, OldUserRevenue, IapPayMoney, NewIapPayMoney, Utime)
var NonePrimaryFields = Fields(Tday, TimeZone, CpGameId, GameId, PackageId, SourceId, ChannelId, AdType, NewUserNum, ActiveNum, NewloginUserYesterdayRemain, AdUser, AdShowNum, AdRequestNum, AdFillNum, CountryId, Ecpm, Revenue, NewUserRevenue, OldUserRevenue, IapPayMoney, NewIapPayMoney, Utime)
var NoneAutoIncrementFields = Fields(Tday, TimeZone, CpGameId, GameId, PackageId, SourceId, ChannelId, AdType, NewUserNum, ActiveNum, NewloginUserYesterdayRemain, AdUser, AdShowNum, AdRequestNum, AdFillNum, CountryId, Ecpm, Revenue, NewUserRevenue, OldUserRevenue, IapPayMoney, NewIapPayMoney, Utime)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// IaaReportOperationModel IAA游戏运营报表
type IaaReportOperationModel struct {
	// Id 主键
	Id uint64 `orm:"id" json:"id"`

	// Tday 统计日期
	Tday int `orm:"tday" json:"tday"`

	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone int `orm:"time_zone" json:"time_zone"`

	// CpGameId 游戏原名id
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏id
	GameId int `orm:"game_id" json:"game_id"`

	// PackageId 包号
	PackageId int `orm:"package_id" json:"package_id"`

	// SourceId 媒体id
	SourceId int `orm:"source_id" json:"source_id"`

	// ChannelId 推广渠道ID
	ChannelId int `orm:"channel_id" json:"channel_id"`

	// AdType 广告类型
	AdType string `orm:"ad_type" json:"ad_type"`

	// NewUserNum 新增用户
	NewUserNum int `orm:"new_user_num" json:"new_user_num"`

	// ActiveNum 活跃用户
	ActiveNum int `orm:"active_num" json:"active_num"`

	// NewloginUserYesterdayRemain 昨日新增次留
	NewloginUserYesterdayRemain int `orm:"newlogin_user_yesterday_remain" json:"newlogin_user_yesterday_remain"`

	// AdUser DEU，每日广告观看人数
	AdUser int `orm:"ad_user" json:"ad_user"`

	// AdShowNum 广告展示数
	AdShowNum int `orm:"ad_show_num" json:"ad_show_num"`

	// AdRequestNum 广告请求数
	AdRequestNum int `orm:"ad_request_num" json:"ad_request_num"`

	// AdFillNum 广告填充数
	AdFillNum int `orm:"ad_fill_num" json:"ad_fill_num"`

	// CountryId 国家
	CountryId int `orm:"country_id" json:"country_id"`

	// Ecpm 广告ecpm
	Ecpm float32 `orm:"ecpm" json:"ecpm"`

	// Revenue 广告收入
	Revenue float32 `orm:"revenue" json:"revenue"`

	// NewUserRevenue 新用户收入
	NewUserRevenue float32 `orm:"new_user_revenue" json:"new_user_revenue"`

	// OldUserRevenue 老用户收入
	OldUserRevenue float32 `orm:"old_user_revenue" json:"old_user_revenue"`

	// IapPayMoney IAP付费金额
	IapPayMoney float32 `orm:"iap_pay_money" json:"iap_pay_money"`

	// NewIapPayMoney 新用户IAP付费金额
	NewIapPayMoney float32 `orm:"new_iap_pay_money" json:"new_iap_pay_money"`

	// Utime 更新时间
	Utime *CustomTime `orm:"utime" json:"utime"`
}

type PagedResult struct {
	Records      []*IaaReportOperationModel `json:"list"`
	PageNum      int                        `json:"page"`
	PageSize     int                        `json:"page_size"`
	TotalPages   int                        `json:"total_pages"`
	TotalRecords int                        `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:                          1,
	Tday:                        2,
	TimeZone:                    3,
	CpGameId:                    4,
	GameId:                      5,
	PackageId:                   6,
	SourceId:                    7,
	ChannelId:                   8,
	AdType:                      9,
	NewUserNum:                  10,
	ActiveNum:                   11,
	NewloginUserYesterdayRemain: 12,
	AdUser:                      13,
	AdShowNum:                   14,
	AdRequestNum:                15,
	AdFillNum:                   16,
	CountryId:                   17,
	Ecpm:                        18,
	Revenue:                     19,
	NewUserRevenue:              20,
	OldUserRevenue:              21,
	IapPayMoney:                 22,
	NewIapPayMoney:              23,
	Utime:                       24,
}

func (m *IaaReportOperationModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *IaaReportOperationModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.SourceId, m.ChannelId, m.AdType, m.NewUserNum, m.ActiveNum, m.NewloginUserYesterdayRemain, m.AdUser, m.AdShowNum, m.AdRequestNum, m.AdFillNum, m.CountryId, m.Ecpm, m.Revenue, m.NewUserRevenue, m.OldUserRevenue, m.IapPayMoney, m.NewIapPayMoney, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaReportOperationModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.SourceId, m.ChannelId, m.AdType, m.NewUserNum, m.ActiveNum, m.NewloginUserYesterdayRemain, m.AdUser, m.AdShowNum, m.AdRequestNum, m.AdFillNum, m.CountryId, m.Ecpm, m.Revenue, m.NewUserRevenue, m.OldUserRevenue, m.IapPayMoney, m.NewIapPayMoney, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaReportOperationModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *IaaReportOperationModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.SourceId, m.ChannelId, m.AdType, m.NewUserNum, m.ActiveNum, m.NewloginUserYesterdayRemain, m.AdUser, m.AdShowNum, m.AdRequestNum, m.AdFillNum, m.CountryId, m.Ecpm, m.Revenue, m.NewUserRevenue, m.OldUserRevenue, m.IapPayMoney, m.NewIapPayMoney, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaReportOperationModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *IaaReportOperationModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.SourceId, m.ChannelId, m.AdType, m.NewUserNum, m.ActiveNum, m.NewloginUserYesterdayRemain, m.AdUser, m.AdShowNum, m.AdRequestNum, m.AdFillNum, m.CountryId, m.Ecpm, m.Revenue, m.NewUserRevenue, m.OldUserRevenue, m.IapPayMoney, m.NewIapPayMoney, m.Utime), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaReportOperationModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *IaaReportOperationModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.SourceId, m.ChannelId, m.AdType, m.NewUserNum, m.ActiveNum, m.NewloginUserYesterdayRemain, m.AdUser, m.AdShowNum, m.AdRequestNum, m.AdFillNum, m.CountryId, m.Ecpm, m.Revenue, m.NewUserRevenue, m.OldUserRevenue, m.IapPayMoney, m.NewIapPayMoney, m.Utime), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.SourceId, m.ChannelId, m.AdType, m.NewUserNum, m.ActiveNum, m.NewloginUserYesterdayRemain, m.AdUser, m.AdShowNum, m.AdRequestNum, m.AdFillNum, m.CountryId, m.Ecpm, m.Revenue, m.NewUserRevenue, m.OldUserRevenue, m.IapPayMoney, m.NewIapPayMoney, m.Utime))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaReportOperationModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *IaaReportOperationModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *IaaReportOperationModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *IaaReportOperationModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *IaaReportOperationModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *IaaReportOperationModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*IaaReportOperationModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*IaaReportOperationModel, error) {

	modelList := make([]*IaaReportOperationModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaReportOperationModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.SourceId, &m.ChannelId, &m.AdType, &m.NewUserNum, &m.ActiveNum, &m.NewloginUserYesterdayRemain, &m.AdUser, &m.AdShowNum, &m.AdRequestNum, &m.AdFillNum, &m.CountryId, &m.Ecpm, &m.Revenue, &m.NewUserRevenue, &m.OldUserRevenue, &m.IapPayMoney, &m.NewIapPayMoney, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint64) (*IaaReportOperationModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint64) (*IaaReportOperationModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint64) (*IaaReportOperationModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint64) (*IaaReportOperationModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*IaaReportOperationModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*IaaReportOperationModel, error) {

	m := &IaaReportOperationModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.SourceId, &m.ChannelId, &m.AdType, &m.NewUserNum, &m.ActiveNum, &m.NewloginUserYesterdayRemain, &m.AdUser, &m.AdShowNum, &m.AdRequestNum, &m.AdFillNum, &m.CountryId, &m.Ecpm, &m.Revenue, &m.NewUserRevenue, &m.OldUserRevenue, &m.IapPayMoney, &m.NewIapPayMoney, &m.Utime)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*IaaReportOperationModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*IaaReportOperationModel, error) {

	modelList := make([]*IaaReportOperationModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaReportOperationModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.SourceId, &m.ChannelId, &m.AdType, &m.NewUserNum, &m.ActiveNum, &m.NewloginUserYesterdayRemain, &m.AdUser, &m.AdShowNum, &m.AdRequestNum, &m.AdFillNum, &m.CountryId, &m.Ecpm, &m.Revenue, &m.NewUserRevenue, &m.OldUserRevenue, &m.IapPayMoney, &m.NewIapPayMoney, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*IaaReportOperationModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*IaaReportOperationModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &IaaReportOperationModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.SourceId, &m.ChannelId, &m.AdType, &m.NewUserNum, &m.ActiveNum, &m.NewloginUserYesterdayRemain, &m.AdUser, &m.AdShowNum, &m.AdRequestNum, &m.AdFillNum, &m.CountryId, &m.Ecpm, &m.Revenue, &m.NewUserRevenue, &m.OldUserRevenue, &m.IapPayMoney, &m.NewIapPayMoney, &m.Utime)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint64) (*IaaReportOperationModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint64) (*IaaReportOperationModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*IaaReportOperationModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*IaaReportOperationModel, error) {

	m := &IaaReportOperationModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*IaaReportOperationModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*IaaReportOperationModel, error) {

	modelList := make([]*IaaReportOperationModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &IaaReportOperationModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*IaaReportOperationModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*IaaReportOperationModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*IaaReportOperationModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*IaaReportOperationModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.Tday, d.TimeZone, d.CpGameId, d.GameId, d.PackageId, d.SourceId, d.ChannelId, d.AdType, d.NewUserNum, d.ActiveNum, d.NewloginUserYesterdayRemain, d.AdUser, d.AdShowNum, d.AdRequestNum, d.AdFillNum, d.CountryId, d.Ecpm, d.Revenue, d.NewUserRevenue, d.OldUserRevenue, d.IapPayMoney, d.NewIapPayMoney, d.Utime))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "主键",
		},
		{
			"dataIndex": "tday",
			"title":     "统计日期",
		},
		{
			"dataIndex": "time_zone",
			"title":     "时区: 1=北京，2=欧洲，3=美国",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名id",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏id",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号",
		},
		{
			"dataIndex": "source_id",
			"title":     "媒体id",
		},
		{
			"dataIndex": "channel_id",
			"title":     "推广渠道ID",
		},
		{
			"dataIndex": "ad_type",
			"title":     "广告类型",
		},
		{
			"dataIndex": "new_user_num",
			"title":     "新增用户",
		},
		{
			"dataIndex": "active_num",
			"title":     "活跃用户",
		},
		{
			"dataIndex": "newlogin_user_yesterday_remain",
			"title":     "昨日新增次留",
		},
		{
			"dataIndex": "ad_user",
			"title":     "DEU，每日广告观看人数",
		},
		{
			"dataIndex": "ad_show_num",
			"title":     "广告展示数",
		},
		{
			"dataIndex": "ad_request_num",
			"title":     "广告请求数",
		},
		{
			"dataIndex": "ad_fill_num",
			"title":     "广告填充数",
		},
		{
			"dataIndex": "country_id",
			"title":     "国家",
		},
		{
			"dataIndex": "ecpm",
			"title":     "广告ecpm",
		},
		{
			"dataIndex": "revenue",
			"title":     "广告收入",
		},
		{
			"dataIndex": "new_user_revenue",
			"title":     "新用户收入",
		},
		{
			"dataIndex": "old_user_revenue",
			"title":     "老用户收入",
		},
		{
			"dataIndex": "iap_pay_money",
			"title":     "IAP付费金额",
		},
		{
			"dataIndex": "new_iap_pay_money",
			"title":     "新用户IAP付费金额",
		},
		{
			"dataIndex": "utime",
			"title":     "更新时间",
		}}
}
