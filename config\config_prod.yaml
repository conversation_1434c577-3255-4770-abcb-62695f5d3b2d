listen_addr: 0.0.0.0:8082
log_dir: ./elk_log
console_log_file: ./iaa_data.log # 控制台日志文件路径， daemon 守护进程模式下有效, 配置了才能启用守护进程模式
config:
    # 缓存配置
    cache:
        default:
            type: redis
            host: 127.0.0.1:6379
            password: ''
            db: 0
    # 数据库配置
    sql_print: true # 是否打印sql语句
    db:
        platform:
            host: 127.0.0.1
            port: 3306
            username: project_iaa_data
            password: dataCCr123488#
            database_name: iaa_platform
            charset: utf8mb4
            max_open_conns: 15
            max_idle_conns: 5
            max_lifetime: 300
        data:
            host: 127.0.0.1
            port: 3306
            username: project_iaa_data
            password: dataCCr123488#
            database_name: iaa_data
            charset: utf8mb4
            max_open_conns: 15
            max_idle_conns: 5
            max_lifetime: 300