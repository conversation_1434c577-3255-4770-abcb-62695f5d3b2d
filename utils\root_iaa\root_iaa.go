package root_iaa

import (
	"910.com/plus2.git/utils/string_util"
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"log"
	"net/http"
	"strconv"
	"time"
)

// 机器人发送信息地址
var ROBOT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key="

type RobotHelper struct {
	MTimeMap map[string]int64
}

var singleton *RobotHelper

func RobotInstance() *RobotHelper {
	if singleton == nil {
		singleton = &RobotHelper{}
	}
	return singleton
}

func CheckData(mTime, tag string) {
	nTime := time.Now().Format(time.DateTime)
	if isTimeDifferenceMoreThan10Minutes(mTime, nTime, time.DateTime) {
		RobotInstance().Send(tag + "数据存在消耗延迟")
	}
}

func (m *RobotHelper) Send(content string) {

	if m.MTimeMap == nil {
		m.MTimeMap = make(map[string]int64)
	}

	key := string_util.Md5(content)
	mTime, ok := m.MTimeMap[key]
	t1 := time.Now().Unix()
	if ok {
		if t1-mTime > 60*30 {
			m.MTimeMap[key] = t1
			qyWxBot("f9339973-4375-4332-8566-a72ac7ffde95", content)
		}
	} else {
		m.MTimeMap[key] = t1
		qyWxBot("f9339973-4375-4332-8566-a72ac7ffde95", content)
	}

}

// 企业微信机器人发送
func qyWxBot(key, content string) error {
	var webHook = ROBOT_URL + key
	var mapParam = map[string]any{}
	var markdown = map[string]any{}
	mapParam["msgtype"] = "markdown"
	markdown["content"] = content
	mapParam["markdown"] = markdown
	body, err := httpPostData(webHook, mapParam)
	if err != nil {
		return err
	}

	log.Println("============", string(body))
	return nil
}
func httpPostData(mUrl string, param interface{}) ([]byte, error) {
	postByte, err := json.Marshal(param)
	response, err := http.Post(mUrl, "application/json", bytes.NewBuffer(postByte))
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	if response.StatusCode == 200 {
		body, err := io.ReadAll(response.Body)
		if err != nil {
			return nil, err
		}
		return body, nil
	}
	return nil, errors.New("code =" + strconv.Itoa(response.StatusCode))
}

func isTimeDifferenceMoreThan10Minutes(timeStr1, timeStr2 string, layout string) bool {
	// 解析时间字符串
	t1, err := time.Parse(layout, timeStr1)
	if err != nil {
		return false
	}
	t2, err := time.Parse(layout, timeStr2)
	if err != nil {
		return false
	}

	// 计算时间差
	duration := t1.Sub(t2)
	absDuration := duration.Abs()

	// 判断时间差是否超过10分钟
	return absDuration > 10*time.Minute
}
