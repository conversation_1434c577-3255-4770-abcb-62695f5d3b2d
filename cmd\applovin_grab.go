package cmd

/*
命令执行：/path/to/cli  -op applovin_grab -start_date 2025-04-28 -end_date 2025-04-28
*/

import (
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/utils/system"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"iaa_data/cmd/helper"
	"iaa_data/cmd/model/applovin"
	"iaa_data/model/data/ad_account_conf"
	"iaa_data/model/data/hw_device_active_source"
	"iaa_data/model/platform/game"
	"iaa_data/utils"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

const APL_CHANNEL_ID = 5

type ApplovinGrab struct {
}

func (this *ApplovinGrab) Run(params map[string]string) error {
	utils.Debug("命令行参数", params)
	var ok bool
	var startDate string
	var endDate string
	startDate, ok = params["start_date"]
	if !ok {
		startDate = time.Now().AddDate(0, 0, -1).Format(time.DateOnly)
	}
	endDate, ok = params["end_date"]
	if !ok {
		endDate = time.Now().Format(time.DateOnly)
	}
	accountId, ok := params["account_id"]
	if !ok {
		accountList, _ := ad_account_conf.ListBy(ad_account_conf.ChannelId.Eq(APL_CHANNEL_ID).And(ad_account_conf.Status.Eq(1)))
		for _, accountInfo := range accountList {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("applovin_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
		}
	} else {
		accountInfo, _ := ad_account_conf.GetBy(ad_account_conf.ChannelId.Eq(APL_CHANNEL_ID).And(ad_account_conf.AccountId.Eq(accountId)).And(ad_account_conf.Status.Eq(1)))
		if accountInfo != nil {
			utils.Debug("开始抓取", accountInfo.AdAccount)
			err := this.fetch(accountInfo, startDate, endDate)
			if err != nil {
				plusQ.Logger().Error("applovin_grab_err", fmt.Errorf("%v", err))
				utils.Debug("发生错误", err)
			}
		}
	}

	return nil
}
func (this *ApplovinGrab) fetch(accountInfo *ad_account_conf.AdAccountConfModel, startDate string, endDate string) error {
	apiKey := "ehRuQ04TldQaVk73GpKWIQazBx5QfTlcveAOT9IHZDxMdhVfhnvQBWSIQMTGFUVoTAsE_CCEKwpQ9H1yD62i98"

	flag := true
	limit := 1000
	page := 1

	for flag {
		params := url.Values{}
		params.Add("api_key", apiKey)
		params.Add("start", startDate)
		params.Add("end", endDate)
		params.Add("format", "json")
		params.Add("report_type", "advertiser")
		params.Add("not_zero", "1")
		params.Add("limit", strconv.Itoa(limit))
		params.Add("offset", strconv.Itoa((page-1)*limit))
		params.Add("columns", "day,ad,ad_creative_type,ad_id,campaign,campaign_id_external,creative_set,creative_set_id,country,impressions,clicks,conversions,cost,ctr")

		apiURL := "https://r.applovin.com/probabilisticReport?" + params.Encode()

		plusQ.Logger().Info("applovin_grab_cron", "发送请求："+apiURL)

		req, err := http.NewRequest("GET", apiURL, nil)
		if err != nil {
			return fmt.Errorf("创建请求失败: %v", err)
		}
		var client *http.Client
		if os.Getenv("APP_ENV") == "dev" {
			proxyURL := "http://127.0.0.1:33210"
			proxy, _ := url.Parse(proxyURL)
			client = &http.Client{
				Timeout: 180 * time.Second,
				Transport: &http.Transport{
					Proxy: http.ProxyURL(proxy),
				},
			}
		} else {
			client = &http.Client{
				Timeout: 180 * time.Second,
			}
		}
		resp, err := client.Do(req)
		if err != nil {
			return fmt.Errorf("HTTP请求失败: %v", err)
		}
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("读取响应失败: %v", err)
		}
		if resp.StatusCode != 200 {
			return fmt.Errorf("接口响应错误: %v", resp.StatusCode)
		}
		_ = resp.Body.Close()
		var report applovin.AppLovinReport
		if err := json.Unmarshal(body, &report); err != nil {
			return fmt.Errorf("JSON解析失败: %v", err)
		}

		if report.Count == 0 || report.Count < limit {
			flag = false
		}
		page = page + 1

		utils.Debug("返回条数", report.Count)

		insertData := make([]map[string]any, 0)
		for _, item := range report.Results {

			platformId := 1
			gameId := 0
			cpGameId := 0
			countryCode := strings.ToUpper(item.Country)

			// Mini Heroes_AND-AND-US-D7ROAS-ALL-AP-1211
			parts := strings.Split(item.Campaign, "-")
			if len(parts) >= 2 {
				platform := strings.ToLower(parts[1])
				if platform == "ios" {
					platformId = 2
				}
				gameInfo, _ := game.GetBy(game.AppShowName.Eq(parts[0]))
				if gameInfo != nil {
					gameId = int(gameInfo.Id)
					cpGameId = gameInfo.CpGameId
				}

			}

			inserted := make(map[string]any)
			// 基本信息
			inserted["source_id"] = 12
			inserted["country_code"] = countryCode
			inserted["time"] = item.Day
			inserted["channel_id"] = APL_CHANNEL_ID
			inserted["cp_game_id"] = cpGameId
			inserted["game_id"] = gameId
			actLog, _ := hw_device_active_source.GetBy(hw_device_active_source.CampaignId.Eq(item.CampaignIDExternal).And(hw_device_active_source.GameId.Eq(inserted["game_id"].(int))))
			if actLog != nil {
				inserted["package_id"] = actLog.PackageId
			} else {
				inserted["package_id"] = 0
			}

			inserted["account_id"] = accountInfo.AccountId
			inserted["ad_account"] = accountInfo.AdAccount
			inserted["platform_id"] = platformId
			inserted["time_zone"] = strings.ToLower(accountInfo.TimeZone)
			inserted["update_time"] = time.Now().Format(time.DateTime)
			// 广告信息
			inserted["campaign_id"] = item.CampaignIDExternal
			inserted["campaign_name"] = item.Campaign

			hash := md5.Sum([]byte(item.CampaignIDExternal + "_" + item.Ad))

			inserted["plan_id"] = hex.EncodeToString(hash[:])
			inserted["plan_name"] = item.CreativeSet
			inserted["creative_id_origin"] = hex.EncodeToString(hash[:])

			inserted["creative_id"] = fmt.Sprintf("%s-%s-%s", inserted["campaign_id"].(string), inserted["plan_id"].(string), inserted["creative_id_origin"].(string))
			hash2 := md5.Sum([]byte(inserted["creative_id"].(string)))
			inserted["creative_id_md5"] = hex.EncodeToString(hash2[:])

			inserted["creative_name"] = item.Ad
			// 广告指标
			inserted["show"], _ = item.Impressions.Int64()
			inserted["click"], _ = item.Clicks.Int64()
			inserted["install"], _ = item.Conversions.Int64()
			cost, _ := item.Cost.Float64()
			inserted["cost"] = cost
			// 返点后金额
			inserted["cost_discount"] = helper.GetDiscount(cost, item.Day, accountInfo)

			inserted["ctr"], _ = item.Ctr.Float64()
			//媒体安装成本
			install, _ := item.Conversions.Float64()
			if install > 0 {
				inserted["install_cost"] = cost / install
			} else {
				inserted["install_cost"] = 0
			}

			//点击安装率
			clicks, _ := item.Clicks.Float64()
			if clicks > 0 {
				inserted["click_install_rate"] = install / clicks
			} else {
				inserted["click_install_rate"] = 0
			}

			//千次曝光安装量
			show, _ := item.Impressions.Float64()
			if show > 0 {
				inserted["install_per_show"] = install / show * 1000
			} else {
				inserted["install_per_show"] = 0
			}
			// cpm
			if show > 0 {
				inserted["cpm"] = cost / show * 1000
			} else {
				inserted["cpm"] = 0
			}

			insertData = append(insertData, inserted)
		}

		if len(insertData) > 0 {
			_, _, e := plusQ.Db("data").InsertOrUpdateBatch("ad_cost", insertData, true)
			if e != nil {
				plusQ.Logger().Error("applovin_grab_err", insertData)
				plusQ.Logger().Error("applovin_grab_err", e)
			}
			this.insertConfData(insertData)
		}
	}
	return nil
}

func (this *ApplovinGrab) insertConfData(data []map[string]any) {
	insertDataCampaign := make([]map[string]any, 0)
	insertDataPlan := make([]map[string]any, 0)
	insertDataCreative := make([]map[string]any, 0)

	for _, inserted := range data {
		insertDataCampaign = append(insertDataCampaign, map[string]any{
			"campaign_id":   inserted["campaign_id"],
			"campaign_name": inserted["campaign_name"],
			"update_time":   time.Now().Format(time.DateTime),
		})
		insertDataPlan = append(insertDataPlan, map[string]any{
			"campaign_id": inserted["campaign_id"],
			"plan_id":     inserted["plan_id"],
			"plan_name":   inserted["plan_name"],
			"update_time": time.Now().Format(time.DateTime),
		})
		insertDataCreative = append(insertDataCreative, map[string]any{
			"campaign_id":   inserted["campaign_id"],
			"plan_id":       inserted["plan_id"],
			"creative_id":   inserted["creative_id_md5"],
			"creative_name": inserted["creative_name"],
			"update_time":   time.Now().Format(time.DateTime),
			"country_id":    0,
			"os":            system.If(inserted["platform_id"].(int) == 2, "ios", "android"),
			"ext":           inserted["creative_id_origin"],
			"game_id":       inserted["game_id"],
			"package_id":    inserted["package_id"],
		})
	}
	if len(insertDataCampaign) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_campaign_conf", insertDataCampaign, true)
	}
	if len(insertDataPlan) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_plan_conf", insertDataPlan, true)
	}
	if len(insertDataCreative) > 0 {
		plusQ.Db("platform").InsertOrUpdateBatch("ad_creative_conf", insertDataCreative, true)
	}
}
