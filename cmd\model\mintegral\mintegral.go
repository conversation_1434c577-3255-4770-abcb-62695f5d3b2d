package mintegral

import "encoding/json"

// MintegralStatusResponse type=1 的响应结构
type MintegralStatusResponse struct {
	Code int                    `json:"code"`
	Msg  string                 `json:"msg"`
	Data MintegralStatusData    `json:"data"`
}

type MintegralStatusData struct {
	Hours      int  `json:"hours"`
	IsComplete bool `json:"is_complete"`
}

// MintegralErrorResponse 错误响应结构
type MintegralErrorResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"`
}

// MintegralReportData 报表数据结构（type=2返回的TSV数据解析后的结构）
type MintegralReportData struct {
	Date                string      `json:"date"`
	Timestamp           json.Number `json:"timestamp,omitempty"`
	OfferID             json.Number `json:"offer_id,omitempty"`
	OfferUuid           string      `json:"offer_uuid,omitempty"`
	OfferName           string      `json:"offer_name,omitempty"`
	CampaignID          json.Number `json:"campaign_id,omitempty"`
	CampaignPackage     string      `json:"campaign_package,omitempty"`
	CreativeID          json.Number `json:"creative_id,omitempty"`
	CreativeName        string      `json:"creative_name,omitempty"`
	AdType              string      `json:"ad_type,omitempty"`
	SubID               string      `json:"sub_id,omitempty"`
	PackageName         string      `json:"package_name,omitempty"`
	Location            string      `json:"location,omitempty"`
	EndcardID           json.Number `json:"endcard_id,omitempty"`
	EndcardName         string      `json:"endcard_name,omitempty"`
	AdOutputType        string      `json:"ad_output_type,omitempty"`
	Currency            string      `json:"currency"`
	Impression          json.Number `json:"impression"`
	Click               json.Number `json:"click"`
	Conversion          json.Number `json:"conversion"`
	Ecpm                json.Number `json:"ecpm"`
	Cpc                 json.Number `json:"cpc"`
	Ctr                 json.Number `json:"ctr"`
	Cvr                 json.Number `json:"cvr"`
	Ivr                 json.Number `json:"ivr"`
	Spend               json.Number `json:"spend"`
}
