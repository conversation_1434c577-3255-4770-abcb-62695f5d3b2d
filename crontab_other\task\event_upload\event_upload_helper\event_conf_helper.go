package event_upload_helper

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"log"
)

type EventConf struct {
	Result map[string]map[string]interface{}
}

var eventConf *EventConf

func EventConfInstant() *EventConf {
	if eventConf == nil {
		eventConf = &EventConf{}
	}
	return eventConf
}

func (e *EventConf) GetInfo(eventName, channel, gameId string) map[string]interface{} {
	vv, err := plusQ.Cache("c0").Get("iaa_third_event_conf_u2")
	if err == nil && vv != nil {
		plusQ.Cache("c0").Delete("iaa_third_event_conf_u2")
		e.Result = nil
		log.Println("========iaa_third_event_conf_u2", vv)
	}

	if e.Result == nil {
		sql := "select * from third_event_conf "
		list, err := plusQ.Db("platform").List(sql)
		if err != nil {
			return nil
		}
		e.Result = make(map[string]map[string]interface{})
		for _, item := range list {
			event_name := fmt.Sprint(item["event"])
			channel_item := fmt.Sprint(item["channel"])
			game_id := fmt.Sprint(item["game_id"])
			e.Result[event_name+channel_item+game_id] = item
		}
	}
	item, ok := e.Result[eventName+channel+gameId]
	if !ok {
		return nil
	}
	return item
}
