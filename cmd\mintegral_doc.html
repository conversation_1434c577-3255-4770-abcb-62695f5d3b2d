
<!DOCTYPE html><html  lang="zh" class="" data-capo=""><head><meta charset="utf-8">
<meta name="viewport" content="initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
<title>广告投放报表_进阶版</title>
<style innerHTML="undefined"></style>
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/entry.CDX5bEm2.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-input.D4G_tsfg.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/HeaderTemplate.BbelA2Z_.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/SearchDropdown.BRIAa1El.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/SearchContent.4PABlAre.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/function-call.BHo1kS7R.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-scrollbar.DgVM_IK3.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-skeleton.D2gX9wof.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-select.CCeajNOs.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-breadcrumb-item.Cdvm65bB.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-loading.x7H6yciF.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/AdvancedSearch.IM3VsdK2.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/language.CrW2l9Po.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-drawer.CtJDP4Mc.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-overlay.D5-YRZ_E.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/index.Ci2U735a.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-form-item.CkjWgKuN.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-tab-pane.cUpTSgnY.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/SearchDocs.DtnN85dE.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/SearchDialog.Busm701K.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/Layout.BkIzcuuh.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/SideTemplate4.CIrazDuh.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/Menu.DpbethYs.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/DocDesc.Ca_qy-Sp.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/NewEditor.DCAFcltA.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/index.DzWYk4sK.css">
<link rel="stylesheet" href="https://cdn-customer.helplook.net/_hl/el-col.BP4dtlli.css">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/cWIk9_W4.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BTdBLJg0.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DSLOoRs8.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/Dat2yjAV.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CGJFNQPE.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/B-K1CqMR.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/C3yjfUb6.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DUcExxLw.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/ByHL9nPh.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/D2nWmbaD.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BW1R7Pul.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BuByyY7I.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BDPdr5sm.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BwZjJ36f.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/ByatyKno.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CSD4FyoT.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BfwMI1FQ.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/ChOdoA5f.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CEA6f5aN.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DD-MDOR_.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BUsUBL1u.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/Djo6Jk3k.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CjMR50xg.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DlAUqK2U.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CPZZOGFw.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/zzy5lXaB.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DjLhxuzV.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DLPGuxro.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DTJmKYtD.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DLQ-3lPS.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BPDGeNuf.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BHfX4HWF.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CfJ_D0iu.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BdaVe4Nh.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/KQfBGYzz.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/B8WtomCn.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/cL22NpGI.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/Bu2CDY4Q.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/p82yq0Qv.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/Dyg-HtUy.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/Cj3v-2sT.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CAr1Ou1C.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BlanlofB.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/prism.BAFA_YTn.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/PA26oeYw.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CbSP8aoh.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/B9j4GAdH.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/PN8GGvLK.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/Cm0DqJbT.js">
<link rel="modulepreload" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/C0ru_SRO.js">
<link rel="prefetch" as="image" type="image/png" href="https://cdn-customer.helplook.net/_hl/404.BY-rDuE5.png">
<link rel="prefetch" as="style" href="https://cdn-customer.helplook.net/_hl/footer.Dm7Q4GRe.css">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CY7U-ft_.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BcOrl1Zv.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/Ba5pf9FM.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/COkkpnj1.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/UVFHBO0g.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/ByMW9C0S.js">
<link rel="prefetch" as="style" href="https://cdn-customer.helplook.net/_hl/hl-template3.x0zNOxR9.css">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DAXA3guv.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/CxjNgPG3.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DEIsYF19.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DG67fLty.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/lgHTzl_5.js">
<link rel="prefetch" as="style" href="https://cdn-customer.helplook.net/_hl/hl-template7.CPCeaWYM.css">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BxZj_Idc.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/BJNwhQGE.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/D6A2K4LD.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/_Ad9HrqD.js">
<link rel="prefetch" as="script" crossorigin href="https://cdn-customer.helplook.net/_hl/DMB70hlS.js">
<link rel="prefetch" as="image" type="image/png" href="https://cdn-customer.helplook.net/_hl/EnterpriseWeChat.CZ9ajUbv.png">
<link rel="prefetch" as="image" type="image/png" href="https://cdn-customer.helplook.net/_hl/dingding.C1OFAKT8.png">
<link rel="prefetch" as="image" type="image/jpeg" href="https://cdn-customer.helplook.net/_hl/forbid.DRgLdMSv.jpg">
<link rel="prefetch" as="image" type="image/jpeg" href="https://cdn-customer.helplook.net/_hl/forbid_en.GDn_CZSM.jpg">
<link rel="prefetch" as="image" type="image/png" href="https://cdn-customer.helplook.net/_hl/load-img.DSGdRIBO.png">
<link rel="prefetch" as="image" type="image/png" href="https://cdn-customer.helplook.net/_hl/blog-cover.DiErobrk.png">
<link rel="prefetch" as="image" type="image/png" href="https://cdn-customer.helplook.net/_hl/blog-cover2.C4pskeKJ.png">
<meta name="baidu-site-verification" content>
<meta property="og:locale" content="zh">
<meta property="og:url" content="https://helpcenter.mintegral.com/cn/docs/advanced-ad-delivery-report">
<meta property="og:site_name" content="Mintegral AppGrowth | 帮助中心">
<link rel="icon" type="image/x-icon" href="https://resource-wangsu.helplook.net/docker_production/qizyk4/icon/icon.png?rand=1313199061">
<link rel="canonical" href="https://helpcenter.mintegral.com/cn/docs/advanced-ad-delivery-report">
<link rel="alternate" href="https://helpcenter.mintegral.com/cn/docs/advanced-ad-delivery-report" hreflang="zh-CN">
<link rel="alternate" hreflang="en-US" href="https://helpcenter.mintegral.com/en/docs/advanced-ad-delivery-report">
<meta name="keywords" content="程序化互动广告平台,移动营销平台,海外广告平台,移动广告平台,Mintegral,广告投放平台DSP,流量变现平台SSP">
<meta name="description" content="需要携带 Token，详情看Token


拉取报表数据时机说明：
数据会在当天过后 1.5 小时左右可以拉取。建议第二天凌晨 1:30 后拉取。举例说明：假设拉取东八区 8 月 1 日的数据，需要在东八区 8 月 2 日凌晨 1:30 后才能拉取。

本章节文档将介绍广告投放报表_进阶版接口。你可以调整 dimension_option 参数进行查询特定的维度。调用该接口需分成两个步骤：

首先需要设置参数 typ">
<meta property="og:title" content="广告投放报表_进阶版">
<meta property="og:description" content="需要携带 Token，详情看Token


拉取报表数据时机说明：
数据会在当天过后 1.5 小时左右可以拉取。建议第二天凌晨 1:30 后拉取。举例说明：假设拉取东八区 8 月 1 日的数据，需要在东八区 8 月 2 日凌晨 1:30 后才能拉取。

本章节文档将介绍广告投放报表_进阶版接口。你可以调整 dimension_option 参数进行查询特定的维度。调用该接口需分成两个步骤：

首先需要设置参数 typ">
<meta property="og:type" content="article">
<meta property="og:image" content>
<meta property="og:image:width" content="900">
<meta property="og:image:height" content="540">
<script type="module" src="https://cdn-customer.helplook.net/_hl/cWIk9_W4.js" crossorigin></script></head><body><!--teleport start anchor--><!--[--><div style="z-index:2001;position:fixed;top:0px;right:0px;bottom:0px;left:0px;display:none;"><!--[--><div role="dialog" aria-modal="true" aria-labelledby="el-id-1024-0" aria-describedby="el-id-1024-1" class="el-overlay-dialog" style=""><!--[--><!--]--></div><!--]--></div><!--]--><!--teleport anchor--><!--teleport start anchor--><!--[--><div style="z-index:2002;position:fixed;top:0px;right:0px;bottom:0px;left:0px;display:none;"><!--[--><div role="dialog" aria-modal="true" aria-label="登录信息" aria-describedby="el-id-1024-2" class="el-overlay-dialog" style=""><!--[--><!--]--></div><!--]--></div><!--]--><!--teleport anchor--><div id="__nuxt"><!--[--><img src="https://resource-wangsu.helplook.net/docker_production/qizyk4/icon/icon.png?rand=1313199061" alt="" class="hidden"><div id="HelpLook" class="route-domain-docs-id siteTemplate-5"><div><div class="hl-custom"><!--[--><div class="header header-border hl-header-custom" id="hl-header" style="--36861470:#ffffff;--c0e55b1c:#363939;" data-v-3ddd25ef><div class="header-box doc-header" data-v-3ddd25ef><div class="flex items-center" data-v-3ddd25ef><a href="/cn/docs/www.mintegral.com" class="hl-logo-a" data-v-3ddd25ef><img src="https://resource-wangsu.helplook.net/docker_production/qizyk4/nav_logo/site_logo?rand=724632855" alt class="logo" data-v-3ddd25ef></a></div><div class="flex items-center h-full" data-v-3ddd25ef><!--[--><!----><div class="flex items-center ml-5" data-v-3ddd25ef><!--[--><span class="i-icon i-icon-search sm:hidden mr-[16px]"><svg width="16" height="16" viewBox="0 0 48 48" fill="none"><path d="M21 38C30.3888 38 38 30.3888 38 21C38 11.6112 30.3888 4 21 4C11.6112 4 4 11.6112 4 21C4 30.3888 11.6112 38 21 38Z" fill="none" stroke="#94a3b8" stroke-width="4" stroke-linejoin="round"></path><path d="M26.657 14.3431C25.2093 12.8954 23.2093 12 21.0001 12C18.791 12 16.791 12.8954 15.3433 14.3431" stroke="#94a3b8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M33.2216 33.2217L41.7069 41.707" stroke="#94a3b8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path></svg></span><div class="help-search-wrap"><span class="i-icon i-icon-search mr-[8px]"><svg width="16" height="16" viewBox="0 0 48 48" fill="none"><path d="M21 38C30.3888 38 38 30.3888 38 21C38 11.6112 30.3888 4 21 4C11.6112 4 4 11.6112 4 21C4 30.3888 11.6112 38 21 38Z" fill="none" stroke="#94a3b8" stroke-width="4" stroke-linejoin="round"></path><path d="M26.657 14.3431C25.2093 12.8954 23.2093 12 21.0001 12C18.791 12 16.791 12.8954 15.3433 14.3431" stroke="#94a3b8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M33.2216 33.2217L41.7069 41.707" stroke="#94a3b8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path></svg></span><span class="text-sm text-slate-400 dark:text-slate-400">请输入</span></div><!--]--><span></span></div><!--]--><!--[--><div class="items-center h-full hidden sm:flex hl-nav-pc" style="--16e14b7c:#363939;--6b575165:#00D1AE;"><!--[--><!--]--></div><span></span><!--]--><!----><div class="el-switch ml-2" style="" data-v-3ddd25ef><input class="el-switch__input" type="checkbox" role="switch" aria-checked="false" aria-disabled="false" name true-value="dark" false-value="light"><!--v-if--><span class="el-switch__core" style="width:;"><!--v-if--><div class="el-switch__action"><!--[--><i class="el-icon" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M512 704a192 192 0 1 0 0-384 192 192 0 0 0 0 384m0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512m0-704a32 32 0 0 1 32 32v64a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32m0 768a32 32 0 0 1 32 32v64a32 32 0 1 1-64 0v-64a32 32 0 0 1 32-32M195.2 195.2a32 32 0 0 1 45.248 0l45.248 45.248a32 32 0 1 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm543.104 543.104a32 32 0 0 1 45.248 0l45.248 45.248a32 32 0 0 1-45.248 45.248l-45.248-45.248a32 32 0 0 1 0-45.248M64 512a32 32 0 0 1 32-32h64a32 32 0 0 1 0 64H96a32 32 0 0 1-32-32m768 0a32 32 0 0 1 32-32h64a32 32 0 1 1 0 64h-64a32 32 0 0 1-32-32M195.2 828.8a32 32 0 0 1 0-45.248l45.248-45.248a32 32 0 0 1 45.248 45.248L240.448 828.8a32 32 0 0 1-45.248 0zm543.104-543.104a32 32 0 0 1 0-45.248l45.248-45.248a32 32 0 0 1 45.248 45.248l-45.248 45.248a32 32 0 0 1-45.248 0"></path></svg><!--]--></i><!--]--></div></span><!--v-if--></div><div class="ml-3" data-v-3ddd25ef><span></span></div><!----><!----></div></div></div><div class="flex justify-between items-center h-10 px-4 cursor-pointer xl:hidden header-border" style="--36861470:#ffffff;--c0e55b1c:#363939;" data-v-3ddd25ef><span class="flex items-center text-xs text-stone-500" data-v-3ddd25ef><span class="i-icon i-icon-align-text-left mr-1" data-v-3ddd25ef><svg width="16" height="16" viewBox="0 0 48 48" fill="none"><path d="M42 9H6" stroke="#78716c" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M34 19H6" stroke="#78716c" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M42 29H6" stroke="#78716c" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M34 39H6" stroke="#78716c" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path></svg></span> 菜单</span><!----></div><!--teleport start--><!--teleport end--><!--teleport start--><!--teleport end--><span style="--36861470:#ffffff;--c0e55b1c:#363939;" data-v-3ddd25ef></span><!--]--><!--[--><div class="overflow-hidden break-words layout-div h-[calc(100vh-106px)] sm:h-[calc(100vh-66px)] max-h-[calc(100vh-66px)]" data-v-fd18dfce><div class="el-scrollbar" data-v-fd18dfce><div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" style=""><div id="hl-docLayoutScroll" class="el-scrollbar__view" style=""><!--[--><!----><div class="mx-auto box-border flex max-w-[1510px]" data-v-fd18dfce><div class="flex-1 flex flex-row-reverse max-w-full office-width xl:max-w-[calc(100%-210px)] 2xl:max-w-[calc(100%-250px)]" data-v-fd18dfce><div class="h-full flex-1 overflow-hidden" data-v-fd18dfce><!--[--><!----><div class="flex-1 pt-6 pb-12 sm:px-8 dark:bg-slate-900 dark:text-slate-400 hl-doc-template-box hl-doc-template5"><div class="el-breadcrumb mt-2 mb-8" aria-label="Breadcrumb" role="navigation"><!--[--><span class="el-breadcrumb__item"><span class="el-breadcrumb__inner is-link" role="link"><!--[-->首页<!--]--></span><i class="el-icon el-breadcrumb__separator" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z"></path></svg><!--]--></i></span><!--[--><span class="el-breadcrumb__item mb-1.5"><span class="el-breadcrumb__inner" role="link"><!--[--><a href="/cn/docs/api-integration" class="">API 对接</a><!--]--></span><i class="el-icon el-breadcrumb__separator" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z"></path></svg><!--]--></i></span><span class="el-breadcrumb__item mb-1.5"><span class="el-breadcrumb__inner" role="link"><!--[--><a href="/cn/docs/report" class="">报表</a><!--]--></span><i class="el-icon el-breadcrumb__separator" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z"></path></svg><!--]--></i></span><span class="el-breadcrumb__item mb-1.5"><span class="el-breadcrumb__inner" role="link"><!--[--><a aria-current="page" href="/cn/docs/advanced-ad-delivery-report" class="router-link-active router-link-exact-active">广告投放报表_进阶版</a><!--]--></span><i class="el-icon el-breadcrumb__separator" style=""><!--[--><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z"></path></svg><!--]--></i></span><!--]--><!--]--></div><!----><div id="hl-doc"><div class="flex flex-col-reverse items-start break-words md:block"><!----><div><h1 class="docs-title">广告投放报表_进阶版</h1><!----><!----></div><div class="clear-both"></div></div><div class=""><!----><div><div id="preview-only" class="md-editor dark:bg-slate-900 dark:text-slate-400 md-editor-previewOnly" style=""><!--[--><div id="preview-only-preview-wrapper" class="md-editor-preview-wrapper"><div id="preview-only-preview" class="md-editor-preview default-theme md-editor-scrn"><blockquote data-line="0">
<p data-line="0">需要携带 Token，详情看<a href="https://mintegral.helplook.com/docs/token">Token</a></p>
</blockquote>
<blockquote data-line="2">
<p data-line="2">拉取报表数据时机说明：<br>
数据会在当天过后 1.5 小时左右可以拉取。建议第二天凌晨 1:30 后拉取。举例说明：假设拉取东八区 8 月 1 日的数据，需要在东八区 8 月 2 日凌晨 1:30 后才能拉取。</p>
</blockquote>
<p data-line="5">本章节文档将介绍广告投放报表_进阶版接口。你可以调整 dimension_option 参数进行查询特定的维度。调用该接口需分成两个步骤：</p>
<ol data-line="7">
<li data-line="7">首先需要设置参数 type=1 并调用接口，系统会在服务端异步生成好数据。</li>
</ol>
<blockquote data-line="9">
<p data-line="9">1.1 请求后需要等待数据生成，可继续用 type = 1 发起相同请求（Token需要更新）获取数据生成信息。<br/><br>
1.2 当接口返回码 code=200，说明数据已经成功生成。<br/><br>
1.3 如果拉取的是当天的数据，数据可能不完整，数据更新为小时维度，可等待n小时直至数据准备，再重新发起 type=1 请求更新以及获取新的数据生成信息，从而判断是否使用 type=2 更新数据。<br /><br>
1.4 数据的生成信息参考下文 type=1 的响应结果。</p>
</blockquote>
<ol start="2" data-line="14">
<li data-line="14">数据生成后，设置参数 type=2 再次调用接口即可直接下载数据。</li>
</ol>
<blockquote data-line="16">
<p data-line="16">2.1 若数据还没生成完，调用 type=2 接口会返回非 200 的 code。<br/><br>
2.2 若数据已经生成，使用 type=2 接口会直接返回文件字节流（Content-Type: application/octet-stream）。<br/><br>
2.3 数据按&quot;\t&quot;分列，按&quot;\n&quot;分行。<br/><br>
2.4 返回的数据每次返回的是当前请求的所有数据，并不是增量数据。</p>
</blockquote>
<h3 data-line="21" id="请求地址">请求地址</h3>
<p data-line="23"><a href="https://ss-api.mintegral.com/api/v2/reports/data">https://ss-api.mintegral.com/api/v2/reports/data</a></p>
<h3 data-line="25" id="请求方法">请求方法</h3>
<p data-line="27">GET</p>
<h3 data-line="29" id="请求示例">请求示例</h3>

        <div  data-line="31" class="md-editor-code">
          <div class="md-editor-code-head">
            <div class="md-editor-code-flag"><span></span><span></span><span></span></div>
            <div class="md-editor-code-action">
              <span class="md-editor-code-lang">json</span>
              <span class="md-editor-copy-button" data-tips="复制代码">复制代码</span>
              
              
            </div>
          </div>
          <pre><code class="language-json" language=json><span class="md-editor-code-block">GET /api/v2/reports/data?start_time=2024-06-01&amp;end_time=2024-06-01&amp;type=1&amp;dimension_option=Offer
HTTP/1.1 Host: ss-api.mintegral.com</span><span rn-wrapper aria-hidden="true"><span></span><span></span></span></code></pre>

        </div>
      <h3 data-line="36" id="请求参数">请求参数</h3>
<table data-line="38">
<thead data-line="38">
<tr data-line="38">
<th>字段</th>
<th>类型</th>
<th>说明</th>
<th>默认值</th>
<th>例子</th>
</tr>
</thead>
<tbody data-line="40">
<tr data-line="40">
<td>timezone <code class="fr">选填</code></td>
<td>string</td>
<td>数据对应的时区</td>
<td><code>&quot;+8&quot;</code></td>
<td><code>&quot;+8&quot;</code></td>
</tr>
<tr data-line="41">
<td>start_time</td>
<td>string</td>
<td>请求数据的开始时间, 格式为 <code>YYYY-mm-dd</code>。只支持查询最近半年的数据。</td>
<td>—</td>
<td><code>&quot;2020-02-01&quot;</code></td>
</tr>
<tr data-line="42">
<td>end_time</td>
<td>string</td>
<td>请求数据的结束时间, 格式为 <code>YYYY-mm-dd</code>。结束时间和开始时间的时间跨度不超过 7 天。</td>
<td>—</td>
<td><code>&quot;2020-02-03&quot;</code></td>
</tr>
<tr data-line="43">
<td>dimension_option</td>
<td>string</td>
<td>可选的字段名： <code>&quot;Offer&quot;</code>, <code>&quot;Campaign&quot;</code>, <code>&quot;CampaignPackage&quot;</code>, <code>&quot;Creative&quot;</code>, <code>&quot;AdType&quot;</code>, <code>&quot;Sub&quot;</code>, <code>&quot;Package&quot;</code>, <code>&quot;Location&quot;</code>, <code>&quot;Endcard&quot;</code>, <code>&quot;AdOutputType&quot;</code>. 多个字段用,分隔.<br/>dimension_option=&gt;<code>&quot;Offer&quot;</code>，按 Offer ID, Offer Name, UUID 细分数据;<br/>dimension_option=&gt;<code>&quot;Campaign&quot;</code>, 按 Campaign ID 细分数据;<br/>dimension_option=&gt;<code>&quot;CampaignPackage&quot;</code>, 按 Campaign 的 Package Name 细分数据;<br/>dimension_option=&gt;<code>&quot;Creative&quot;</code>, 按 Creative ID, Creative Name 细分数据;<br/>dimension_option=&gt;<code>&quot;AdType&quot;</code>, 按 Ad Type 细分数据;<br/>dimension_option=&gt;<code>&quot;Sub&quot;</code>, 按 Sub ID（mtgid，App ID） 细分数据;<br/>dimension_option=&gt;<code>&quot;Package&quot;</code>, 按 Sub 的 Package Name 细分数据;<br/>dimension_option=&gt;<code>&quot;Location&quot;</code>，按 国家/地区 细分数据;<br/>dimension_option=&gt;<code>&quot;Endcard&quot;</code>，按 Endcard ID, Endcard Name 细分数据;<br/>dimension_option=&gt;<code>&quot;AdOutputType&quot;</code>，按 Ad Output Type 细分数据;<br/>不支持请求的组合里同时具有下列枚举值组合：<br/>Creative &amp; Sub<br/>Creative &amp; Package<br/>Creative &amp; time_granularity = hourly<br/>Endcard &amp; Sub<br/>Endcard &amp; Package<br/>Endcard &amp; time_granularity = hourly</td>
<td>-</td>
<td><code>&quot;Offer,Location&quot;</code></td>
</tr>
<tr data-line="44">
<td>time_granularity <code class="fr">选填</code></td>
<td>string</td>
<td>按 小时/天 细分数据. 枚举值: <code>&quot;hourly&quot;</code>, <code>&quot;daily&quot;</code>.</td>
<td><code>&quot;daily&quot;</code></td>
<td><code>&quot;hourly&quot;</code></td>
</tr>
<tr data-line="45">
<td>type <code class="fr">选填</code></td>
<td>int</td>
<td>type =&gt; 1， 拉取数据请求获取当前请求条件的数据状态。<br />type =&gt; 2， 下载数据。</td>
<td><code>1</code></td>
<td><code>1</code></td>
</tr>
</tbody>
</table>
<h3 data-line="47" id="type=2 返回的文件表头（字段）">type=2 返回的文件表头（字段）</h3>
<table data-line="49">
<thead data-line="49">
<tr data-line="49">
<th>表头（字段）</th>
<th>类型</th>
<th>说明</th>
<th>例子</th>
</tr>
</thead>
<tbody data-line="51">
<tr data-line="51">
<td>Date</td>
<td>int</td>
<td>日期</td>
<td><code>20220418</code></td>
</tr>
<tr data-line="52">
<td>Timestamp</td>
<td>int</td>
<td>时间戳  <br/>当 time_granularity = <code>&quot;hourly&quot;</code> 时返回</td>
<td><code>1650270348</code></td>
</tr>
<tr data-line="53">
<td>Offer Id</td>
<td>int</td>
<td>广告单元 ID  <br/>当 dimension_option 包含 <code>&quot;Offer&quot;</code> 时返回</td>
<td><code>73332</code></td>
</tr>
<tr data-line="54">
<td>Offer Uuid</td>
<td>string</td>
<td>系统自动生成的广告单元名称 <br/>当 dimension_option 包含 <code>&quot;Offer&quot;</code> 时返回</td>
<td><code>ss_xxxx_US_AND_xxx_220112_MTG</code></td>
</tr>
<tr data-line="55">
<td>Offer Name</td>
<td>string</td>
<td>广告单元名称 <br/>当 dimension_option 包含 <code>&quot;Offer&quot;</code> 时返回</td>
<td><code>xxxx_US_AND_xxx_220112_MTG</code></td>
</tr>
<tr data-line="56">
<td>Campaign Id</td>
<td>int</td>
<td>广告 ID  <br/>当 dimension_option 包含 <code>&quot;Campaign&quot;</code> 时返回</td>
<td><code>1111</code></td>
</tr>
<tr data-line="57">
<td>Campaign Package</td>
<td>string</td>
<td>广告包名    <br/>当 dimension_option 包含 <code>&quot;CampaignPackage&quot;</code> 时返回</td>
<td><code>com.xxx.yyy</code></td>
</tr>
<tr data-line="58">
<td>Creative Id</td>
<td>bigint</td>
<td>素材 ID      <br/>当 dimension_option 包含 <code>&quot;Creative&quot;</code> 时返回</td>
<td><code>2222</code></td>
</tr>
<tr data-line="59">
<td>Creative Name</td>
<td>string</td>
<td>素材名称  <br/>当 dimension_option 包含 <code>&quot;Creative&quot;</code> 时返回</td>
<td><code>220301-xxx-US-MTG01.png</code></td>
</tr>
<tr data-line="60">
<td>Ad Type</td>
<td>string</td>
<td>广告类型   <br/>当 dimension_option 包含 <code>&quot;AdType&quot;</code> 时返回</td>
<td><code>banner</code></td>
</tr>
<tr data-line="61">
<td>Sub Id</td>
<td>string</td>
<td>应用 ID (mtgid，App ID)  <br/>当 dimension_option 包含 <code>&quot;Sub&quot;</code> 时返回</td>
<td><code>mtg123456</code></td>
</tr>
<tr data-line="62">
<td>Package Name</td>
<td>string</td>
<td>应用包名   <br/>当 dimension_option 包含 <code>&quot;Package&quot;</code> 时返回</td>
<td><code>com.aaa.bbb</code></td>
</tr>
<tr data-line="63">
<td>Location</td>
<td>string</td>
<td>推广投放的国家/地区    <br/>当 dimension_option 包含 <code>&quot;Location&quot;</code> 时返回</td>
<td><code>US</code></td>
</tr>
<tr data-line="64">
<td>Endcard ID</td>
<td>bigint</td>
<td>结束卡片 ID     <br/>当 dimension_option 包含 <code>&quot;Endcard&quot;</code> 时返回</td>
<td><code>3333</code></td>
</tr>
<tr data-line="65">
<td>Endcard Name</td>
<td>string</td>
<td>结束卡片名称  <br/>当 dimension_option 包含 <code>&quot;Endcard</code>&quot; 时返回</td>
<td><code>EC_PL_XXXX_X</code></td>
</tr>
<tr data-line="66">
<td>Ad Output Type</td>
<td>string</td>
<td>广告呈现类型   <br/>当 dimension_option 包含 <code>&quot;AdOutputType&quot;</code> 时返回 <br/> <code>&quot;standard&quot;</code>:标准创意，<code>&quot;dynamic&quot;</code>:应用动态创意,<code>&quot;playable&quot;</code>:应用试玩创意</td>
<td><code>standard</code></td>
</tr>
<tr data-line="67">
<td>Currency</td>
<td>string</td>
<td>货币类型 USD/CNY</td>
<td><code>USD</code></td>
</tr>
<tr data-line="68">
<td>Impression</td>
<td>bigint</td>
<td>展示</td>
<td><code>7777</code></td>
</tr>
<tr data-line="69">
<td>Click</td>
<td>bigint</td>
<td>点击</td>
<td><code>88888</code></td>
</tr>
<tr data-line="70">
<td>Conversion</td>
<td>bigint</td>
<td>转化</td>
<td><code>9999</code></td>
</tr>
<tr data-line="71">
<td>Ecpm</td>
<td>Double</td>
<td>eCPM</td>
<td><code>11.11</code></td>
</tr>
<tr data-line="72">
<td>Cpc</td>
<td>Double</td>
<td>CPC</td>
<td><code>0.03</code></td>
</tr>
<tr data-line="73">
<td>Ctr</td>
<td>Double</td>
<td>CTR</td>
<td><code>0.3</code></td>
</tr>
<tr data-line="74">
<td>Cvr</td>
<td>Double</td>
<td>CVR</td>
<td><code>0.1</code></td>
</tr>
<tr data-line="75">
<td>Ivr</td>
<td>Double</td>
<td>IVR</td>
<td><code>0.05</code></td>
</tr>
<tr data-line="76">
<td>Spend</td>
<td>Double</td>
<td>花费</td>
<td><code>8888.8</code></td>
</tr>
</tbody>
</table>
<h3 data-line="80" id="type=1 的响应结果">type=1 的响应结果</h3>
<table data-line="82">
<thead data-line="82">
<tr data-line="82">
<th>字段</th>
<th>类型</th>
<th>说明</th>
</tr>
</thead>
<tbody data-line="84">
<tr data-line="84">
<td>code</td>
<td>int</td>
<td><code>200</code> =&gt; 生成数据完成，可使用 type=2 获取数据。<br /><code>201</code> =&gt; 接收请求成功，等待生成数据。<br /><code>202</code> =&gt; 数据正在生成中。<br /> <code>10000</code> =&gt; 参数错误或权限缺失。</td>
</tr>
<tr data-line="85">
<td>msg</td>
<td>string</td>
<td>成功，返回相应的成功信息，失败返回相应的错误信息</td>
</tr>
<tr data-line="86">
<td>data</td>
<td>object</td>
<td>成功，返回数据生成信息，失败返回具体的错误信息</td>
</tr>
<tr data-line="87">
<td>hours</td>
<td>int</td>
<td>此刻数据包含的小时数，如 2024-06-01 12:00 请求了 start_time = end_time = '2024-06-01'， 可能会返回 hours=12， 因为此刻数据包含了0～11点的数据，有12个小时。</td>
</tr>
<tr data-line="88">
<td>is_complete</td>
<td>boolean</td>
<td><code>TRUE</code> =&gt; 数据完整, <code>FALSE</code> =&gt; 数据不完整，如 end_time 大于等于当前日期则数据可能会不完整</td>
</tr>
</tbody>
</table>
<h3 data-line="90" id="type=2 的响应结果（code 不是 200）">type=2 的响应结果（code 不是 200）</h3>
<table data-line="92">
<thead data-line="92">
<tr data-line="92">
<th>字段</th>
<th>类型</th>
<th>说明</th>
</tr>
</thead>
<tbody data-line="94">
<tr data-line="94">
<td>code</td>
<td>int</td>
<td><code>203</code> =&gt; 没有接收到相同条件请求，请先使用 type=1 发起请求生成数据。<br /><code>204</code> =&gt; 数据还没生成，请等待生成数据。<br /><code>205</code> =&gt; 数据已经过期（生成的数据保留 1 个月），正在重新生成中。<br /> <code>10000</code> =&gt; 参数错误或权限缺失。</td>
</tr>
<tr data-line="95">
<td>msg</td>
<td>string</td>
<td>失败返回相应的错误信息</td>
</tr>
<tr data-line="96">
<td>data</td>
<td>json</td>
<td>失败返回具体的错误信息</td>
</tr>
</tbody>
</table>
<h3 data-line="98" id="应答示例">应答示例</h3>

        <div  data-line="100" class="md-editor-code">
          <div class="md-editor-code-head">
            <div class="md-editor-code-flag"><span></span><span></span><span></span></div>
            <div class="md-editor-code-action">
              <span class="md-editor-code-lang">json</span>
              <span class="md-editor-copy-button" data-tips="复制代码">复制代码</span>
              
              
            </div>
          </div>
          <pre><code class="language-json" language=json><span class="md-editor-code-block">{
  &quot;code&quot;: 200,
  &quot;msg&quot;: &quot;Generate success, please use type = 2 to get data&quot;,
  &quot;data&quot;: {
    &quot;hours&quot;: 24,
    &quot;is_complete&quot;: true
  }
}</span><span rn-wrapper aria-hidden="true"><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span></span></code></pre>

        </div>
      </div></div>
<script>window.__NUXT__={};window.__NUXT__.config={public:{VITE_MODE_NAME:"production",VITE_API_URL:"https://api.helplook.net",VITE_API_GETURL:"https://api-get.helplook.net",VITE_SERVER_API_URL:"https://api.helplook.net",VITE_CDN_URL:"https://resource-wangsu.helplook.net",VITE_CODE_CDN_URL:"https://cdn-customer.helplook.net",VITE_SDK_URL:"https://sdk.helplook.net/pro",i18n:{baseUrl:"",defaultLocale:"",defaultDirection:"ltr",strategy:"prefix_except_default",lazy:false,rootRedirect:"",routesNameSeparator:"___",defaultLocaleRouteNameSuffix:"default",skipSettingLocaleOnNavigate:false,differentDomains:false,trailingSlash:false,configLocales:[],locales:{},detectBrowserLanguage:{alwaysRedirect:false,cookieCrossOrigin:false,cookieDomain:"",cookieKey:"i18n_redirected",cookieSecure:false,fallbackLocale:"",redirectOn:"root",useCookie:true},experimental:{localeDetector:"",switchLocalePathLinkSSR:false,autoImportTranslationFunctions:false},multiDomainLocales:false}},app:{baseURL:"/",buildId:"921e53a8-7810-4125-8d92-cb376850b0df",buildAssetsDir:"/_hl/",cdnURL:"https://cdn-customer.helplook.net"}}</script></body></html>