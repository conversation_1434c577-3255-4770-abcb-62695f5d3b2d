package ad_report_user

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_report_user")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// AdUnitId 广告单元 ID
	AdUnitId = property.Property("ad_unit_id")
	// AdUnitName 广告单元名称
	AdUnitName = property.Property("ad_unit_name")
	// Waterfall MAX 广告单元瀑布流名称（仅限非聚合广告单元）
	Waterfall = property.Property("waterfall")
	// Placement 用户产生印象和收入的广告展示位置名称
	Placement = property.Property("placement")
	// AdPlacement 展示广告的外部广告网络的展示位置 ID
	AdPlacement = property.Property("ad_placement")
	// Idfa 广告标识符：iOS 设备为 IDFA，Android 设备为 GAID
	Idfa = property.Property("idfa")
	// Idfv 供应商的标识符
	Idfv = property.Property("idfv")
	// Revenue 用户产生的收入，以美元为单位
	Revenue = property.Property("revenue")
	// UserId 可选的用户 ID
	UserId = property.Property("user_id")
	// Date 印象时间
	Date = property.Property("date")
	// Utime 更新时间
	Utime = property.Property("utime")
	// Country 展示的两字母国家/地区代码
	Country = property.Property("country")
	// Network 产生展示的广告网络
	Network = property.Property("network")
	// CustomData 从集成传入的自定义数据
	CustomData = property.Property("custom_data")
	// DeviceType 用户的设备类型：phone、tablet、other
	DeviceType = property.Property("device_type")
	// AdFormat 广告单元广告类型：INTER、BANNER、REWARD
	AdFormat = property.Property("ad_format")
	// PackageId 包号
	PackageId = property.Property("package_id")
	// Platform 应用平台android、ios
	Platform = property.Property("platform")
)

var FieldsAll = Fields(Id, AdUnitId, AdUnitName, Waterfall, Placement, AdPlacement, Idfa, Idfv, Revenue, UserId, Date, Utime, Country, Network, CustomData, DeviceType, AdFormat, PackageId, Platform)
var NonePrimaryFields = Fields(AdUnitId, AdUnitName, Waterfall, Placement, AdPlacement, Idfa, Idfv, Revenue, UserId, Date, Utime, Country, Network, CustomData, DeviceType, AdFormat, PackageId, Platform)
var NoneAutoIncrementFields = Fields(AdUnitId, AdUnitName, Waterfall, Placement, AdPlacement, Idfa, Idfv, Revenue, UserId, Date, Utime, Country, Network, CustomData, DeviceType, AdFormat, PackageId, Platform)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdReportUserModel 用户层级广告收入表
type AdReportUserModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// AdUnitId 广告单元 ID
	AdUnitId *string `orm:"ad_unit_id" json:"ad_unit_id"`

	// AdUnitName 广告单元名称
	AdUnitName *string `orm:"ad_unit_name" json:"ad_unit_name"`

	// Waterfall MAX 广告单元瀑布流名称（仅限非聚合广告单元）
	Waterfall *string `orm:"waterfall" json:"waterfall"`

	// Placement 用户产生印象和收入的广告展示位置名称
	Placement *string `orm:"placement" json:"placement"`

	// AdPlacement 展示广告的外部广告网络的展示位置 ID
	AdPlacement *string `orm:"ad_placement" json:"ad_placement"`

	// Idfa 广告标识符：iOS 设备为 IDFA，Android 设备为 GAID
	Idfa *string `orm:"idfa" json:"idfa"`

	// Idfv 供应商的标识符
	Idfv *string `orm:"idfv" json:"idfv"`

	// Revenue 用户产生的收入，以美元为单位
	Revenue *float32 `orm:"revenue" json:"revenue"`

	// UserId 可选的用户 ID
	UserId *string `orm:"user_id" json:"user_id"`

	// Date 印象时间
	Date CustomTime `orm:"date" json:"date"`

	// Utime 更新时间
	Utime *CustomTime `orm:"utime" json:"utime"`

	// Country 展示的两字母国家/地区代码
	Country *string `orm:"country" json:"country"`

	// Network 产生展示的广告网络
	Network *string `orm:"network" json:"network"`

	// CustomData 从集成传入的自定义数据
	CustomData *string `orm:"custom_data" json:"custom_data"`

	// DeviceType 用户的设备类型：phone、tablet、other
	DeviceType *string `orm:"device_type" json:"device_type"`

	// AdFormat 广告单元广告类型：INTER、BANNER、REWARD
	AdFormat *string `orm:"ad_format" json:"ad_format"`

	// PackageId 包号
	PackageId string `orm:"package_id" json:"package_id"`

	// Platform 应用平台android、ios
	Platform string `orm:"platform" json:"platform"`
}

type PagedResult struct {
	Records      []*AdReportUserModel `json:"list"`
	PageNum      int                  `json:"page"`
	PageSize     int                  `json:"page_size"`
	TotalPages   int                  `json:"total_pages"`
	TotalRecords int                  `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:          1,
	AdUnitId:    2,
	AdUnitName:  3,
	Waterfall:   4,
	Placement:   5,
	AdPlacement: 6,
	Idfa:        7,
	Idfv:        8,
	Revenue:     9,
	UserId:      10,
	Date:        11,
	Utime:       12,
	Country:     13,
	Network:     14,
	CustomData:  15,
	DeviceType:  16,
	AdFormat:    17,
	PackageId:   18,
	Platform:    19,
}

func (m *AdReportUserModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdReportUserModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.AdUnitId, m.AdUnitName, m.Waterfall, m.Placement, m.AdPlacement, m.Idfa, m.Idfv, m.Revenue, m.UserId, m.Date, m.Utime, m.Country, m.Network, m.CustomData, m.DeviceType, m.AdFormat, m.PackageId, m.Platform)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdReportUserModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AdUnitId, m.AdUnitName, m.Waterfall, m.Placement, m.AdPlacement, m.Idfa, m.Idfv, m.Revenue, m.UserId, m.Date, m.Utime, m.Country, m.Network, m.CustomData, m.DeviceType, m.AdFormat, m.PackageId, m.Platform)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdReportUserModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdReportUserModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AdUnitId, m.AdUnitName, m.Waterfall, m.Placement, m.AdPlacement, m.Idfa, m.Idfv, m.Revenue, m.UserId, m.Date, m.Utime, m.Country, m.Network, m.CustomData, m.DeviceType, m.AdFormat, m.PackageId, m.Platform)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportUserModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdReportUserModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AdUnitId, m.AdUnitName, m.Waterfall, m.Placement, m.AdPlacement, m.Idfa, m.Idfv, m.Revenue, m.UserId, m.Date, m.Utime, m.Country, m.Network, m.CustomData, m.DeviceType, m.AdFormat, m.PackageId, m.Platform), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportUserModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdReportUserModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.AdUnitId, m.AdUnitName, m.Waterfall, m.Placement, m.AdPlacement, m.Idfa, m.Idfv, m.Revenue, m.UserId, m.Date, m.Utime, m.Country, m.Network, m.CustomData, m.DeviceType, m.AdFormat, m.PackageId, m.Platform), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AdUnitId, m.AdUnitName, m.Waterfall, m.Placement, m.AdPlacement, m.Idfa, m.Idfv, m.Revenue, m.UserId, m.Date, m.Utime, m.Country, m.Network, m.CustomData, m.DeviceType, m.AdFormat, m.PackageId, m.Platform))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportUserModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdReportUserModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdReportUserModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdReportUserModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdReportUserModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdReportUserModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdReportUserModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdReportUserModel, error) {

	modelList := make([]*AdReportUserModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdReportUserModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Waterfall, &m.Placement, &m.AdPlacement, &m.Idfa, &m.Idfv, &m.Revenue, &m.UserId, &m.Date, &m.Utime, &m.Country, &m.Network, &m.CustomData, &m.DeviceType, &m.AdFormat, &m.PackageId, &m.Platform)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdReportUserModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdReportUserModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdReportUserModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdReportUserModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdReportUserModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdReportUserModel, error) {

	m := &AdReportUserModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Waterfall, &m.Placement, &m.AdPlacement, &m.Idfa, &m.Idfv, &m.Revenue, &m.UserId, &m.Date, &m.Utime, &m.Country, &m.Network, &m.CustomData, &m.DeviceType, &m.AdFormat, &m.PackageId, &m.Platform)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdReportUserModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdReportUserModel, error) {

	modelList := make([]*AdReportUserModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdReportUserModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Waterfall, &m.Placement, &m.AdPlacement, &m.Idfa, &m.Idfv, &m.Revenue, &m.UserId, &m.Date, &m.Utime, &m.Country, &m.Network, &m.CustomData, &m.DeviceType, &m.AdFormat, &m.PackageId, &m.Platform)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdReportUserModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdReportUserModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdReportUserModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdUnitId, &m.AdUnitName, &m.Waterfall, &m.Placement, &m.AdPlacement, &m.Idfa, &m.Idfv, &m.Revenue, &m.UserId, &m.Date, &m.Utime, &m.Country, &m.Network, &m.CustomData, &m.DeviceType, &m.AdFormat, &m.PackageId, &m.Platform)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdReportUserModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdReportUserModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdReportUserModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdReportUserModel, error) {

	m := &AdReportUserModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdReportUserModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdReportUserModel, error) {

	modelList := make([]*AdReportUserModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdReportUserModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdReportUserModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdReportUserModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdReportUserModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdReportUserModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.AdUnitId, d.AdUnitName, d.Waterfall, d.Placement, d.AdPlacement, d.Idfa, d.Idfv, d.Revenue, d.UserId, d.Date, d.Utime, d.Country, d.Network, d.CustomData, d.DeviceType, d.AdFormat, d.PackageId, d.Platform))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "ad_unit_id",
			"title":     "广告单元 ID",
		},
		{
			"dataIndex": "ad_unit_name",
			"title":     "广告单元名称",
		},
		{
			"dataIndex": "waterfall",
			"title":     "MAX 广告单元瀑布流名称（仅限非聚合广告单元）",
		},
		{
			"dataIndex": "placement",
			"title":     "用户产生印象和收入的广告展示位置名称",
		},
		{
			"dataIndex": "ad_placement",
			"title":     "展示广告的外部广告网络的展示位置 ID",
		},
		{
			"dataIndex": "idfa",
			"title":     "广告标识符：iOS 设备为 IDFA，Android 设备为 GAID",
		},
		{
			"dataIndex": "idfv",
			"title":     "供应商的标识符",
		},
		{
			"dataIndex": "revenue",
			"title":     "用户产生的收入，以美元为单位",
		},
		{
			"dataIndex": "user_id",
			"title":     "可选的用户 ID",
		},
		{
			"dataIndex": "date",
			"title":     "印象时间",
		},
		{
			"dataIndex": "utime",
			"title":     "更新时间",
		},
		{
			"dataIndex": "country",
			"title":     "展示的两字母国家/地区代码",
		},
		{
			"dataIndex": "network",
			"title":     "产生展示的广告网络",
		},
		{
			"dataIndex": "custom_data",
			"title":     "从集成传入的自定义数据",
		},
		{
			"dataIndex": "device_type",
			"title":     "用户的设备类型：phone、tablet、other",
		},
		{
			"dataIndex": "ad_format",
			"title":     "广告单元广告类型：INTER、BANNER、REWARD",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号",
		},
		{
			"dataIndex": "platform",
			"title":     "应用平台android、ios",
		}}
}
