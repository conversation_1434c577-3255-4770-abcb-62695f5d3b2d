package ad_attribute

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_attribute")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// Md5Key 属性唯一标识
	Md5Key = property.Property("md5_key")
	// Size 广告宽高
	Size = property.Property("size")
	// AdUnitId 广告单元id
	AdUnitId = property.Property("ad_unit_id")
	// NetworkName 广告网络名称
	NetworkName = property.Property("network_name")
	// NetworkPlacement 第三方网络广告的placement 名称
	NetworkPlacement = property.Property("network_placement")
	// Placement 自定义的placement名称
	Placement = property.Property("placement")
	// Waterfall 触发该广告的瀑布流名称
	Waterfall = property.Property("waterfall")
	// RequestLatencyMillis 广告请求花费的时间
	RequestLatencyMillis = property.Property("requestLatency_millis")
	// CreativeId 广告素材id
	CreativeId = property.Property("creative_id")
	// Revenue 该次广告产生的收益（美元）
	Revenue = property.Property("revenue")
	// DspName dsp 名称
	DspName = property.Property("dsp_name")
	// DspId dsp的id
	DspId = property.Property("dsp_id")
)

var FieldsAll = Fields(Id, Md5Key, Size, AdUnitId, NetworkName, NetworkPlacement, Placement, Waterfall, RequestLatencyMillis, CreativeId, Revenue, DspName, DspId)
var NonePrimaryFields = Fields(Md5Key, Size, AdUnitId, NetworkName, NetworkPlacement, Placement, Waterfall, RequestLatencyMillis, CreativeId, Revenue, DspName, DspId)
var NoneAutoIncrementFields = Fields(Md5Key, Size, AdUnitId, NetworkName, NetworkPlacement, Placement, Waterfall, RequestLatencyMillis, CreativeId, Revenue, DspName, DspId)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdAttributeModel 广告属性表
type AdAttributeModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// Md5Key 属性唯一标识
	Md5Key string `orm:"md5_key" json:"md5_key"`

	// Size 广告宽高
	Size string `orm:"size" json:"size"`

	// AdUnitId 广告单元id
	AdUnitId string `orm:"ad_unit_id" json:"ad_unit_id"`

	// NetworkName 广告网络名称
	NetworkName string `orm:"network_name" json:"network_name"`

	// NetworkPlacement 第三方网络广告的placement 名称
	NetworkPlacement string `orm:"network_placement" json:"network_placement"`

	// Placement 自定义的placement名称
	Placement string `orm:"placement" json:"placement"`

	// Waterfall 触发该广告的瀑布流名称
	Waterfall string `orm:"waterfall" json:"waterfall"`

	// RequestLatencyMillis 广告请求花费的时间
	RequestLatencyMillis *string `orm:"requestLatency_millis" json:"requestLatency_millis"`

	// CreativeId 广告素材id
	CreativeId *string `orm:"creative_id" json:"creative_id"`

	// Revenue 该次广告产生的收益（美元）
	Revenue *float32 `orm:"revenue" json:"revenue"`

	// DspName dsp 名称
	DspName *string `orm:"dsp_name" json:"dsp_name"`

	// DspId dsp的id
	DspId *string `orm:"dsp_id" json:"dsp_id"`
}

type PagedResult struct {
	Records      []*AdAttributeModel `json:"list"`
	PageNum      int                 `json:"page"`
	PageSize     int                 `json:"page_size"`
	TotalPages   int                 `json:"total_pages"`
	TotalRecords int                 `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:                   1,
	Md5Key:               2,
	Size:                 3,
	AdUnitId:             4,
	NetworkName:          5,
	NetworkPlacement:     6,
	Placement:            7,
	Waterfall:            8,
	RequestLatencyMillis: 9,
	CreativeId:           10,
	Revenue:              11,
	DspName:              12,
	DspId:                13,
}

func (m *AdAttributeModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdAttributeModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.Md5Key, m.Size, m.AdUnitId, m.NetworkName, m.NetworkPlacement, m.Placement, m.Waterfall, m.RequestLatencyMillis, m.CreativeId, m.Revenue, m.DspName, m.DspId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdAttributeModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Md5Key, m.Size, m.AdUnitId, m.NetworkName, m.NetworkPlacement, m.Placement, m.Waterfall, m.RequestLatencyMillis, m.CreativeId, m.Revenue, m.DspName, m.DspId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdAttributeModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdAttributeModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Md5Key, m.Size, m.AdUnitId, m.NetworkName, m.NetworkPlacement, m.Placement, m.Waterfall, m.RequestLatencyMillis, m.CreativeId, m.Revenue, m.DspName, m.DspId)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdAttributeModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdAttributeModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Md5Key, m.Size, m.AdUnitId, m.NetworkName, m.NetworkPlacement, m.Placement, m.Waterfall, m.RequestLatencyMillis, m.CreativeId, m.Revenue, m.DspName, m.DspId), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdAttributeModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdAttributeModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.Md5Key, m.Size, m.AdUnitId, m.NetworkName, m.NetworkPlacement, m.Placement, m.Waterfall, m.RequestLatencyMillis, m.CreativeId, m.Revenue, m.DspName, m.DspId), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Md5Key, m.Size, m.AdUnitId, m.NetworkName, m.NetworkPlacement, m.Placement, m.Waterfall, m.RequestLatencyMillis, m.CreativeId, m.Revenue, m.DspName, m.DspId))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdAttributeModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdAttributeModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdAttributeModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdAttributeModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdAttributeModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdAttributeModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdAttributeModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdAttributeModel, error) {

	modelList := make([]*AdAttributeModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdAttributeModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Md5Key, &m.Size, &m.AdUnitId, &m.NetworkName, &m.NetworkPlacement, &m.Placement, &m.Waterfall, &m.RequestLatencyMillis, &m.CreativeId, &m.Revenue, &m.DspName, &m.DspId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdAttributeModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdAttributeModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdAttributeModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdAttributeModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdAttributeModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdAttributeModel, error) {

	m := &AdAttributeModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.Md5Key, &m.Size, &m.AdUnitId, &m.NetworkName, &m.NetworkPlacement, &m.Placement, &m.Waterfall, &m.RequestLatencyMillis, &m.CreativeId, &m.Revenue, &m.DspName, &m.DspId)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdAttributeModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdAttributeModel, error) {

	modelList := make([]*AdAttributeModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdAttributeModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Md5Key, &m.Size, &m.AdUnitId, &m.NetworkName, &m.NetworkPlacement, &m.Placement, &m.Waterfall, &m.RequestLatencyMillis, &m.CreativeId, &m.Revenue, &m.DspName, &m.DspId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdAttributeModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdAttributeModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdAttributeModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Md5Key, &m.Size, &m.AdUnitId, &m.NetworkName, &m.NetworkPlacement, &m.Placement, &m.Waterfall, &m.RequestLatencyMillis, &m.CreativeId, &m.Revenue, &m.DspName, &m.DspId)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdAttributeModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdAttributeModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdAttributeModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdAttributeModel, error) {

	m := &AdAttributeModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdAttributeModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdAttributeModel, error) {

	modelList := make([]*AdAttributeModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdAttributeModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdAttributeModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdAttributeModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdAttributeModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdAttributeModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.Md5Key, d.Size, d.AdUnitId, d.NetworkName, d.NetworkPlacement, d.Placement, d.Waterfall, d.RequestLatencyMillis, d.CreativeId, d.Revenue, d.DspName, d.DspId))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "md5_key",
			"title":     "属性唯一标识",
		},
		{
			"dataIndex": "size",
			"title":     "广告宽高",
		},
		{
			"dataIndex": "ad_unit_id",
			"title":     "广告单元id",
		},
		{
			"dataIndex": "network_name",
			"title":     "广告网络名称",
		},
		{
			"dataIndex": "network_placement",
			"title":     "第三方网络广告的placement 名称",
		},
		{
			"dataIndex": "placement",
			"title":     "自定义的placement名称",
		},
		{
			"dataIndex": "waterfall",
			"title":     "触发该广告的瀑布流名称",
		},
		{
			"dataIndex": "requestLatency_millis",
			"title":     "广告请求花费的时间",
		},
		{
			"dataIndex": "creative_id",
			"title":     "广告素材id",
		},
		{
			"dataIndex": "revenue",
			"title":     "该次广告产生的收益（美元）",
		},
		{
			"dataIndex": "dsp_name",
			"title":     "dsp 名称",
		},
		{
			"dataIndex": "dsp_id",
			"title":     "dsp的id",
		}}
}
