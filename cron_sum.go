package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/plus/server"
	"910.com/plus2.git/plusQ"
	"910.com/plus2.git/provider"
	"context"
	"iaa_data/crontab_summary_data"
	"log"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	configFile := "config/config_hw.yaml"
	_, err := plus.LoadServerConfigDefault(configFile)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}
	plus.AppConfig.ConsoleLogFile = "./log_common/crontab_summary/demo.log"
	provider.Register() //注册服务

	// 关闭信号
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM, os.Kill)
	defer stop()

	//注册接口
	services := server.NewCrontabServer(ctx, crontab_summary_data.NewService())
	//启动服务
	err = plus.New(ctx, services).Run("go_summary_crontab")
	if err != nil {
		plusQ.Logger().Alert("进程关闭", err)
	}
}
