package parser

import (
	"910.com/plus2.git/utils/string_util"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
)

type SdkData struct {
	Key  string         `json:"key"`
	Data map[string]any `json:"data"`
}

func Extract(rawStr string) ([]map[string]any, error) {
	var sdkData SdkData
	err := json.Unmarshal([]byte(rawStr), &sdkData)
	var data []map[string]any
	data = append(data, sdkData.Data)
	return data, err
}

// convertKey 转换 key 为驼峰和下划线形式
func convertKey[K comparable](key K) []any {
	//如果是字符串
	if Key, ok := any(key).(string); ok {
		camelKey := string_util.ToCamelCase(Key)   // 首字母大写的驼峰
		camelKey2 := string_util.LcFirst(camelKey) // 首字母小写的驼峰
		snakeKey := string_util.ToSnakeCase(Key)   // 下划线
		return []any{Key, camelKey, camelKey2, snakeKey}
	} else {
		return []any{key}
	}
}

// GetStr 获取字段值并转换为字符串, 尝试转换key成驼峰、下划线形式查找
func GetStr[K comparable, V any, T comparable](c *map[K]V, key T) string {
	for _, oneKey := range convertKey(key) {
		if value, ok := (*c)[any(oneKey).(K)]; ok {
			switch val := any(value).(type) {
			case string:
				return val
			case int:
				return strconv.Itoa(val)
			case uint:
				return strconv.Itoa(int(val))
			case uint32:
				return strconv.Itoa(int(val))
			case uint64:
				return strconv.Itoa(int(val))
			case int64:
				return strconv.FormatInt(val, 10)
			case float64:
				return strconv.FormatFloat(val, 'f', -1, 64)
			case bool:
				return strconv.FormatBool(val)
			default:
				//结构体
				if reflect.TypeOf(value).Kind() == reflect.Struct {
					return fmt.Sprintf("%v", val)
				}
				// 对于其他类型，我们继续循环，尝试其他的 key
				continue
			}
		}
	}
	// 如果所有的 key 尝试都失败了，才返回空字符串
	return ""
}

// GetInt 获取字段值并转换为int64， 尝试转换key成驼峰、下划线形式查找
func GetInt[K comparable, V any, T comparable](c *map[K]V, key T) int64 {
	for _, oneKey := range convertKey(key) {
		if value, ok := (*c)[any(oneKey).(K)]; ok {
			switch val := any(value).(type) {
			case int:
				return int64(val)
			case uint:
				return int64(val)
			case uint32:
				return int64(val)
			case uint64:
				return int64(val)
			case int64:
				return val
			case float64:
				return int64(val)
			case string:
				if i, err := strconv.ParseInt(val, 10, 64); err == nil {
					return i
				}
			default:
				// 对于其他类型，继续循环，尝试其他的 key
				continue
			}
		}
	}
	// 如果所有的 key 尝试都失败了，才返回 0
	return 0
}

// GetFloat 获取字段值并转换为float64， 尝试转换key成驼峰、下划线形式查找
func GetFloat[K comparable, V any, T comparable](c *map[K]V, key T) float64 {
	for _, oneKey := range convertKey(key) {
		if value, ok := (*c)[any(oneKey).(K)]; ok {
			switch val := any(value).(type) {
			case float64:
				return val
			case float32:
				return float64(val)
			case int:
				return float64(val)
			case uint:
				return float64(val)
			case uint32:
				return float64(val)
			case uint64:
				return float64(val)
			case int64:
				return float64(val)
			case string:
				if f, err := strconv.ParseFloat(val, 64); err == nil {
					return f
				}
			default:
				// 对于其他类型，继续循环，尝试其他的 key
				continue
			}
		}
	}
	// 如果所有的 key 尝试都失败了，才返回 0.0
	return 0.0
}
