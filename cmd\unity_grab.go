package cmd

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"time"
)

// 替换为你的 Unity API Key
const apiKey = "YOUR_UNITY_API_KEY"

// 替换为你的 organizationId
const organizationId = "YOUR_ORG_ID"

// 速率限制：每秒最多1次请求
const rateLimit = time.Second

// Unity 广告投放数据 API URL
const statsURL = "https://services.api.unity.com/statistics/v2/advertising/organization/%s/reporting/data"

// 请求参数结构体
type QueryParams struct {
	StartDate string
	EndDate   string
	// 可根据需要添加更多参数
}

// 返回数据结构体（根据实际返回结构调整）
type AdStatsResponse struct {
	Data []struct {
		Date        string  `json:"date"`
		Impressions int     `json:"impressions"`
		Clicks      int     `json:"clicks"`
		Spend       float64 `json:"spend"`
		// ... 其他投放相关字段
	} `json:"data"`
}

func fetchAdStats(params url.Values) (*AdStatsResponse, error) {
	url := fmt.Sprintf(statsURL, organizationId)
	url = fmt.Sprintf("%s?%s", url, params.Encode())

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Accept", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %s", string(body))
	}

	var result AdStatsResponse
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&result); err != nil {
		return nil, err
	}
	return &result, nil
}

func run2() {

	var startDate string
	var endDate string
	startDate = time.Now().AddDate(0, 0, -3).Format(time.DateOnly)
	endDate = time.Now().Format(time.DateOnly)

	params := url.Values{}

	t1, _ := time.Parse(time.DateOnly, startDate)
	t2, _ := time.Parse(time.DateOnly, endDate)

	params.Add("start", t1.Format(time.RFC3339))
	params.Add("end", t2.Format(time.RFC3339))
	params.Add("scale", "day")
	params.Add("metrics", "views,clicks,installs,spend,cpi,ctr,cvr")
	params.Add("breakdowns", "country,creativePack")
	params.Add("format", "json")

	ticker := time.NewTicker(rateLimit)
	defer ticker.Stop()

	for i := 0; i < 1; i++ { // 这里只抓取一次，实际可按需调整
		<-ticker.C // 速率限制
		stats, err := fetchAdStats(params)
		if err != nil {
			fmt.Fprintf(os.Stderr, "抓取失败: %v\n", err)
			continue
		}
		// 打印投放数据
		for _, d := range stats.Data {
			fmt.Printf("日期: %s, 展示: %d, 点击: %d, 消耗: %.2f\n", d.Date, d.Impressions, d.Clicks, d.Spend)
		}
	}
}
