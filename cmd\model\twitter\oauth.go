package twitter

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"iaa_data/utils"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

type OAuthClient struct {
	ConsumerKey       string
	ConsumerSecret    string
	AccessToken       string
	AccessTokenSecret string
	AccountId         string
}

func NewOAuthClient(accountId string) *OAuthClient {
	return &OAuthClient{
		ConsumerKey:       "*************************",
		ConsumerSecret:    "eTBmribgV4PntovNiNPj6UccYy494CSAeRK7fXvzWp9dQrfWHe",
		AccessToken:       "1816405098719010816-Ex49KeINCZQjlP8YC2aueQ9C7Mbj09",
		AccessTokenSecret: "8sQIpPqmvNk5woXzqvj4kdQnY6CQhbY8wRxRkNeHUGelw",
		AccountId:         accountId,
	}
}

func (c *OAuthClient) Request(endpoint string, params map[string]string, method string) ([]byte, error) {
	// Generate OAuth nonce
	oauthNonce := generateNonce()

	urlStr := fmt.Sprintf("https://ads-api.twitter.com/11/accounts/%s/%s", c.AccountId, endpoint)

	if endpoint == "stats" {
		urlStr = fmt.Sprintf("https://ads-api.twitter.com/11/stats/accounts/%s", endpoint)
	}

	oauth := map[string]string{
		"oauth_consumer_key":     c.ConsumerKey,
		"oauth_nonce":            oauthNonce,
		"oauth_signature_method": "HMAC-SHA1",
		"oauth_timestamp":        strconv.FormatInt(time.Now().Unix(), 10),
		"oauth_version":          "1.0",
		"oauth_token":            c.AccessToken,
	}

	// Merge params and oauth for base string
	allParams := make(map[string]string)
	for k, v := range params {
		allParams[k] = v
	}
	for k, v := range oauth {
		allParams[k] = v
	}

	baseInfo := c.buildBaseString(urlStr, method, allParams)
	compositeKey := c.ConsumerSecret + "&" + c.AccessTokenSecret
	oauthSignature := c.generateSignature(baseInfo, compositeKey)
	oauth["oauth_signature"] = oauthSignature

	headers := map[string]string{
		"Authorization": c.buildAuthorizationHeader(oauth),
	}

	var resp *http.Response
	var err error

	if method == "POST" {
		form := url.Values{}
		for k, v := range params {
			form.Add(k, v)
		}
		resp, err = c.postRequest(urlStr, form, headers)
	} else {
		query := url.Values{}
		for k, v := range params {
			query.Add(k, v)
		}
		resp, err = c.getRequest(urlStr, query, headers)
	}

	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	utils.Debug("响应结果", string(body))

	return body, nil
}

func generateNonce() string {
	const chars = "ABCDEFGHIJKMNLOPQRSTUVWXIZabcdefghijkmnpqrstuvwxy"
	rand.Seed(time.Now().UnixNano())
	b := make([]byte, 11)
	for i := range b {
		b[i] = chars[rand.Intn(len(chars))]
	}
	return string(b)
}

func (c *OAuthClient) buildAuthorizationHeader(oauth map[string]string) string {
	// Sort the keys
	keys := make([]string, 0, len(oauth))
	for k := range oauth {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var values []string
	for _, k := range keys {
		values = append(values, fmt.Sprintf(`%s="%s"`, k, url.QueryEscape(oauth[k])))
	}

	return "OAuth " + strings.Join(values, ", ")
}

func (c *OAuthClient) buildBaseString(baseURI, method string, params map[string]string) string {
	// Sort the keys
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var parts []string
	for _, k := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", k, url.QueryEscape(params[k])))
	}

	return url.QueryEscape(method) + "&" + url.QueryEscape(baseURI) + "&" + url.QueryEscape(strings.Join(parts, "&"))
}

func (c *OAuthClient) generateSignature(baseString, key string) string {
	mac := hmac.New(sha1.New, []byte(key))
	mac.Write([]byte(baseString))
	return base64.StdEncoding.EncodeToString(mac.Sum(nil))
}

func (*OAuthClient) getWithProxy() *http.Client {
	var client *http.Client
	if os.Getenv("APP_ENV") == "dev" {
		proxyURL := "http://127.0.0.1:33210"
		proxy, _ := url.Parse(proxyURL)
		client = &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxy),
			},
		}
	} else {
		client = &http.Client{
			Timeout: 30 * time.Second,
		}
	}
	return client
}

func (c *OAuthClient) postRequest(urlStr string, form url.Values, headers map[string]string) (*http.Response, error) {
	req, err := http.NewRequest("POST", urlStr, strings.NewReader(form.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	client := c.getWithProxy()
	return client.Do(req)
}

func (c *OAuthClient) getRequest(urlStr string, query url.Values, headers map[string]string) (*http.Response, error) {
	if len(query) > 0 {
		urlStr += "?" + query.Encode()
	}

	utils.Debug("发送GET请求", urlStr)

	req, err := http.NewRequest("GET", urlStr, nil)
	if err != nil {
		return nil, err
	}

	for k, v := range headers {
		req.Header.Set(k, v)
	}

	client := c.getWithProxy()
	return client.Do(req)
}
