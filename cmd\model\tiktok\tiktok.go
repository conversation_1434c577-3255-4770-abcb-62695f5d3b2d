package tiktok

import "encoding/json"

type TikTokReportRequest struct {
	AdvertiserID string   `json:"advertiser_id"` // 广告主ID
	ServiceType  string   `json:"service_type"`  // 服务类型，如"AUCTION"
	ReportType   string   `json:"report_type"`   // 报告类型，如"BASIC"
	DataLevel    string   `json:"data_level"`    // 数据级别，如"AUCTION_AD"
	Dimensions   []string `json:"dimensions"`    // 维度列表
	Metrics      []string `json:"metrics"`       // 指标列表
	StartDate    string   `json:"start_date"`    // 报告开始日期，格式: "YYYY-MM-DD"
	EndDate      string   `json:"end_date"`      // 报告结束日期，格式: "YYYY-MM-DD"
	AccessToken  string   `json:"access_token"`  // 访问令牌
}

type TikTokReportResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Data      struct {
		PageInfo struct {
			TotalNumber int `json:"total_number"`
			Page        int `json:"page"`
			PageSize    int `json:"page_size"`
			TotalPage   int `json:"total_page"`
		} `json:"page_info"`
		List []ReportItem `json:"list"`
	} `json:"data"`
}

type ReportItem struct {
	Dimensions Dimensions `json:"dimensions"`
	Metrics    Metrics    `json:"metrics"`
}
type Dimensions struct {
	AdId         string `json:"ad_id"`
	CountryCode  string `json:"country_code"`
	StatTimeDay  string `json:"stat_time_day"`
	StatTimeHour string `json:"stat_time_hour"`
}
type Metrics struct {
	AdId         string      `json:"ad_id"`
	AdName       string      `json:"ad_name"`
	AdGroupId    string      `json:"adgroup_id"`
	AdGroupName  string      `json:"adgroup_name"`
	CampaignId   string      `json:"campaign_id"`
	CampaignName string      `json:"campaign_name"`
	AppInstall   json.Number `json:"app_install"`
	Conversion   json.Number `json:"conversion"`
	Clicks       json.Number `json:"clicks"`
	Cpm          json.Number `json:"cpm"`
	Ctr          json.Number `json:"ctr"`
	Impressions  json.Number `json:"impressions"`
	Spend        json.Number `json:"spend"`
}
