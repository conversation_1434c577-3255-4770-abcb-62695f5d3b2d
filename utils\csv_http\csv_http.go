package csv_http

import (
	"encoding/csv"
	"fmt"
	"net/http"
	"strings"
)

func GetCsvData(url string) []map[string]interface{} {
	// 发起 HTTP GET 请求
	resp, err := http.Get(url)
	if err != nil {
		fmt.Println("Error fetching the URL:", err)
		return nil
	}
	defer resp.Body.Close()

	// 检查 HTTP 响应状态码
	if resp.StatusCode != http.StatusOK {
		fmt.Println("Failed to fetch the CSV, status code:", resp.StatusCode)
		return nil
	}

	// 创建一个 CSV 阅读器
	reader := csv.NewReader(resp.Body)

	// 读取 CSV 文件的所有记录
	records, err := reader.ReadAll()
	if err != nil {
		fmt.Println("Error reading the CSV file:", err)
		return nil
	}

	var list []map[string]interface{}
	keyList := make([]string, 0)
	valList := make([][]string, 0)
	// 打印 CSV 文件的内容
	for i, record := range records {
		if i == 0 {
			keyList = record
		} else {
			valList = append(valList, record)
		}
	}
	for i, key := range keyList {
		newKey := strings.Replace(key, " ", "_", -1)
		newKey = strings.ToLower(newKey)
		keyList[i] = newKey
	}

	for _, val := range valList {
		item := make(map[string]interface{})
		for i, itemVal := range val {
			item[keyList[i]] = itemVal
		}
		list = append(list, item)
	}
	return list
}
