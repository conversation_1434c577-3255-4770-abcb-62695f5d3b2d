package max_ad

//MarketingMaterialGroupModel

// 新增接口请求参数
type AddRequest struct {
	// GROUPNAME 分组名称
	Idfa          string `json:"idfa"  `
	AdUnitId      string `json:"ad_unit_id" `
	AdUnitName    string `json:"ad_unit_name"  `
	Amount        string `json:"amount"  `
	Country       string `json:"country" `
	Currency      string `json:"currency" `
	CustomData    string `json:"custom_data"  `
	EventId       string `json:"event_id" `
	EventToken    string `json:"event_token"  `
	EventTokenAll string `json:"event_token_all" `
	Idfv          string `json:"idfv"  `
	Ip            string `json:"ip" `
	NetworkName   string `json:"network_name"  `
	PackageName   string `json:"package_name"  `
	Placement     string `json:"placement"  `
	Platform      string `json:"platform"  `
	Ts            string `json:"ts"  `
	UserId        string `json:"user_id" `
}
