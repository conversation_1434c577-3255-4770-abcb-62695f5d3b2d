package ad_report_api

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_report_api")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// AdFormat 广告单元广告类型：INTER、BANNER、REWARD
	AdFormat = property.Property("ad_format")
	// AdUnitWaterfallName 广告单元瀑布的名称。
	AdUnitWaterfallName = property.Property("ad_unit_waterfall_name")
	// Application 应用程序的名称
	Application = property.Property("application")
	// Attempts 尝试投放广告网络的次数
	Attempts = property.Property("attempts")
	// Country 国家代码
	Country = property.Property("country")
	// CustomNetworkName 网络名称
	CustomNetworkName = property.Property("custom_network_name")
	// Day 数据的日期«YYYY»-«MM»-«DD»
	Day = property.Property("day")
	// DeviceType 用户的设备类型
	DeviceType = property.Property("device_type")
	// Ecpm 预计产生的 eCPM 以美元计
	Ecpm = property.Property("ecpm")
	// EstimatedRevenue 预计产生的收入以美元计算
	EstimatedRevenue = property.Property("estimated_revenue")
	// HasIdfa 用户是否有可用的广告 ID
	HasIdfa = property.Property("has_idfa")
	// Hour 数据的小时（仅适用于过去 30 天内的日期）。
	Hour = property.Property("hour")
	// Impressions 显示的印象数
	Impressions = property.Property("impressions")
	// MaxAdUnitId 最大广告单元 ID
	MaxAdUnitId = property.Property("max_ad_unit_id")
	// MaxAdUnitTest MAX 广告单元测试组名称（如果适用）
	MaxAdUnitTest = property.Property("max_ad_unit_test")
	// MaxPlacement MAX 中介安置名称（如果适用）。
	MaxPlacement = property.Property("max_placement")
	// Network 广告网络名称
	Network = property.Property("network")
	// NetworkPlacement 外部广告网络的放置
	NetworkPlacement = property.Property("network_placement")
	// PackageName 包名
	PackageName = property.Property("package_name")
	// Platform 应用程序平台：android、fireos、 或ios
	Platform = property.Property("platform")
	// Requests 广告请求数（如果包含、或列则无法显示）
	Requests = property.Property("requests")
	// Responses 来自广告网络的响应数
	Responses = property.Property("responses")
	// StoreId iOS 系统中 iTunes ID 的数字部分，或 Android 系统中应用的软件包名称
	StoreId = property.Property("store_id")
)

var FieldsAll = Fields(Id, AdFormat, AdUnitWaterfallName, Application, Attempts, Country, CustomNetworkName, Day, DeviceType, Ecpm, EstimatedRevenue, HasIdfa, Hour, Impressions, MaxAdUnitId, MaxAdUnitTest, MaxPlacement, Network, NetworkPlacement, PackageName, Platform, Requests, Responses, StoreId)
var NonePrimaryFields = Fields(AdFormat, AdUnitWaterfallName, Application, Attempts, Country, CustomNetworkName, Day, DeviceType, Ecpm, EstimatedRevenue, HasIdfa, Hour, Impressions, MaxAdUnitId, MaxAdUnitTest, MaxPlacement, Network, NetworkPlacement, PackageName, Platform, Requests, Responses, StoreId)
var NoneAutoIncrementFields = Fields(AdFormat, AdUnitWaterfallName, Application, Attempts, Country, CustomNetworkName, Day, DeviceType, Ecpm, EstimatedRevenue, HasIdfa, Hour, Impressions, MaxAdUnitId, MaxAdUnitTest, MaxPlacement, Network, NetworkPlacement, PackageName, Platform, Requests, Responses, StoreId)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdReportApiModel 实时广告收入API抓取日志
type AdReportApiModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// AdFormat 广告单元广告类型：INTER、BANNER、REWARD
	AdFormat *string `orm:"ad_format" json:"ad_format"`

	// AdUnitWaterfallName 广告单元瀑布的名称。
	AdUnitWaterfallName *string `orm:"ad_unit_waterfall_name" json:"ad_unit_waterfall_name"`

	// Application 应用程序的名称
	Application *string `orm:"application" json:"application"`

	// Attempts 尝试投放广告网络的次数
	Attempts *int `orm:"attempts" json:"attempts"`

	// Country 国家代码
	Country *string `orm:"country" json:"country"`

	// CustomNetworkName 网络名称
	CustomNetworkName *string `orm:"custom_network_name" json:"custom_network_name"`

	// Day 数据的日期«YYYY»-«MM»-«DD»
	Day *CustomTime `orm:"day" json:"day"`

	// DeviceType 用户的设备类型
	DeviceType *string `orm:"device_type" json:"device_type"`

	// Ecpm 预计产生的 eCPM 以美元计
	Ecpm *string `orm:"ecpm" json:"ecpm"`

	// EstimatedRevenue 预计产生的收入以美元计算
	EstimatedRevenue *string `orm:"estimated_revenue" json:"estimated_revenue"`

	// HasIdfa 用户是否有可用的广告 ID
	HasIdfa *string `orm:"has_idfa" json:"has_idfa"`

	// Hour 数据的小时（仅适用于过去 30 天内的日期）。
	Hour *string `orm:"hour" json:"hour"`

	// Impressions 显示的印象数
	Impressions *string `orm:"impressions" json:"impressions"`

	// MaxAdUnitId 最大广告单元 ID
	MaxAdUnitId *string `orm:"max_ad_unit_id" json:"max_ad_unit_id"`

	// MaxAdUnitTest MAX 广告单元测试组名称（如果适用）
	MaxAdUnitTest *string `orm:"max_ad_unit_test" json:"max_ad_unit_test"`

	// MaxPlacement MAX 中介安置名称（如果适用）。
	MaxPlacement *string `orm:"max_placement" json:"max_placement"`

	// Network 广告网络名称
	Network *string `orm:"network" json:"network"`

	// NetworkPlacement 外部广告网络的放置
	NetworkPlacement *string `orm:"network_placement" json:"network_placement"`

	// PackageName 包名
	PackageName *string `orm:"package_name" json:"package_name"`

	// Platform 应用程序平台：android、fireos、 或ios
	Platform *string `orm:"platform" json:"platform"`

	// Requests 广告请求数（如果包含、或列则无法显示）
	Requests *string `orm:"requests" json:"requests"`

	// Responses 来自广告网络的响应数
	Responses *string `orm:"responses" json:"responses"`

	// StoreId iOS 系统中 iTunes ID 的数字部分，或 Android 系统中应用的软件包名称
	StoreId *string `orm:"store_id" json:"store_id"`
}

type PagedResult struct {
	Records      []*AdReportApiModel `json:"list"`
	PageNum      int                 `json:"page"`
	PageSize     int                 `json:"page_size"`
	TotalPages   int                 `json:"total_pages"`
	TotalRecords int                 `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:                  1,
	AdFormat:            2,
	AdUnitWaterfallName: 3,
	Application:         4,
	Attempts:            5,
	Country:             6,
	CustomNetworkName:   7,
	Day:                 8,
	DeviceType:          9,
	Ecpm:                10,
	EstimatedRevenue:    11,
	HasIdfa:             12,
	Hour:                13,
	Impressions:         14,
	MaxAdUnitId:         15,
	MaxAdUnitTest:       16,
	MaxPlacement:        17,
	Network:             18,
	NetworkPlacement:    19,
	PackageName:         20,
	Platform:            21,
	Requests:            22,
	Responses:           23,
	StoreId:             24,
}

func (m *AdReportApiModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdReportApiModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.AdFormat, m.AdUnitWaterfallName, m.Application, m.Attempts, m.Country, m.CustomNetworkName, m.Day, m.DeviceType, m.Ecpm, m.EstimatedRevenue, m.HasIdfa, m.Hour, m.Impressions, m.MaxAdUnitId, m.MaxAdUnitTest, m.MaxPlacement, m.Network, m.NetworkPlacement, m.PackageName, m.Platform, m.Requests, m.Responses, m.StoreId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdReportApiModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AdFormat, m.AdUnitWaterfallName, m.Application, m.Attempts, m.Country, m.CustomNetworkName, m.Day, m.DeviceType, m.Ecpm, m.EstimatedRevenue, m.HasIdfa, m.Hour, m.Impressions, m.MaxAdUnitId, m.MaxAdUnitTest, m.MaxPlacement, m.Network, m.NetworkPlacement, m.PackageName, m.Platform, m.Requests, m.Responses, m.StoreId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdReportApiModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdReportApiModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AdFormat, m.AdUnitWaterfallName, m.Application, m.Attempts, m.Country, m.CustomNetworkName, m.Day, m.DeviceType, m.Ecpm, m.EstimatedRevenue, m.HasIdfa, m.Hour, m.Impressions, m.MaxAdUnitId, m.MaxAdUnitTest, m.MaxPlacement, m.Network, m.NetworkPlacement, m.PackageName, m.Platform, m.Requests, m.Responses, m.StoreId)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportApiModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdReportApiModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AdFormat, m.AdUnitWaterfallName, m.Application, m.Attempts, m.Country, m.CustomNetworkName, m.Day, m.DeviceType, m.Ecpm, m.EstimatedRevenue, m.HasIdfa, m.Hour, m.Impressions, m.MaxAdUnitId, m.MaxAdUnitTest, m.MaxPlacement, m.Network, m.NetworkPlacement, m.PackageName, m.Platform, m.Requests, m.Responses, m.StoreId), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportApiModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdReportApiModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.AdFormat, m.AdUnitWaterfallName, m.Application, m.Attempts, m.Country, m.CustomNetworkName, m.Day, m.DeviceType, m.Ecpm, m.EstimatedRevenue, m.HasIdfa, m.Hour, m.Impressions, m.MaxAdUnitId, m.MaxAdUnitTest, m.MaxPlacement, m.Network, m.NetworkPlacement, m.PackageName, m.Platform, m.Requests, m.Responses, m.StoreId), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AdFormat, m.AdUnitWaterfallName, m.Application, m.Attempts, m.Country, m.CustomNetworkName, m.Day, m.DeviceType, m.Ecpm, m.EstimatedRevenue, m.HasIdfa, m.Hour, m.Impressions, m.MaxAdUnitId, m.MaxAdUnitTest, m.MaxPlacement, m.Network, m.NetworkPlacement, m.PackageName, m.Platform, m.Requests, m.Responses, m.StoreId))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdReportApiModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdReportApiModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdReportApiModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdReportApiModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdReportApiModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdReportApiModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdReportApiModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdReportApiModel, error) {

	modelList := make([]*AdReportApiModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdReportApiModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdFormat, &m.AdUnitWaterfallName, &m.Application, &m.Attempts, &m.Country, &m.CustomNetworkName, &m.Day, &m.DeviceType, &m.Ecpm, &m.EstimatedRevenue, &m.HasIdfa, &m.Hour, &m.Impressions, &m.MaxAdUnitId, &m.MaxAdUnitTest, &m.MaxPlacement, &m.Network, &m.NetworkPlacement, &m.PackageName, &m.Platform, &m.Requests, &m.Responses, &m.StoreId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdReportApiModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdReportApiModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdReportApiModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdReportApiModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdReportApiModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdReportApiModel, error) {

	m := &AdReportApiModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.AdFormat, &m.AdUnitWaterfallName, &m.Application, &m.Attempts, &m.Country, &m.CustomNetworkName, &m.Day, &m.DeviceType, &m.Ecpm, &m.EstimatedRevenue, &m.HasIdfa, &m.Hour, &m.Impressions, &m.MaxAdUnitId, &m.MaxAdUnitTest, &m.MaxPlacement, &m.Network, &m.NetworkPlacement, &m.PackageName, &m.Platform, &m.Requests, &m.Responses, &m.StoreId)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdReportApiModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdReportApiModel, error) {

	modelList := make([]*AdReportApiModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdReportApiModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdFormat, &m.AdUnitWaterfallName, &m.Application, &m.Attempts, &m.Country, &m.CustomNetworkName, &m.Day, &m.DeviceType, &m.Ecpm, &m.EstimatedRevenue, &m.HasIdfa, &m.Hour, &m.Impressions, &m.MaxAdUnitId, &m.MaxAdUnitTest, &m.MaxPlacement, &m.Network, &m.NetworkPlacement, &m.PackageName, &m.Platform, &m.Requests, &m.Responses, &m.StoreId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdReportApiModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdReportApiModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdReportApiModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AdFormat, &m.AdUnitWaterfallName, &m.Application, &m.Attempts, &m.Country, &m.CustomNetworkName, &m.Day, &m.DeviceType, &m.Ecpm, &m.EstimatedRevenue, &m.HasIdfa, &m.Hour, &m.Impressions, &m.MaxAdUnitId, &m.MaxAdUnitTest, &m.MaxPlacement, &m.Network, &m.NetworkPlacement, &m.PackageName, &m.Platform, &m.Requests, &m.Responses, &m.StoreId)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdReportApiModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdReportApiModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdReportApiModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdReportApiModel, error) {

	m := &AdReportApiModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdReportApiModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdReportApiModel, error) {

	modelList := make([]*AdReportApiModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdReportApiModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdReportApiModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdReportApiModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdReportApiModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdReportApiModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.AdFormat, d.AdUnitWaterfallName, d.Application, d.Attempts, d.Country, d.CustomNetworkName, d.Day, d.DeviceType, d.Ecpm, d.EstimatedRevenue, d.HasIdfa, d.Hour, d.Impressions, d.MaxAdUnitId, d.MaxAdUnitTest, d.MaxPlacement, d.Network, d.NetworkPlacement, d.PackageName, d.Platform, d.Requests, d.Responses, d.StoreId))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "ad_format",
			"title":     "广告单元广告类型：INTER、BANNER、REWARD",
		},
		{
			"dataIndex": "ad_unit_waterfall_name",
			"title":     "广告单元瀑布的名称。",
		},
		{
			"dataIndex": "application",
			"title":     "应用程序的名称",
		},
		{
			"dataIndex": "attempts",
			"title":     "尝试投放广告网络的次数",
		},
		{
			"dataIndex": "country",
			"title":     "国家代码",
		},
		{
			"dataIndex": "custom_network_name",
			"title":     "网络名称",
		},
		{
			"dataIndex": "day",
			"title":     "数据的日期«YYYY»-«MM»-«DD»",
		},
		{
			"dataIndex": "device_type",
			"title":     "用户的设备类型",
		},
		{
			"dataIndex": "ecpm",
			"title":     "预计产生的 eCPM 以美元计",
		},
		{
			"dataIndex": "estimated_revenue",
			"title":     "预计产生的收入以美元计算",
		},
		{
			"dataIndex": "has_idfa",
			"title":     "用户是否有可用的广告 ID",
		},
		{
			"dataIndex": "hour",
			"title":     "数据的小时（仅适用于过去 30 天内的日期）。",
		},
		{
			"dataIndex": "impressions",
			"title":     "显示的印象数",
		},
		{
			"dataIndex": "max_ad_unit_id",
			"title":     "最大广告单元 ID",
		},
		{
			"dataIndex": "max_ad_unit_test",
			"title":     "MAX 广告单元测试组名称（如果适用）",
		},
		{
			"dataIndex": "max_placement",
			"title":     "MAX 中介安置名称（如果适用）。",
		},
		{
			"dataIndex": "network",
			"title":     "广告网络名称",
		},
		{
			"dataIndex": "network_placement",
			"title":     "外部广告网络的放置",
		},
		{
			"dataIndex": "package_name",
			"title":     "包名",
		},
		{
			"dataIndex": "platform",
			"title":     "应用程序平台：android、fireos、 或ios",
		},
		{
			"dataIndex": "requests",
			"title":     "广告请求数（如果包含、或列则无法显示）",
		},
		{
			"dataIndex": "responses",
			"title":     "来自广告网络的响应数",
		},
		{
			"dataIndex": "store_id",
			"title":     "iOS 系统中 iTunes ID 的数字部分，或 Android 系统中应用的软件包名称",
		}}
}
