package report_helper

import (
	"encoding/json"
	"iaa_data/utils/http_utils"
	"log"
)

func GetReport(url string, params map[string]interface{}) *ResultData {
	headMap := make(map[string]string)
	params["format"] = "json"
	url = url + "?" + http_utils.GetParamsUrl(params)
	log.Println("url:", url)
	result, err := http_utils.HttpRequest(url, "GET", params, headMap)
	if err != nil {
		log.Println(err.Error())
		return nil
	} else {
		//log.Println(result)
	}
	var resultData ResultData
	err = json.Unmarshal([]byte(result), &resultData)
	if err != nil {
		log.Println(err.Error())
		return nil
	}
	return &resultData
}

type ResultData struct {
	Code           int                      `json:"code"`
	MaxReportItems []map[string]interface{} `json:"results"`
}
