package hw_device_payment_source

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("hw_device_payment_source")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id
	Id = property.Property("id")
	// TimeServer 服务器接收时间
	TimeServer = property.Property("time_server")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 平台游戏ID
	GameId = property.Property("game_id")
	// PackageId 包号
	PackageId = property.Property("package_id")
	// PackageName 包名或BundleId
	PackageName = property.Property("package_name")
	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv = property.Property("aid_or_idfv")
	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa = property.Property("gid_or_idfa")
	// Source 媒体渠道
	Source = property.Property("source")
	// CampaignId 广告组ID    CAMPAIGN_GROUP_ID（广告系列ID）
	CampaignId = property.Property("campaign_id")
	// CampaignName 广告组名称 CAMPAIGN_GROUP_NAME （广告系列名称）
	CampaignName = property.Property("campaign_name")
	// PlanId 计划ID       CAMPAIGN_ID （广告组ID ）
	PlanId = property.Property("plan_id")
	// PlanName 计划名       CAMPAIGN_NAME （广告组名称）
	PlanName = property.Property("plan_name")
	// CreativeId 创意ID       AD_GROUP_ID（广告ID）
	CreativeId = property.Property("creative_id")
	// CreativeName 创意名       AD_GROUP_NAME （广告名）
	CreativeName = property.Property("creative_name")
	// DeviceLanguage 设备语言
	DeviceLanguage = property.Property("device_language")
	// CountryCode 国家代码
	CountryCode = property.Property("country_code")
	// Ip IP
	Ip = property.Property("ip")
	// Env 环境
	Env = property.Property("env")
	// Ext 扩展字段
	Ext = property.Property("ext")
	// CoreAccount 登录账号
	CoreAccount = property.Property("core_account")
	// OrderId 我方订单号
	OrderId = property.Property("order_id")
	// Money 订单金额
	Money = property.Property("money")
	// PayTime 订单支付时间
	PayTime = property.Property("pay_time")
	// RawData 原始数据
	RawData = property.Property("raw_data")
	// AdjustId adjust_id
	AdjustId = property.Property("adjust_id")
)

var FieldsAll = Fields(Id, TimeServer, CpGameId, GameId, PackageId, PackageName, AidOrIdfv, GidOrIdfa, Source, CampaignId, CampaignName, PlanId, PlanName, CreativeId, CreativeName, DeviceLanguage, CountryCode, Ip, Env, Ext, CoreAccount, OrderId, Money, PayTime, RawData, AdjustId)
var NonePrimaryFields = Fields(TimeServer, CpGameId, GameId, PackageId, PackageName, AidOrIdfv, GidOrIdfa, Source, CampaignId, CampaignName, PlanId, PlanName, CreativeId, CreativeName, DeviceLanguage, CountryCode, Ip, Env, Ext, CoreAccount, OrderId, Money, PayTime, RawData, AdjustId)
var NoneAutoIncrementFields = Fields(TimeServer, CpGameId, GameId, PackageId, PackageName, AidOrIdfv, GidOrIdfa, Source, CampaignId, CampaignName, PlanId, PlanName, CreativeId, CreativeName, DeviceLanguage, CountryCode, Ip, Env, Ext, CoreAccount, OrderId, Money, PayTime, RawData, AdjustId)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// HwDevicePaymentSourceModel 海外媒体付费表
type HwDevicePaymentSourceModel struct {
	// Id
	Id int `orm:"id" json:"id"`

	// TimeServer 服务器接收时间
	TimeServer CustomTime `orm:"time_server" json:"time_server"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 平台游戏ID
	GameId uint32 `orm:"game_id" json:"game_id"`

	// PackageId 包号
	PackageId int `orm:"package_id" json:"package_id"`

	// PackageName 包名或BundleId
	PackageName string `orm:"package_name" json:"package_name"`

	// AidOrIdfv 安卓ID or idfv
	AidOrIdfv string `orm:"aid_or_idfv" json:"aid_or_idfv"`

	// GidOrIdfa 谷歌ID or 苹果ID
	GidOrIdfa string `orm:"gid_or_idfa" json:"gid_or_idfa"`

	// Source 媒体渠道
	Source string `orm:"source" json:"source"`

	// CampaignId 广告组ID    CAMPAIGN_GROUP_ID（广告系列ID）
	CampaignId string `orm:"campaign_id" json:"campaign_id"`

	// CampaignName 广告组名称 CAMPAIGN_GROUP_NAME （广告系列名称）
	CampaignName string `orm:"campaign_name" json:"campaign_name"`

	// PlanId 计划ID       CAMPAIGN_ID （广告组ID ）
	PlanId string `orm:"plan_id" json:"plan_id"`

	// PlanName 计划名       CAMPAIGN_NAME （广告组名称）
	PlanName string `orm:"plan_name" json:"plan_name"`

	// CreativeId 创意ID       AD_GROUP_ID（广告ID）
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// CreativeName 创意名       AD_GROUP_NAME （广告名）
	CreativeName string `orm:"creative_name" json:"creative_name"`

	// DeviceLanguage 设备语言
	DeviceLanguage string `orm:"device_language" json:"device_language"`

	// CountryCode 国家代码
	CountryCode string `orm:"country_code" json:"country_code"`

	// Ip IP
	Ip string `orm:"ip" json:"ip"`

	// Env 环境
	Env string `orm:"env" json:"env"`

	// Ext 扩展字段
	Ext string `orm:"ext" json:"ext"`

	// CoreAccount 登录账号
	CoreAccount string `orm:"core_account" json:"core_account"`

	// OrderId 我方订单号
	OrderId string `orm:"order_id" json:"order_id"`

	// Money 订单金额
	Money float32 `orm:"money" json:"money"`

	// PayTime 订单支付时间
	PayTime CustomTime `orm:"pay_time" json:"pay_time"`

	// RawData 原始数据
	RawData *string `orm:"raw_data" json:"raw_data"`

	// AdjustId adjust_id
	AdjustId string `orm:"adjust_id" json:"adjust_id"`
}

type PagedResult struct {
	Records      []*HwDevicePaymentSourceModel `json:"list"`
	PageNum      int                           `json:"page"`
	PageSize     int                           `json:"page_size"`
	TotalPages   int                           `json:"total_pages"`
	TotalRecords int                           `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:             1,
	TimeServer:     2,
	CpGameId:       3,
	GameId:         4,
	PackageId:      5,
	PackageName:    6,
	AidOrIdfv:      7,
	GidOrIdfa:      8,
	Source:         9,
	CampaignId:     10,
	CampaignName:   11,
	PlanId:         12,
	PlanName:       13,
	CreativeId:     14,
	CreativeName:   15,
	DeviceLanguage: 16,
	CountryCode:    17,
	Ip:             18,
	Env:            19,
	Ext:            20,
	CoreAccount:    21,
	OrderId:        22,
	Money:          23,
	PayTime:        24,
	RawData:        25,
	AdjustId:       26,
}

func (m *HwDevicePaymentSourceModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *HwDevicePaymentSourceModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.TimeServer, m.CpGameId, m.GameId, m.PackageId, m.PackageName, m.AidOrIdfv, m.GidOrIdfa, m.Source, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.DeviceLanguage, m.CountryCode, m.Ip, m.Env, m.Ext, m.CoreAccount, m.OrderId, m.Money, m.PayTime, m.RawData, m.AdjustId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *HwDevicePaymentSourceModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.TimeServer, m.CpGameId, m.GameId, m.PackageId, m.PackageName, m.AidOrIdfv, m.GidOrIdfa, m.Source, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.DeviceLanguage, m.CountryCode, m.Ip, m.Env, m.Ext, m.CoreAccount, m.OrderId, m.Money, m.PayTime, m.RawData, m.AdjustId)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *HwDevicePaymentSourceModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *HwDevicePaymentSourceModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.TimeServer, m.CpGameId, m.GameId, m.PackageId, m.PackageName, m.AidOrIdfv, m.GidOrIdfa, m.Source, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.DeviceLanguage, m.CountryCode, m.Ip, m.Env, m.Ext, m.CoreAccount, m.OrderId, m.Money, m.PayTime, m.RawData, m.AdjustId)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwDevicePaymentSourceModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *HwDevicePaymentSourceModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.TimeServer, m.CpGameId, m.GameId, m.PackageId, m.PackageName, m.AidOrIdfv, m.GidOrIdfa, m.Source, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.DeviceLanguage, m.CountryCode, m.Ip, m.Env, m.Ext, m.CoreAccount, m.OrderId, m.Money, m.PayTime, m.RawData, m.AdjustId), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwDevicePaymentSourceModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *HwDevicePaymentSourceModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.TimeServer, m.CpGameId, m.GameId, m.PackageId, m.PackageName, m.AidOrIdfv, m.GidOrIdfa, m.Source, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.DeviceLanguage, m.CountryCode, m.Ip, m.Env, m.Ext, m.CoreAccount, m.OrderId, m.Money, m.PayTime, m.RawData, m.AdjustId), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.TimeServer, m.CpGameId, m.GameId, m.PackageId, m.PackageName, m.AidOrIdfv, m.GidOrIdfa, m.Source, m.CampaignId, m.CampaignName, m.PlanId, m.PlanName, m.CreativeId, m.CreativeName, m.DeviceLanguage, m.CountryCode, m.Ip, m.Env, m.Ext, m.CoreAccount, m.OrderId, m.Money, m.PayTime, m.RawData, m.AdjustId))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *HwDevicePaymentSourceModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *HwDevicePaymentSourceModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *HwDevicePaymentSourceModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *HwDevicePaymentSourceModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *HwDevicePaymentSourceModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *HwDevicePaymentSourceModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*HwDevicePaymentSourceModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*HwDevicePaymentSourceModel, error) {

	modelList := make([]*HwDevicePaymentSourceModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwDevicePaymentSourceModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.TimeServer, &m.CpGameId, &m.GameId, &m.PackageId, &m.PackageName, &m.AidOrIdfv, &m.GidOrIdfa, &m.Source, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.DeviceLanguage, &m.CountryCode, &m.Ip, &m.Env, &m.Ext, &m.CoreAccount, &m.OrderId, &m.Money, &m.PayTime, &m.RawData, &m.AdjustId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*HwDevicePaymentSourceModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*HwDevicePaymentSourceModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*HwDevicePaymentSourceModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*HwDevicePaymentSourceModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*HwDevicePaymentSourceModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*HwDevicePaymentSourceModel, error) {

	m := &HwDevicePaymentSourceModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.TimeServer, &m.CpGameId, &m.GameId, &m.PackageId, &m.PackageName, &m.AidOrIdfv, &m.GidOrIdfa, &m.Source, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.DeviceLanguage, &m.CountryCode, &m.Ip, &m.Env, &m.Ext, &m.CoreAccount, &m.OrderId, &m.Money, &m.PayTime, &m.RawData, &m.AdjustId)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*HwDevicePaymentSourceModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*HwDevicePaymentSourceModel, error) {

	modelList := make([]*HwDevicePaymentSourceModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &HwDevicePaymentSourceModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.TimeServer, &m.CpGameId, &m.GameId, &m.PackageId, &m.PackageName, &m.AidOrIdfv, &m.GidOrIdfa, &m.Source, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.DeviceLanguage, &m.CountryCode, &m.Ip, &m.Env, &m.Ext, &m.CoreAccount, &m.OrderId, &m.Money, &m.PayTime, &m.RawData, &m.AdjustId)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*HwDevicePaymentSourceModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*HwDevicePaymentSourceModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &HwDevicePaymentSourceModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.TimeServer, &m.CpGameId, &m.GameId, &m.PackageId, &m.PackageName, &m.AidOrIdfv, &m.GidOrIdfa, &m.Source, &m.CampaignId, &m.CampaignName, &m.PlanId, &m.PlanName, &m.CreativeId, &m.CreativeName, &m.DeviceLanguage, &m.CountryCode, &m.Ip, &m.Env, &m.Ext, &m.CoreAccount, &m.OrderId, &m.Money, &m.PayTime, &m.RawData, &m.AdjustId)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*HwDevicePaymentSourceModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*HwDevicePaymentSourceModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*HwDevicePaymentSourceModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*HwDevicePaymentSourceModel, error) {

	m := &HwDevicePaymentSourceModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*HwDevicePaymentSourceModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*HwDevicePaymentSourceModel, error) {

	modelList := make([]*HwDevicePaymentSourceModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &HwDevicePaymentSourceModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*HwDevicePaymentSourceModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*HwDevicePaymentSourceModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*HwDevicePaymentSourceModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*HwDevicePaymentSourceModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.TimeServer, d.CpGameId, d.GameId, d.PackageId, d.PackageName, d.AidOrIdfv, d.GidOrIdfa, d.Source, d.CampaignId, d.CampaignName, d.PlanId, d.PlanName, d.CreativeId, d.CreativeName, d.DeviceLanguage, d.CountryCode, d.Ip, d.Env, d.Ext, d.CoreAccount, d.OrderId, d.Money, d.PayTime, d.RawData, d.AdjustId))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "",
		},
		{
			"dataIndex": "time_server",
			"title":     "服务器接收时间",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "平台游戏ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号",
		},
		{
			"dataIndex": "package_name",
			"title":     "包名或BundleId",
		},
		{
			"dataIndex": "aid_or_idfv",
			"title":     "安卓ID or idfv",
		},
		{
			"dataIndex": "gid_or_idfa",
			"title":     "谷歌ID or 苹果ID",
		},
		{
			"dataIndex": "source",
			"title":     "媒体渠道",
		},
		{
			"dataIndex": "campaign_id",
			"title":     "广告组ID    CAMPAIGN_GROUP_ID（广告系列ID）",
		},
		{
			"dataIndex": "campaign_name",
			"title":     "广告组名称 CAMPAIGN_GROUP_NAME （广告系列名称）",
		},
		{
			"dataIndex": "plan_id",
			"title":     "计划ID       CAMPAIGN_ID （广告组ID ）",
		},
		{
			"dataIndex": "plan_name",
			"title":     "计划名       CAMPAIGN_NAME （广告组名称）",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID       AD_GROUP_ID（广告ID）",
		},
		{
			"dataIndex": "creative_name",
			"title":     "创意名       AD_GROUP_NAME （广告名）",
		},
		{
			"dataIndex": "device_language",
			"title":     "设备语言",
		},
		{
			"dataIndex": "country_code",
			"title":     "国家代码",
		},
		{
			"dataIndex": "ip",
			"title":     "IP",
		},
		{
			"dataIndex": "env",
			"title":     "环境",
		},
		{
			"dataIndex": "ext",
			"title":     "扩展字段",
		},
		{
			"dataIndex": "core_account",
			"title":     "登录账号",
		},
		{
			"dataIndex": "order_id",
			"title":     "我方订单号",
		},
		{
			"dataIndex": "money",
			"title":     "订单金额 ",
		},
		{
			"dataIndex": "pay_time",
			"title":     "订单支付时间",
		},
		{
			"dataIndex": "raw_data",
			"title":     "原始数据",
		},
		{
			"dataIndex": "adjust_id",
			"title":     "adjust_id",
		}}
}
