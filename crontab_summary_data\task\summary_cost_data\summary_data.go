package summary_cost_data

import (
	"910.com/plus2.git/plusQ"
	"encoding/json"
	"iaa_data/utils/item_helper"
	"iaa_data/utils/sql_helper"
	"log"
	"time"
)

var oldData = ""

func InitFunc(args ...string) {

	//汇总数据
	log.Println("==========", time.Now().Format(time.DateTime))
	nowTime := time.Now()
	nowTime = nowTime.Add(1 * 24 * time.Hour)

	startTime := nowTime.Add(-31 * 24 * time.Hour)

	sql := "select concat(cp_game_id,'cp_api_hw_iaa','iaa_cost', " +
		" DATE_SUB(`time` , INTERVAL 0 HOUR) " +
		" ,country_code,platform_id) as account_id,  " +
		" cp_game_id, " +
		" 'cp_api_hw_iaa' as category, " +
		" 'realtime' as event_access, " +
		" 'iaa_cost' as event_name, " +
		" DATE_SUB(`time` , INTERVAL 0 HOUR) as event_time, " +
		" country_code,platform_id, " +
		" sum(cost) as cost, " +
		" sum(`show`) as `show`, " +
		" sum(click) as click, sum(install) as install " +
		" from iaa_data.ad_cost  " +
		" where `time`  between '" + startTime.Format(time.DateOnly) + "' and '" + nowTime.Format(time.DateOnly) + "' " +
		" group by cp_game_id,country_code,platform_id,event_time " +
		" order by cp_game_id ,country_code ,platform_id ,event_time "

	list, err := plusQ.Db("data").List(sql)
	if err != nil {
		log.Println("========", err.Error())
		return
	}
	jsonStr, err2 := json.Marshal(list)
	if err2 == nil {
		if string(jsonStr) == oldData {
			log.Println("========存在相同")
			return
		}
	}
	log.Println("数据入库")
	oldData = string(jsonStr)

	for _, v := range list {
		newItem := item_helper.ChangeData(v)
		//sql_helper.InsertOrUpdateAllFiled("ad_cost_summary", "account_id", newItem)
		sql_helper.LogHelperInstance().HandleDataMapInsert("", "ad_cost_summary", "ad_cost_summary", newItem, 5, 10)
	}
	sql_helper.LogHelperInstance().HandleDataMapEnd("", "ad_cost_summary", "ad_cost_summary")
}
