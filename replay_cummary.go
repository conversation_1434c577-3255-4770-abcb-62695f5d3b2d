package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/provider"
	"iaa_data/crontab_summary_data/task/adjust_match"
	"iaa_data/crontab_summary_data/task/max_hour_report"
	"iaa_data/crontab_summary_data/task/max_ltv_report"
	"iaa_data/crontab_summary_data/task/max_user_report"
	"iaa_data/crontab_summary_data/task/summary_ad_position/summary_ad_position_helper"
	"iaa_data/crontab_summary_data/task/summary_ad_roi/summary_ad_roi_helper"
	"iaa_data/crontab_summary_data/task/summary_ad_user"
	"iaa_data/crontab_summary_data/task/summary_operation/summary_operation_helper"
	"iaa_data/utils/args_iaa"
	"iaa_data/utils/constant"
	"log"
	"time"
)

func main() {
	configFile := "config/config_hw.yaml"
	_, err := plus.LoadServerConfigDefault(configFile)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}
	plus.AppConfig.ConsoleLogFile = "./log_common/replay/demo.log"
	provider.Register() //注册服务

	start, end, err := args_iaa.GetReplayArgsDataOnly()
	if err != nil {
		log.Println(err.Error())
		return
	}
	dataType := args_iaa.GetArgs("type")
	
	timeZoneMap := []string{constant.TIME_BJ, constant.TIME_US, constant.TIME_ERU}

	if dataType == "" {
		for _, val := range timeZoneMap {
			summary_ad_user.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
			summary_operation_helper.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
			summary_ad_roi_helper.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
			summary_ad_position_helper.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
		}
		return
	}

	if dataType == "max_hour_report" {
		max_hour_report.Replay(start.Format(time.DateOnly), end.Format(time.DateOnly))
	} else if dataType == "max_ltv_report" {
		max_ltv_report.Replay()
	} else if dataType == "max_user_report" {
		max_user_report.Replay(start, end)
	} else if dataType == "adjust_match" {
		adjust_match.Replay(start, end)
	} else if dataType == "adjust_match_ad_user" {
		adjust_match.PlayAdUser(start, end)
	} else if dataType == "summary_ad_user" {
		for _, val := range timeZoneMap {
			summary_ad_user.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
		}
	} else if dataType == "summary_operation" {
		for _, val := range timeZoneMap {
			summary_operation_helper.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
		}
	} else if dataType == "summary_ad_roi" {
		for _, val := range timeZoneMap {
			summary_ad_roi_helper.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
		}
	} else if dataType == "summary_ad_position" {
		for _, val := range timeZoneMap {
			summary_ad_position_helper.SummaryReport(start.Format(time.DateOnly), end.Format(time.DateOnly), val)
		}
	}
}
