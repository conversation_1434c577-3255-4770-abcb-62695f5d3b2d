package game_helper

import (
	"iaa_data/model/platform/game"
	"iaa_data/model/platform/pkg"
	"iaa_data/model/platform/sdk_channel"
)

type GameHelper struct {
	GameMap        map[int]int
	PackageInfo    map[string]pkg.PkgModel
	PackageNameMap map[int]string
	ChannelMap     map[int]string
	ChannelIdMap   map[int]int
}

var gameHel *GameHelper

func GameHelInstant() *GameHelper {
	if gameHel == nil {
		gameHel = &GameHelper{}
	}
	return gameHel
}

func (g *GameHelper) GetCpGameId(gameId int) int {
	if g.GameMap == nil {
		g.GameMap = make(map[int]int)
	}

	cpGameId, ok := g.GameMap[gameId]
	if ok {
		return cpGameId
	}
	con := game.Id.Equal(gameId)
	info, err := game.GetBy(con)
	if err == nil {
		g.GameMap[gameId] = info.CpGameId
		return info.CpGameId
	}
	return 0
}

func (g *GameHelper) GetPackageInfo(packageName string) (int, int, int, error) {
	if g.PackageInfo == nil {
		g.PackageInfo = make(map[string]pkg.PkgModel)
	}

	packageInfo, ok := g.PackageInfo[packageName]
	if ok {
		return int(packageInfo.GameId), int(packageInfo.CpGameId), packageInfo.PackageId, nil
	}
	con := pkg.PackageName.Equal(packageName)
	info, err := pkg.GetBy(con)

	if err == nil {
		g.PackageInfo[packageName] = *info
		return int(info.GameId), int(info.CpGameId), info.PackageId, nil
	}
	return 0, 0, 0, err
}
func (g *GameHelper) GetPackageName(packageId int) string {
	if g.PackageNameMap == nil {
		g.PackageNameMap = make(map[int]string)
	}

	packageName, ok := g.PackageNameMap[packageId]
	if ok {
		return packageName
	}
	con := pkg.PackageId.Equal(packageId)
	info, err := pkg.GetBy(con)

	if err == nil {
		g.PackageNameMap[packageId] = info.PackageName
		return info.PackageName
	}
	return ""
}

func (g *GameHelper) GetChannelName(packageId int) string {
	if g.ChannelMap == nil {
		g.ChannelMap = make(map[int]string)
	}

	channelName, ok := g.ChannelMap[packageId]
	if ok {
		return channelName
	}
	con := pkg.PackageId.Equal(packageId)
	info, err := pkg.GetBy(con)
	if err == nil {
		con2 := sdk_channel.Id.Equal(info.ChannelId)
		channelInfo, err := sdk_channel.GetBy(con2)
		if err == nil {
			g.ChannelMap[packageId] = channelInfo.ChannelName
			return channelInfo.ChannelName
		}
	}
	return "未知"
}

func (g *GameHelper) GetChannelId(packageId int) int {
	if g.ChannelIdMap == nil {
		g.ChannelIdMap = make(map[int]int)
	}

	channelId, ok := g.ChannelIdMap[packageId]
	if ok {
		return channelId
	}
	con := pkg.PackageId.Equal(packageId)
	info, err := pkg.GetBy(con)
	if err == nil {
		con2 := sdk_channel.Id.Equal(info.ChannelId)
		channelInfo, err := sdk_channel.GetBy(con2)
		if err == nil {
			g.ChannelIdMap[packageId] = int(channelInfo.Id)
			return int(channelInfo.Id)
		}
	}
	return 0
}
