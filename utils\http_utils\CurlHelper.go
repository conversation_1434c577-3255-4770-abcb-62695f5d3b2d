package http_utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"log"
	"net/http"
)

type CommonResult struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"datahelper"`
}

func GetSuccessResult(data any) CommonResult {
	return CommonResult{
		Code:    0,
		Message: "OK",
		Data:    data,
	}
}

func HttpRequest(url string, method string, params interface{}, header map[string]string) (string, error) {
	postByte, err := json.Marshal(params)
	// 发送POST请求并设置请求头
	//log.Println(url)
	//log.Println(string(postByte))
	//hh, _ := json.Marshal(header)
	//log.Println(string(hh))
	var req *http.Request
	if method == "GET" {
		req, err = http.NewRequest(method, url, nil)
	} else {
		req, err = http.NewRequest(method, url, bytes.NewBuffer(postByte))
	}

	if err != nil {
		fmt.Println("创建请求失败:", err)
		return "", err
	}
	if header != nil {
		for key, value := range header {
			req.Header.Set(key, value)
		}
	}
	client := &http.Client{}
	response, err := client.Do(req)
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return "", err
	}
	defer response.Body.Close()
	// 处理响应
	//fmt.Println("响应状态码:", response.StatusCode)
	// 其他响应处理逻辑...
	if response.StatusCode == 200 {
		body, err := io.ReadAll(response.Body)
		if err != nil {
			return "", err
		}
		return string(body), nil
	} else {
		body, err := io.ReadAll(response.Body)
		if err == nil {
			log.Println("=err===========", string(body))
		}
	}
	return "", errors.New("获取失败code:" + fmt.Sprint(response.StatusCode))
}

func GetParamsUrl(params map[string]interface{}) string {
	paramsUrl := ""
	for key, value := range params {
		if paramsUrl == "" {
			paramsUrl = key + "=" + fmt.Sprint(value)
		} else {
			paramsUrl = paramsUrl + "&" + key + "=" + fmt.Sprint(value)
		}
	}
	return paramsUrl
}
