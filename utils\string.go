package utils

import (
	"log"
	"math/rand"
	"time"
)

func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

func ShuffleString(s string) string {
	runes := []rune(s)
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	r.Shuffle(len(runes), func(i, j int) {
		runes[i], runes[j] = runes[j], runes[i]
	})
	return string(runes)
}

func Debug(label string, data any) {
	log.Printf("%s：%#v", label, data)
}
