# IAA定时任务文档

- **文档创建日期**：2025-05-14
- **最后更新日期**：2025-05-14
- **文档版本**：1.0

## 1. 文档概述
本文档记录了IAA系统的汇总、匹配、api数据抓取 定时任务，包括任务描述及错误处理，以便团队理解和管理这些任务。


## 2. 系统环境信息

| 环境   | 服务器               | 操作系统           | 依赖服务           |
|--------|----------------------|--------------------|--------------------|
| 生产   | 43.166.137.126 | Linux (CentOS 7)   | MySQL、Redis、Golang |


## 3. 定时任务清单

| 任务名称             | 描述                     | 执行频率       | 手动重跑 | 重跑命令  |
|------------------|------------------------|-----------|--------|-------|
| 消耗汇总             | 消耗汇总 summary_cost_data | 每3分钟        | 不支持  | 参考3.1 |
| Max广告实时抓取（0.5小时） | 实时广告收入API抓取日志 max_hour_report  | 30分钟         | 支持   | 参考3.1 |
| Max用户层级数据抓取      | 每天下午四点后抓取前2天数据 max_user_report | 每天下午四点后       |  支持    |参考3.1 |
| Max广告LTV API抓取   | 每天下午四点后抓取前45天数据 max_ltv_report | 每天下午四点后    |   支持   |参考3.1 |
| adjust匹配         | 表匹配 进行归因  adjust_match  | 5分钟/1小时/1天     | 支持  |参考3.1 |
| 广告版位汇总           | 广告版位汇总  summary_ad_position | 8分钟/1天       | 支持  | 参考3.1 |
| 广告roi汇总          | 广告roi汇总  summary_ad_roi | 8分钟/1天        | 支持  |参考3.1 |
| 运营报表汇总           | 运营报表汇总 summary_operation | 8分钟/1天       | 支持  | 参考3.1 |
| 广告活跃用户汇总         | 广告活跃用户汇总  summary_ad_user | 8分钟/1天        | 支持  | 参考3.1 |

### 3.1重跑说明

#### 启动命令：/data/hw-data-go/rt_data_analyse_iaa/replay_summary start=年月日 end=年月日 type=xxx 
#### type参数：max_hour_report、max_user_report、max_ltv_report、adjust_match、summary_ad_position、summary_ad_roi、summary_operation、summary_ad_user


## 4. jenkins发布 api-prod-rt_data_analyse_iaa_hw
支持编译定时器和重跑程序
### 程序文件路径：/data/hw-data-go/rt_data_analyse_iaa     cd进去后可执行
### 定时器启动命令：./crontab_summary tag=xxx zone=时区
### 参数说明： tag（任务类型） 不传则跑所有任务
#### 参数说明：zone不填默认北京时区  US-美国时区  ERU-欧洲时区

####  /data/hw-data-go/rt_data_analyse_iaa/start.sh  一键启动所有汇总和广告抓取相关任务，启动前记得"pkill -f crontab_summary" 杀掉所有进程

#### tag=summary_cost_data  消耗  
#### tag=max_hour_report  max实时数据抓取
#### tag=max_ltv_report  max广告ltv抓取
#### tag=max_user_report  max广告用户层级抓取
#### tag=adjust_match  adjust匹配
#### tag=summary_ad_user  广告用户日收益汇总
#### tag=summary_operation  运营报表汇总
#### tag=summary_ad_roi  roi报表汇总
#### tag=summary_ad_position  广告版位看板汇总
多个任务可用“-”拼接  (如 ./crontab_summary tag=max_hour_report-max_ltv_report)

日志路径在对应的tag上: /data/hw-data-go/rt_data_analyse_iaa/log_common/tag/

## 5. max广告抓取说明

### Max广告实时抓取：https://developers.applovin.com/en/max/reporting-apis/revenue-reporting-api
### Max用户层级数据抓取：https://developers.applovin.com/en/max/reporting-apis/user-level-ad-revenue-api
### Max广告LTV API抓取 ：https://developers.applovin.com/en/max/reporting-apis/cohort-api 