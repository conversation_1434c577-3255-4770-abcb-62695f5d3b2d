package country_helper

import (
	"iaa_data/model/platform/hw_base_country_conf"
	"log"
)

type CountryHelper struct {
	CountryMap map[string]int
}

var countryInstance *CountryHelper

func CountryInstant() *CountryHelper {
	if countryInstance == nil {
		countryInstance = &CountryHelper{}
	}
	return countryInstance
}

func (s *CountryHelper) GetCountryId(cuntryName string) int {
	if s.CountryMap == nil {
		s.CountryMap = make(map[string]int)
		list, err := hw_base_country_conf.ListAll()
		if err != nil {
			log.Println("hw_base_country_conf", err)
			return 0
		}
		for _, v := range list {
			s.CountryMap[v.Abbreviate] = int(v.Id)
		}
	}

	id, ok := s.CountryMap[cuntryName]
	if ok {
		return id
	}
	return 0
}
func (s *CountryHelper) GetCountry(cuntry_id int) string {
	if s.CountryMap == nil {
		s.CountryMap = make(map[string]int)
		list, err := hw_base_country_conf.ListAll()
		if err != nil {
			log.Println("hw_base_country_conf", err)
			return ""
		}
		for _, v := range list {
			s.CountryMap[v.Abbreviate] = int(v.Id)
		}
	}
	if s.CountryMap != nil {
		for cuntry, id := range s.CountryMap {
			if cuntry_id == id {
				return cuntry
			}
		}
	}
	return ""
}
