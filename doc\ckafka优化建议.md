# CKafka 连接优化建议

## 问题分析

根据错误日志分析：
```
GroupCoordinator: ***************:50000: Connection setup timed out in state AUTH_HANDSHAKE (after 18016ms in state AUTH_HANDSHAKE)
GroupCoordinator: ***************:50000: SASL PLAIN mechanism handshake failed: Local: Broker transport failure: broker's supported mechanisms: (n/a) (after 0ms in state DOWN)
```

主要问题：
1. **连接超时**: 在 AUTH_HANDSHAKE 状态下连接超时
2. **SASL 认证失败**: SASL PLAIN 机制握手失败
3. **网络连接问题**: Broker 传输失败

## 代码优化改进

### 1. 连接配置优化
- **增加超时时间**: 从 10s 增加到 30s
- **优化重连配置**: 添加指数退避重连机制
- **网络优化**: 启用 keepalive 和禁用 Nagle 算法

### 2. 新增功能

#### 健康检查机制
```go
func (this *CKafkaMQ) HealthCheck() bool {
    // 线程安全的健康状态检查
}
```

#### 自动重连机制
```go
func (this *CKafkaMQ) reconnect() error {
    // 智能重连，支持指数退避
    // 最大重试次数: 10次
    // 重连间隔: 5s -> 7.5s -> 11.25s -> ... -> 60s (最大)
}
```

#### 错误分类处理
- **超时错误**: 不计入连续错误
- **网络错误**: 等待更长时间重连
- **连续错误监控**: 5次连续错误后标记为不健康

### 3. 配置参数优化

#### 消费者配置
| 参数 | 原值 | 新值 | 说明 |
|------|------|------|------|
| session.timeout.ms | 10000 | 30000 | 增加会话超时时间 |
| heartbeat.interval.ms | - | 10000 | 添加心跳间隔 |
| socket.timeout.ms | - | 60000 | 增加 socket 超时 |
| reconnect.backoff.max.ms | - | 10000 | 最大重连退避时间 |

#### 生产者配置
| 参数 | 原值 | 新值 | 说明 |
|------|------|------|------|
| retries | 3 | 10 | 增加重试次数 |
| socket.timeout.ms | 6000 | 60000 | 增加超时时间 |
| request.timeout.ms | - | 30000 | 添加请求超时 |

## 运维建议

### 1. 网络检查
```bash
# 检查网络连通性
telnet *************** 50000

# 检查 DNS 解析
nslookup ***************

# 检查防火墙规则
iptables -L | grep 50000
```

### 2. 监控告警
- 监控连续错误次数
- 监控重连频率
- 设置连接超时告警

### 3. 配置文件建议
```yaml
ckafka:
  bootstrapServers: "your-kafka-server:9092"
  sasl:
    instanceId: "your-instance-id"
    username: "your-username" 
    password: "your-password"
  consumerGroupId: "your-group-id"
  topics: ["your-topic-1", "your-topic-2"]
  
  # 可选的高级配置
  advanced:
    sessionTimeoutMs: 30000
    requestTimeoutMs: 30000
    socketTimeoutMs: 60000
    maxReconnectRetry: 10
    reconnectIntervalMs: 5000
```

### 4. 故障排查步骤
1. **检查网络连通性**: `telnet broker_ip port`
2. **验证认证信息**: 确认 instanceId、username、password 正确
3. **检查 ACL 权限**: 确认用户有对应 topic 的读写权限
4. **查看 Kafka 服务端日志**: 检查 broker 端错误
5. **调整超时配置**: 根据网络环境调整超时参数

## 性能优化建议

### 1. 批处理优化
- `batch.size`: 16KB 批次大小
- `linger.ms`: 5ms 延迟发送
- `buffer.memory`: 32MB 缓冲区

### 2. 并发控制
- 当前设置: 最大 10 个并发 worker
- 可根据服务器性能调整

### 3. 监控指标
- 消息处理延迟
- 连接重连次数
- 错误率和成功率
- 内存和 CPU 使用率 