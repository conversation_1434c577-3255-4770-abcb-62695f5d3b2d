package ad_cost_summary

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_cost_summary")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// AccountId 数数唯一用户ID
	AccountId = property.Property("account_id")
	// Category 分类标识
	Category = property.Property("category")
	// Click 点击数
	Click = property.Property("click")
	// CpGameId 游戏原名id
	CpGameId = property.Property("cp_game_id")
	// CountryCode 国家
	CountryCode = property.Property("country_code")
	// Cost 费用
	Cost = property.Property("cost")
	// EventAccess 访问事件
	EventAccess = property.Property("event_access")
	// EventName 事件名
	EventName = property.Property("event_name")
	// EventTime 事件时间
	EventTime = property.Property("event_time")
	// Install 安装数
	Install = property.Property("install")
	// PlatformId 1安卓2苹果
	PlatformId = property.Property("platform_id")
	// Show 曝光数
	Show = property.Property("show")
)

var FieldsAll = Fields(Id, AccountId, Category, Click, CpGameId, CountryCode, Cost, EventAccess, EventName, EventTime, Install, PlatformId, Show)
var NonePrimaryFields = Fields(AccountId, Category, Click, CpGameId, CountryCode, Cost, EventAccess, EventName, EventTime, Install, PlatformId, Show)
var NoneAutoIncrementFields = Fields(AccountId, Category, Click, CpGameId, CountryCode, Cost, EventAccess, EventName, EventTime, Install, PlatformId, Show)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdCostSummaryModel 广告消耗汇总表
type AdCostSummaryModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// AccountId 数数唯一用户ID
	AccountId string `orm:"account_id" json:"account_id"`

	// Category 分类标识
	Category string `orm:"category" json:"category"`

	// Click 点击数
	Click int `orm:"click" json:"click"`

	// CpGameId 游戏原名id
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// CountryCode 国家
	CountryCode string `orm:"country_code" json:"country_code"`

	// Cost 费用
	Cost float32 `orm:"cost" json:"cost"`

	// EventAccess 访问事件
	EventAccess string `orm:"event_access" json:"event_access"`

	// EventName 事件名
	EventName string `orm:"event_name" json:"event_name"`

	// EventTime 事件时间
	EventTime CustomTime `orm:"event_time" json:"event_time"`

	// Install 安装数
	Install uint32 `orm:"install" json:"install"`

	// PlatformId 1安卓2苹果
	PlatformId int `orm:"platform_id" json:"platform_id"`

	// Show 曝光数
	Show uint32 `orm:"show" json:"show"`
}

type PagedResult struct {
	Records      []*AdCostSummaryModel `json:"list"`
	PageNum      int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
	TotalPages   int                   `json:"total_pages"`
	TotalRecords int                   `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:          1,
	AccountId:   2,
	Category:    3,
	Click:       4,
	CpGameId:    5,
	CountryCode: 6,
	Cost:        7,
	EventAccess: 8,
	EventName:   9,
	EventTime:   10,
	Install:     11,
	PlatformId:  12,
	Show:        13,
}

func (m *AdCostSummaryModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdCostSummaryModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.AccountId, m.Category, m.Click, m.CpGameId, m.CountryCode, m.Cost, m.EventAccess, m.EventName, m.EventTime, m.Install, m.PlatformId, m.Show)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdCostSummaryModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AccountId, m.Category, m.Click, m.CpGameId, m.CountryCode, m.Cost, m.EventAccess, m.EventName, m.EventTime, m.Install, m.PlatformId, m.Show)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdCostSummaryModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdCostSummaryModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.AccountId, m.Category, m.Click, m.CpGameId, m.CountryCode, m.Cost, m.EventAccess, m.EventName, m.EventTime, m.Install, m.PlatformId, m.Show)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostSummaryModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdCostSummaryModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AccountId, m.Category, m.Click, m.CpGameId, m.CountryCode, m.Cost, m.EventAccess, m.EventName, m.EventTime, m.Install, m.PlatformId, m.Show), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostSummaryModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdCostSummaryModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.AccountId, m.Category, m.Click, m.CpGameId, m.CountryCode, m.Cost, m.EventAccess, m.EventName, m.EventTime, m.Install, m.PlatformId, m.Show), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.AccountId, m.Category, m.Click, m.CpGameId, m.CountryCode, m.Cost, m.EventAccess, m.EventName, m.EventTime, m.Install, m.PlatformId, m.Show))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdCostSummaryModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdCostSummaryModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdCostSummaryModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdCostSummaryModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdCostSummaryModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdCostSummaryModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdCostSummaryModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdCostSummaryModel, error) {

	modelList := make([]*AdCostSummaryModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCostSummaryModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AccountId, &m.Category, &m.Click, &m.CpGameId, &m.CountryCode, &m.Cost, &m.EventAccess, &m.EventName, &m.EventTime, &m.Install, &m.PlatformId, &m.Show)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdCostSummaryModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdCostSummaryModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdCostSummaryModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdCostSummaryModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdCostSummaryModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdCostSummaryModel, error) {

	m := &AdCostSummaryModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.AccountId, &m.Category, &m.Click, &m.CpGameId, &m.CountryCode, &m.Cost, &m.EventAccess, &m.EventName, &m.EventTime, &m.Install, &m.PlatformId, &m.Show)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdCostSummaryModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdCostSummaryModel, error) {

	modelList := make([]*AdCostSummaryModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdCostSummaryModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AccountId, &m.Category, &m.Click, &m.CpGameId, &m.CountryCode, &m.Cost, &m.EventAccess, &m.EventName, &m.EventTime, &m.Install, &m.PlatformId, &m.Show)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdCostSummaryModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdCostSummaryModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdCostSummaryModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.AccountId, &m.Category, &m.Click, &m.CpGameId, &m.CountryCode, &m.Cost, &m.EventAccess, &m.EventName, &m.EventTime, &m.Install, &m.PlatformId, &m.Show)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdCostSummaryModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdCostSummaryModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdCostSummaryModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdCostSummaryModel, error) {

	m := &AdCostSummaryModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdCostSummaryModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdCostSummaryModel, error) {

	modelList := make([]*AdCostSummaryModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdCostSummaryModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdCostSummaryModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdCostSummaryModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdCostSummaryModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdCostSummaryModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.AccountId, d.Category, d.Click, d.CpGameId, d.CountryCode, d.Cost, d.EventAccess, d.EventName, d.EventTime, d.Install, d.PlatformId, d.Show))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "account_id",
			"title":     "数数唯一用户ID",
		},
		{
			"dataIndex": "category",
			"title":     "分类标识",
		},
		{
			"dataIndex": "click",
			"title":     "点击数",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名id",
		},
		{
			"dataIndex": "country_code",
			"title":     "国家",
		},
		{
			"dataIndex": "cost",
			"title":     "费用",
		},
		{
			"dataIndex": "event_access",
			"title":     "访问事件",
		},
		{
			"dataIndex": "event_name",
			"title":     "事件名",
		},
		{
			"dataIndex": "event_time",
			"title":     "事件时间",
		},
		{
			"dataIndex": "install",
			"title":     "安装数",
		},
		{
			"dataIndex": "platform_id",
			"title":     "1安卓2苹果",
		},
		{
			"dataIndex": "show",
			"title":     "曝光数",
		}}
}
