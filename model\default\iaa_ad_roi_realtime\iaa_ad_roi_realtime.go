package iaa_ad_roi_realtime

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("default")
	Table    = orm.Table("iaa_ad_roi_realtime")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 主键
	Id = property.Property("id")
	// Tday 日期
	Tday = property.Property("tday")
	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone = property.Property("time_zone")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏ID
	GameId = property.Property("game_id")
	// PackageId 包号
	PackageId = property.Property("package_id")
	// CountryId 国家
	CountryId = property.Property("country_id")
	// CountryLevel 国家等级
	CountryLevel = property.Property("country_level")
	// SourceId 媒体ID
	SourceId = property.Property("source_id")
	// ChannelId 渠道ID
	ChannelId = property.Property("channel_id")
	// Platform 客户端android、ios
	Platform = property.Property("platform")
	// CreativeId 创意ID
	CreativeId = property.Property("creative_id")
	// AdType 广告类型
	AdType = property.Property("ad_type")
	// CostSun 返点前消耗金额
	CostSun = property.Property("cost_sun")
	// CostDiscountSun 返点后消耗金额
	CostDiscountSun = property.Property("cost_discount_sun")
	// ShowNum 曝光数
	ShowNum = property.Property("show_num")
	// ClickNum 点击数
	ClickNum = property.Property("click_num")
	// InstallNum 安装数
	InstallNum = property.Property("install_num")
	// Ecpm 广告ecpm
	Ecpm = property.Property("ecpm")
	// RealRevenue 广告实际收入
	RealRevenue = property.Property("real_revenue")
	// EstimateRevenue 广告预估收入
	EstimateRevenue = property.Property("estimate_revenue")
	// Revenue 累计广告收入
	Revenue = property.Property("revenue")
	// IapPayMoney IAP付费金额
	IapPayMoney = property.Property("iap_pay_money")
	// NewUserNum 新增用户
	NewUserNum = property.Property("new_user_num")
	// NewUserPayNum 新增付费用户
	NewUserPayNum = property.Property("new_user_pay_num")
	// NewUserPaySum 新增付费金额
	NewUserPaySum = property.Property("new_user_pay_sum")
	// DeviceActiveNum 设备激活数量
	DeviceActiveNum = property.Property("device_active_num")
	// UpdateTime 更新时间
	UpdateTime = property.Property("update_time")
)

var FieldsAll = Fields(Id, Tday, TimeZone, CpGameId, GameId, PackageId, CountryId, CountryLevel, SourceId, ChannelId, Platform, CreativeId, AdType, CostSun, CostDiscountSun, ShowNum, ClickNum, InstallNum, Ecpm, RealRevenue, EstimateRevenue, Revenue, IapPayMoney, NewUserNum, NewUserPayNum, NewUserPaySum, DeviceActiveNum, UpdateTime)
var NonePrimaryFields = Fields(Tday, TimeZone, CpGameId, GameId, PackageId, CountryId, CountryLevel, SourceId, ChannelId, Platform, CreativeId, AdType, CostSun, CostDiscountSun, ShowNum, ClickNum, InstallNum, Ecpm, RealRevenue, EstimateRevenue, Revenue, IapPayMoney, NewUserNum, NewUserPayNum, NewUserPaySum, DeviceActiveNum, UpdateTime)
var NoneAutoIncrementFields = Fields(Tday, TimeZone, CpGameId, GameId, PackageId, CountryId, CountryLevel, SourceId, ChannelId, Platform, CreativeId, AdType, CostSun, CostDiscountSun, ShowNum, ClickNum, InstallNum, Ecpm, RealRevenue, EstimateRevenue, Revenue, IapPayMoney, NewUserNum, NewUserPayNum, NewUserPaySum, DeviceActiveNum, UpdateTime)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// IaaAdRoiRealtimeModel 海外广告投产表
type IaaAdRoiRealtimeModel struct {
	// Id 主键
	Id uint64 `orm:"id" json:"id"`

	// Tday 日期
	Tday int `orm:"tday" json:"tday"`

	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone int `orm:"time_zone" json:"time_zone"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏ID
	GameId int `orm:"game_id" json:"game_id"`

	// PackageId 包号
	PackageId int `orm:"package_id" json:"package_id"`

	// CountryId 国家
	CountryId int `orm:"country_id" json:"country_id"`

	// CountryLevel 国家等级
	CountryLevel int `orm:"country_level" json:"country_level"`

	// SourceId 媒体ID
	SourceId int `orm:"source_id" json:"source_id"`

	// ChannelId 渠道ID
	ChannelId int `orm:"channel_id" json:"channel_id"`

	// Platform 客户端android、ios
	Platform string `orm:"platform" json:"platform"`

	// CreativeId 创意ID
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// AdType 广告类型
	AdType string `orm:"ad_type" json:"ad_type"`

	// CostSun 返点前消耗金额
	CostSun float32 `orm:"cost_sun" json:"cost_sun"`

	// CostDiscountSun 返点后消耗金额
	CostDiscountSun float32 `orm:"cost_discount_sun" json:"cost_discount_sun"`

	// ShowNum 曝光数
	ShowNum int `orm:"show_num" json:"show_num"`

	// ClickNum 点击数
	ClickNum int `orm:"click_num" json:"click_num"`

	// InstallNum 安装数
	InstallNum int `orm:"install_num" json:"install_num"`

	// Ecpm 广告ecpm
	Ecpm float32 `orm:"ecpm" json:"ecpm"`

	// RealRevenue 广告实际收入
	RealRevenue float32 `orm:"real_revenue" json:"real_revenue"`

	// EstimateRevenue 广告预估收入
	EstimateRevenue float32 `orm:"estimate_revenue" json:"estimate_revenue"`

	// Revenue 累计广告收入
	Revenue float32 `orm:"revenue" json:"revenue"`

	// IapPayMoney IAP付费金额
	IapPayMoney float32 `orm:"iap_pay_money" json:"iap_pay_money"`

	// NewUserNum 新增用户
	NewUserNum int `orm:"new_user_num" json:"new_user_num"`

	// NewUserPayNum 新增付费用户
	NewUserPayNum int `orm:"new_user_pay_num" json:"new_user_pay_num"`

	// NewUserPaySum 新增付费金额
	NewUserPaySum float32 `orm:"new_user_pay_sum" json:"new_user_pay_sum"`

	// DeviceActiveNum 设备激活数量
	DeviceActiveNum float32 `orm:"device_active_num" json:"device_active_num"`

	// UpdateTime 更新时间
	UpdateTime CustomTime `orm:"update_time" json:"update_time"`
}

type PagedResult struct {
	Records      []*IaaAdRoiRealtimeModel `json:"list"`
	PageNum      int                      `json:"page"`
	PageSize     int                      `json:"page_size"`
	TotalPages   int                      `json:"total_pages"`
	TotalRecords int                      `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:              1,
	Tday:            2,
	TimeZone:        3,
	CpGameId:        4,
	GameId:          5,
	PackageId:       6,
	CountryId:       7,
	CountryLevel:    8,
	SourceId:        9,
	ChannelId:       10,
	Platform:        11,
	CreativeId:      12,
	AdType:          13,
	CostSun:         14,
	CostDiscountSun: 15,
	ShowNum:         16,
	ClickNum:        17,
	InstallNum:      18,
	Ecpm:            19,
	RealRevenue:     20,
	EstimateRevenue: 21,
	Revenue:         22,
	IapPayMoney:     23,
	NewUserNum:      24,
	NewUserPayNum:   25,
	NewUserPaySum:   26,
	DeviceActiveNum: 27,
	UpdateTime:      28,
}

func (m *IaaAdRoiRealtimeModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *IaaAdRoiRealtimeModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.CountryId, m.CountryLevel, m.SourceId, m.ChannelId, m.Platform, m.CreativeId, m.AdType, m.CostSun, m.CostDiscountSun, m.ShowNum, m.ClickNum, m.InstallNum, m.Ecpm, m.RealRevenue, m.EstimateRevenue, m.Revenue, m.IapPayMoney, m.NewUserNum, m.NewUserPayNum, m.NewUserPaySum, m.DeviceActiveNum, m.UpdateTime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaAdRoiRealtimeModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.CountryId, m.CountryLevel, m.SourceId, m.ChannelId, m.Platform, m.CreativeId, m.AdType, m.CostSun, m.CostDiscountSun, m.ShowNum, m.ClickNum, m.InstallNum, m.Ecpm, m.RealRevenue, m.EstimateRevenue, m.Revenue, m.IapPayMoney, m.NewUserNum, m.NewUserPayNum, m.NewUserPaySum, m.DeviceActiveNum, m.UpdateTime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaAdRoiRealtimeModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *IaaAdRoiRealtimeModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.CountryId, m.CountryLevel, m.SourceId, m.ChannelId, m.Platform, m.CreativeId, m.AdType, m.CostSun, m.CostDiscountSun, m.ShowNum, m.ClickNum, m.InstallNum, m.Ecpm, m.RealRevenue, m.EstimateRevenue, m.Revenue, m.IapPayMoney, m.NewUserNum, m.NewUserPayNum, m.NewUserPaySum, m.DeviceActiveNum, m.UpdateTime)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaAdRoiRealtimeModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *IaaAdRoiRealtimeModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.CountryId, m.CountryLevel, m.SourceId, m.ChannelId, m.Platform, m.CreativeId, m.AdType, m.CostSun, m.CostDiscountSun, m.ShowNum, m.ClickNum, m.InstallNum, m.Ecpm, m.RealRevenue, m.EstimateRevenue, m.Revenue, m.IapPayMoney, m.NewUserNum, m.NewUserPayNum, m.NewUserPaySum, m.DeviceActiveNum, m.UpdateTime), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaAdRoiRealtimeModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *IaaAdRoiRealtimeModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.CountryId, m.CountryLevel, m.SourceId, m.ChannelId, m.Platform, m.CreativeId, m.AdType, m.CostSun, m.CostDiscountSun, m.ShowNum, m.ClickNum, m.InstallNum, m.Ecpm, m.RealRevenue, m.EstimateRevenue, m.Revenue, m.IapPayMoney, m.NewUserNum, m.NewUserPayNum, m.NewUserPaySum, m.DeviceActiveNum, m.UpdateTime), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.TimeZone, m.CpGameId, m.GameId, m.PackageId, m.CountryId, m.CountryLevel, m.SourceId, m.ChannelId, m.Platform, m.CreativeId, m.AdType, m.CostSun, m.CostDiscountSun, m.ShowNum, m.ClickNum, m.InstallNum, m.Ecpm, m.RealRevenue, m.EstimateRevenue, m.Revenue, m.IapPayMoney, m.NewUserNum, m.NewUserPayNum, m.NewUserPaySum, m.DeviceActiveNum, m.UpdateTime))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaAdRoiRealtimeModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *IaaAdRoiRealtimeModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *IaaAdRoiRealtimeModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *IaaAdRoiRealtimeModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *IaaAdRoiRealtimeModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *IaaAdRoiRealtimeModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*IaaAdRoiRealtimeModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*IaaAdRoiRealtimeModel, error) {

	modelList := make([]*IaaAdRoiRealtimeModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaAdRoiRealtimeModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.CountryId, &m.CountryLevel, &m.SourceId, &m.ChannelId, &m.Platform, &m.CreativeId, &m.AdType, &m.CostSun, &m.CostDiscountSun, &m.ShowNum, &m.ClickNum, &m.InstallNum, &m.Ecpm, &m.RealRevenue, &m.EstimateRevenue, &m.Revenue, &m.IapPayMoney, &m.NewUserNum, &m.NewUserPayNum, &m.NewUserPaySum, &m.DeviceActiveNum, &m.UpdateTime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint64) (*IaaAdRoiRealtimeModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint64) (*IaaAdRoiRealtimeModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint64) (*IaaAdRoiRealtimeModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint64) (*IaaAdRoiRealtimeModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*IaaAdRoiRealtimeModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*IaaAdRoiRealtimeModel, error) {

	m := &IaaAdRoiRealtimeModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.CountryId, &m.CountryLevel, &m.SourceId, &m.ChannelId, &m.Platform, &m.CreativeId, &m.AdType, &m.CostSun, &m.CostDiscountSun, &m.ShowNum, &m.ClickNum, &m.InstallNum, &m.Ecpm, &m.RealRevenue, &m.EstimateRevenue, &m.Revenue, &m.IapPayMoney, &m.NewUserNum, &m.NewUserPayNum, &m.NewUserPaySum, &m.DeviceActiveNum, &m.UpdateTime)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*IaaAdRoiRealtimeModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*IaaAdRoiRealtimeModel, error) {

	modelList := make([]*IaaAdRoiRealtimeModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaAdRoiRealtimeModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.CountryId, &m.CountryLevel, &m.SourceId, &m.ChannelId, &m.Platform, &m.CreativeId, &m.AdType, &m.CostSun, &m.CostDiscountSun, &m.ShowNum, &m.ClickNum, &m.InstallNum, &m.Ecpm, &m.RealRevenue, &m.EstimateRevenue, &m.Revenue, &m.IapPayMoney, &m.NewUserNum, &m.NewUserPayNum, &m.NewUserPaySum, &m.DeviceActiveNum, &m.UpdateTime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*IaaAdRoiRealtimeModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*IaaAdRoiRealtimeModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &IaaAdRoiRealtimeModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.TimeZone, &m.CpGameId, &m.GameId, &m.PackageId, &m.CountryId, &m.CountryLevel, &m.SourceId, &m.ChannelId, &m.Platform, &m.CreativeId, &m.AdType, &m.CostSun, &m.CostDiscountSun, &m.ShowNum, &m.ClickNum, &m.InstallNum, &m.Ecpm, &m.RealRevenue, &m.EstimateRevenue, &m.Revenue, &m.IapPayMoney, &m.NewUserNum, &m.NewUserPayNum, &m.NewUserPaySum, &m.DeviceActiveNum, &m.UpdateTime)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint64) (*IaaAdRoiRealtimeModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint64) (*IaaAdRoiRealtimeModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*IaaAdRoiRealtimeModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*IaaAdRoiRealtimeModel, error) {

	m := &IaaAdRoiRealtimeModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*IaaAdRoiRealtimeModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*IaaAdRoiRealtimeModel, error) {

	modelList := make([]*IaaAdRoiRealtimeModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &IaaAdRoiRealtimeModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*IaaAdRoiRealtimeModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*IaaAdRoiRealtimeModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*IaaAdRoiRealtimeModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*IaaAdRoiRealtimeModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.Tday, d.TimeZone, d.CpGameId, d.GameId, d.PackageId, d.CountryId, d.CountryLevel, d.SourceId, d.ChannelId, d.Platform, d.CreativeId, d.AdType, d.CostSun, d.CostDiscountSun, d.ShowNum, d.ClickNum, d.InstallNum, d.Ecpm, d.RealRevenue, d.EstimateRevenue, d.Revenue, d.IapPayMoney, d.NewUserNum, d.NewUserPayNum, d.NewUserPaySum, d.DeviceActiveNum, d.UpdateTime))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "主键",
		},
		{
			"dataIndex": "tday",
			"title":     "日期",
		},
		{
			"dataIndex": "time_zone",
			"title":     "时区: 1=北京，2=欧洲，3=美国",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号",
		},
		{
			"dataIndex": "country_id",
			"title":     "国家",
		},
		{
			"dataIndex": "country_level",
			"title":     "国家等级",
		},
		{
			"dataIndex": "source_id",
			"title":     "媒体ID",
		},
		{
			"dataIndex": "channel_id",
			"title":     "渠道ID",
		},
		{
			"dataIndex": "platform",
			"title":     "客户端android、ios",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID",
		},
		{
			"dataIndex": "ad_type",
			"title":     "广告类型",
		},
		{
			"dataIndex": "cost_sun",
			"title":     "返点前消耗金额",
		},
		{
			"dataIndex": "cost_discount_sun",
			"title":     "返点后消耗金额",
		},
		{
			"dataIndex": "show_num",
			"title":     "曝光数",
		},
		{
			"dataIndex": "click_num",
			"title":     "点击数",
		},
		{
			"dataIndex": "install_num",
			"title":     "安装数",
		},
		{
			"dataIndex": "ecpm",
			"title":     "广告ecpm",
		},
		{
			"dataIndex": "real_revenue",
			"title":     "广告实际收入",
		},
		{
			"dataIndex": "estimate_revenue",
			"title":     "广告预估收入",
		},
		{
			"dataIndex": "revenue",
			"title":     "累计广告收入",
		},
		{
			"dataIndex": "iap_pay_money",
			"title":     "IAP付费金额",
		},
		{
			"dataIndex": "new_user_num",
			"title":     "新增用户",
		},
		{
			"dataIndex": "new_user_pay_num",
			"title":     "新增付费用户",
		},
		{
			"dataIndex": "new_user_pay_sum",
			"title":     "新增付费金额",
		},
		{
			"dataIndex": "device_active_num",
			"title":     "设备激活数量",
		},
		{
			"dataIndex": "update_time",
			"title":     "更新时间",
		}}
}
