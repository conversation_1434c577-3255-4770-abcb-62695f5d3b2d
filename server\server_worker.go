package server

import (
	"context"
	"log"
	"time"

	"910.com/plus2.git/contract"
	"910.com/plus2.git/plusQ"
)

type ServerWorker struct {
	ctx            context.Context          // 上下文
	cancelFunc     context.CancelFunc       // 取消函数
	workerServices []contract.WorkerService // 应用服务列表
	worker         contract.Worker          // 后台任务处理器
}

// NewWorkerServer 创建一个新的后台任务实例
func NewWorkerServer(parentCtx context.Context, services ...contract.WorkerService) *ServerWorker {
	ctx, cancel := context.WithCancel(parentCtx)

	s := &ServerWorker{
		ctx:            ctx,
		cancelFunc:     cancel,
		workerServices: services,
		worker:         plusQ.Worker(),
	}
	s.worker.AddContext(s.ctx)
	for _, svc := range s.workerServices {
		err := svc.InitHandler(s.worker)
		if err != nil {
			log.Println(err)
		}
	}
	return s
}

// Start 启动后台任务
func (s *ServerWorker) Start(jobName ...*string) error {
	if len(jobName) > 0 && *jobName[0] != "" {
		if *jobName[0] == "list" {
			jobs := s.worker.List()
			log.Println("后台任务列表：")
			// 打印所有任务
			for _, job := range jobs {
				log.Println(job)
			}
			return nil
		}
		log.Println("手动执行后台任务：", *jobName[0])
		return s.worker.RunJob(*jobName[0])
	}
	s.worker.Start()
	log.Println("后台任务启动")
	<-s.ctx.Done() // 阻塞程序，直到收到系统信号
	return nil
}

// Shutdown 关闭后台任务
func (s *ServerWorker) Shutdown() error {
	var timeout time.Duration
	timeout = 60 * time.Second
	log.Printf("关闭后台任务最多等待%f秒", timeout.Seconds())
	s.cancelFunc()
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	s.worker.StopWithContext(ctx)
	return nil
}
