# IAA定时任务文档

- **文档创建日期**：2025-05-12
- **最后更新日期**：2025-05-12
- **文档版本**：1.0

## 1. 文档概述
本文档记录了IAA系统的所有定时任务，包括任务描述及错误处理，以便团队理解和管理这些任务。


## 2. 系统环境信息

| 环境   | 服务器               | 操作系统           | 依赖服务           |
|--------|----------------------|--------------------|--------------------|
| 生产   | 43.166.137.126 | Linux (CentOS 7)   | MySQL、Redis、Golang |


## 3. 定时任务清单

| 任务名称         | 描述                 | 执行频率       | 预计执行时长 |手动重跑|重跑命令|
|------------------|----------------------|----------------|--------------|--------------|--------------|
| Meta广告消耗抓取（每日）     | Meta广告消耗抓取 | 每4小时        | 1分钟         |支持|/www/iaa_data/iaa_data_cli -op facebook_grab -start_date 开始时间 -end_date 结束时间|
| Meta广告消耗抓取（每小时）     |  Meta广告消耗抓取 | 每小时        | 3分钟         |支持|/www/iaa_data/iaa_data_cli -op facebook_grab_hour -start_date 开始时间 -end_date 结束时间|
| 谷歌广告消耗抓取（每日）     | 谷歌广告消耗抓取 | 每4小时        | 1分钟         |支持|/www/iaa_data/iaa_data_cli -op google_grab -start_date 开始时间 -end_date 结束时间|
| Tiktok广告消耗抓取（每日）     | Tiktok广告消耗抓取 | 每4小时        | 1分钟         |支持|/www/iaa_data/iaa_data_cli -op tiktok_grab -start_date 开始时间 -end_date 结束时间|
| Tiktok广告消耗抓取（每小时）     |  Tiktok广告消耗抓取 | 每小时        | 3分钟         |支持|/www/iaa_data/iaa_data_cli -op tiktok_grab_hour -start_date 开始时间 -end_date 结束时间|
| Applovin广告消耗抓取（每日）     | Applovin广告消耗抓取 | 每4小时        | 1分钟         |支持|/www/iaa_data/iaa_data_cli -op applovin_grab -start_date 开始时间 -end_date 结束时间|
| Applovin广告消耗抓取（每小时）     |  Applovin广告消耗抓取 | 每小时        | 3分钟         |支持|/www/iaa_data/iaa_data_cli -op applovin_grab_hour -start_date 开始时间 -end_date 结束时间|

## 4. 详细任务说明

### Meta广告消耗抓取（每日）

#### 功能描述
抓取所有Meta平台投放账号的广告消耗数据

#### 执行流程
1. 请求meta广告api接口抓取消耗数据
2. 解释计划名称，匹配游戏ID、系统信息、国家地区信息
3. 相关数据纬度计算
4. 去重入库保存

#### 输入/输出
- **输入**：iaa_data.ad_account_conf 表 channel_id=1的媒体账号
- **输出**：iaa_data.ad_cost 消耗记录

#### 错误处理
- 记录信息到相关日志 `./elk_log/facebook_grab_day_err/xx/xx/xx`

#### 依赖关系
- 无

### Meta广告消耗抓取（每小时）

#### 功能描述
抓取所有Meta平台投放账号的广告消耗数据

#### 执行流程
1. 请求meta广告api接口抓取消耗数据
2. 解释计划名称，匹配游戏ID、系统信息、国家地区信息
3. 相关数据纬度计算
4. 去重入库保存

#### 输入/输出
- **输入**：iaa_data.ad_account_conf 表 channel_id=1的媒体账号
- **输出**：iaa_data.ad_cost 消耗记录

#### 错误处理
- 记录信息到相关日志 `./elk_log/facebook_grab_hour_err/xx/xx/xx`

#### 依赖关系
- 无

### 谷歌广告消耗抓取（每日）

#### 功能描述
抓取所有谷歌平台投放账号的广告消耗数据

#### 执行流程
1. 请求谷歌广告api接口抓取消耗数据
2. 解释计划名称，匹配游戏ID、系统信息、国家地区信息
3. 相关数据纬度计算
4. 去重入库保存

#### 输入/输出
- **输入**：iaa_data.ad_account_conf 表 channel_id=2的媒体账号
- **输出**：iaa_data.ad_cost 消耗记录

#### 错误处理
- 记录信息到相关日志 `./elk_log/google_grab_err/xx/xx/xx`

#### 依赖关系
- 无

### Tiktok广告消耗抓取（每日）

#### 功能描述
抓取所有tiktok平台投放账号的广告消耗数据

#### 执行流程
1. 请求tiktok广告api接口抓取消耗数据
2. 解释计划名称，匹配游戏ID、系统信息、国家地区信息
3. 相关数据纬度计算
4. 去重入库保存

#### 输入/输出
- **输入**：iaa_data.ad_account_conf 表 channel_id=4的媒体账号
- **输出**：iaa_data.ad_cost 消耗记录

#### 错误处理
- 记录信息到相关日志 `./elk_log/tiktok_grab_err/xx/xx/xx`

#### 依赖关系
- 无

### Tiktok广告消耗抓取（每小时）

#### 功能描述
抓取所有tiktok平台投放账号的广告消耗数据

#### 执行流程
1. 请求tiktok广告api接口抓取消耗数据
2. 解释计划名称，匹配游戏ID、系统信息、国家地区信息
3. 相关数据纬度计算
4. 去重入库保存

#### 输入/输出
- **输入**：iaa_data.ad_account_conf 表 channel_id=4的媒体账号
- **输出**：iaa_data.ad_cost 消耗记录

#### 错误处理
- 记录信息到相关日志 `./elk_log/tiktok_grab_hour_err/xx/xx/xx`

#### 依赖关系
- 无

### Applovin广告消耗抓取（每日）

#### 功能描述
抓取所有Applovin平台投放账号的广告消耗数据

#### 执行流程
1. 请求Applovin广告api接口抓取消耗数据
2. 解释计划名称，匹配游戏ID、系统信息、国家地区信息
3. 相关数据纬度计算
4. 去重入库保存

#### 输入/输出
- **输入**：iaa_data.ad_account_conf 表 channel_id=5的媒体账号
- **输出**：iaa_data.ad_cost 消耗记录

#### 错误处理
- 记录信息到相关日志 `./elk_log/applovin_grab_err/xx/xx/xx`

#### 依赖关系
- 无

### Applovin广告消耗抓取（每小时）

#### 功能描述
抓取所有Applovin平台投放账号的广告消耗数据

#### 执行流程
1. 请求Applovin广告api接口抓取消耗数据
2. 解释计划名称，匹配游戏ID、系统信息、国家地区信息
3. 相关数据纬度计算
4. 去重入库保存

#### 输入/输出
- **输入**：iaa_data.ad_account_conf 表 channel_id=5的媒体账号
- **输出**：iaa_data.ad_cost 消耗记录

#### 错误处理
- 记录信息到相关日志 `./elk_log/applovin_grab_hour_err/xx/xx/xx`

#### 依赖关系
- 无