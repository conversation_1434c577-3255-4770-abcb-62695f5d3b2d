package check_login

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils/root_iaa"
	"time"
)

func InitFunc(args ...string) {

	nowTime := time.Now()
	startTime := nowTime.Format("2006-01-02") + " 00:00:00"
	endTime := nowTime.Format("2006-01-02") + " 23:59:59"

	sql := "select core_account , B.TIME_BJ as login_time, A.time_bj as revenue_time" +
		" from hw_sdk_ad_revenue_log as A join hw_sdk_user_login_package as B using(game_id," +
		" PACKAGE_ID, CORE_ACCOUNT) where " +
		" A.time_bj  between '" + startTime + "' and '" + endTime + "' " +
		" and B.time_bj between '" + startTime + "' and '" + endTime + "' " +
		" group by A.core_account  " +
		" having " +
		" A.time_bj<B.time_bj"

	list, _ := plusQ.Db("data").List(sql)
	if len(list) > 3 {
		root_iaa.RobotInstance().Send("存在收益时间比新增时间早的事件过多，记录：" + fmt.Sprint(len(list)))
	}

	sql2 := "select r.cp_game_id , r.core_account from hw_sdk_user_reg  left join hw_sdk_user_login l on r.core_account = l.core_account where " +
		"  r.time_bj between '" + startTime + "' and '" + endTime + "' " +
		" and r.cp_game_id !=11 and l.core_account is null"
	list2, _ := plusQ.Db("data").List(sql2)
	if len(list2) > 1 {
		root_iaa.RobotInstance().Send("新增注册用户查询不到登陆记录用户过多，记录：" + fmt.Sprint(len(list2)))
	}
}
