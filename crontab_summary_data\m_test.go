package crontab_summary_data

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/provider"
	"fmt"
	"iaa_data/utils"
	"log"
	"testing"
	"time"
)

// https://r.applovin.com/max/userAdRevenueReport?api_key=«report-key»&date=«report-day»&platform=«app-platform»&application=«app-package-name»&store_id=«app-store-ID»&aggregated=«is-aggregated»
func TestA2(t2 *testing.T) {

	configFile := "config_test.yaml"
	_, err := plus.LoadServerConfigDefault(configFile)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}
	plus.AppConfig.ConsoleLogFile = "./log_common/crontab_summary/demo.log"
	provider.Register() //注册服务

	nowTime := time.Now()
	startTime := nowTime.Format("2006-01-02") + " 00:00:00"
	endTime := nowTime.Format("2006-01-02") + " 23:59:59"

	sql := "select core_account , B.TIME_BJ as login_time, A.time_bj as revenue_time" +
		" from hw_sdk_ad_revenue_log as A join hw_sdk_user_login_package as B using(game_id," +
		" PACKAGE_ID, CORE_ACCOUNT) where " +
		" A.time_bj  between '" + startTime + "' and '" + endTime + "' " +
		" and B.time_bj between '" + startTime + "' and '" + endTime + "' " +
		" and B.cp_game_id =16  group by A.core_account  " +
		" having " +
		" A.time_bj<B.time_bj"

	log.Println(sql)
	if 2 == 2 {
		return
	}

	//ll := "2025-06-30 00:00:00"
	//log.Println(utils.ERUtoBjTime(ll))
	//summary_ad_user.SummaryReport("2025-06-24", "2025-06-24", constant.TIME_ERU)
	nowTimeStr := utils.BjToUSTime("2025-07-02 00:00:00")
	//da, _ := time.Parse(time.DateTime, nowTimeStr)
	//tdayTime := da.Format(time.DateOnly)
	log.Println(nowTimeStr)

	start := "2025-07-01 16:36:12"
	end := "2025-07-09 16:36:12"
	selectSql := "select a.cp_game_id, a.game_id, a.country, a.package_id, a.source_id, a.os, a.creative_id, count(distinct(a.core_account)) as num  " +
		" from hw_sdk_user_payment  a left join hw_sdk_user_login_package  b on a.core_account = b.core_account and a.package_id = b.package_id  " +
		" where a.pay_time  between '" + start + "' and '" + end + "' " +
		" and b.time_bj  between '" + start + "' and '" + end + "' " +
		" and pay_result = 1 "

	//`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql = selectSql + " group by game_id,package_id,country,source_id ,creative_id"

	log.Println(selectSql)
	//event_upload.GetRemainList("2025-05-11", "2025-05-14")
	//for i := 0; i < 46; i++ {
	//	postData(fmt.Sprint(i))
	//}
	//summary_ad_position_helper.SummaryReport("2025-05-11", "2025-05-14", "")
	//summary_ad_roi_helper.SummaryReport("2025-06-18", "2025-06-18", constant.TIME_ERU)
	//summary_operation_helper.SummaryReport("2025-05-11", "2025-05-14", "")
	//summary_ad_user.SummaryReport("2025-05-11", "2025-05-14", "")

	//max_ltv_report.Replay()

	//keyList := []string{constant.MAX_API_KEY}
	//for _, key := range keyList {
	//	for i := 0; i < 46; i++ {
	//		nowTime := time.Now()
	//		startTime := nowTime.Add(-24 * 2 * time.Hour).Format("2006-01-02")
	//		max_ltv_report.PostData(fmt.Sprint(i), startTime, key)
	//		max_ltv_report.PostDisplayData(fmt.Sprint(i), startTime, key)
	//		max_ltv_report.PostSessionData(fmt.Sprint(i), startTime, key)
	//	}
	//}

	//	max_user_report.GetLtvReport("android", "2025-05-28", "com.m001.shoumeng", constant.MAX_API_KEY)

	//for i := 0; i < 46; i++ {
	//	nowTime := time.Now()
	//	startTime := nowTime.Add(-24 * 45 * time.Hour).Format("2006-01-02")
	//	max_ltv_report.PostSessionData(fmt.Sprint(i), startTime, constant.MAX_API_KEY)
	//	//postDisplayData(fmt.Sprint(i))
	//	//postSessionData(fmt.Sprint(i))
	//}

	//summary_ad_position_helper.SummaryReport("2025-04-22", "2025-04-23", "")
	//max_hour_report.Replay("2025-06-18","2025-06-23")
	//log.Println(max_ltv_report.Ccc)

}

func TestA3(t2 *testing.T) {
	//
	log.Printf(utils.ERUtoBjTime("2025-06-05 00:00:00"))
	log.Printf(utils.ERUtoBjTime("2025-06-05 23:59:59"))

	//item := map[string]interface{}{
	//	"os": "android",
	//}
	//	facebook_event.PostEvent("1271238344630647", "EAA4jbMOVrVYBO5yts4I5lM2KkWYyQoavyPud85u6FusPkT4Ja54ZCD1rLc0I8jbqZAnM8cnEUl1VfzaWIPb81qMnidA54bgOy5mAWieFflfZC2E2f6puVn9PrZAOmJ5AXygawybw0DBKLwCZAlOrRZC4W4kxFw2QsCMlZBRCpo7ePXlwOPKaZC3uCCrZB605eH7j5XAZDZD", "test_enent6", "", item)
}

func getResultList(start, end, tableName string) {
	selectSql := "SELECT  game_id,cp_game_id, country,  package_id, placement,os,  COUNT(*) AS total_count " +
		" FROM " + tableName +
		" where time_bj between '" + start + "' and '" + end + "'"
	if tableName == "hw_sdk_ad_revenue_log" {
		selectSql = selectSql + " and revenue > 0' "
	}

	groupSql := " GROUP BY   game_id,country, package_id, placement "
	selectSql = selectSql + groupSql

	log.Println(selectSql)
	//log.Println("====hw_sdk_ad_revenue_log", selectSql)

}

func GetTimeForTimeZone(timeVal, timeZone string) string {
	timeBj, err := ParseBjTime(time.DateTime, timeVal)
	if err != nil {
		return timeVal
	}
	if timeZone == "US" {
		laLoc, _ := time.LoadLocation("America/Los_Angeles")
		laTime := timeBj.In(laLoc)
		return laTime.Format("2006-01-02 15:04:05")
	} else if timeZone == "EUR" {
		laLoc, _ := time.LoadLocation("UTC")
		laTime := timeBj.In(laLoc)
		return laTime.Format("2006-01-02 15:04:05")
	} else {
		return timeVal
	}
}

func ParseBjTime(layout, dateTimeStr string) (time.Time, error) {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Println("加载时区时出错:", err)
		return time.Now(), err
	}
	// 解析字符串为 time.Time 类型，并指定时区
	t, err := time.ParseInLocation(layout, dateTimeStr, loc)
	return t, err
}

func getColumns(count string) string {
	var columns []string

	columns = append(columns, "day")
	columns = append(columns, "application")
	columns = append(columns, "package_name")
	columns = append(columns, "platform")
	columns = append(columns, "country")
	columns = append(columns, "installs")

	columns = append(columns, "daily_usage_"+count)
	columns = append(columns, "session_count_"+count)
	columns = append(columns, "user_count_"+count)
	columns = append(columns, "session_length_"+count)
	columns = append(columns, "retention_"+count)

	columnsStr := ""
	for _, val := range columns {
		if columnsStr == "" {
			columnsStr = val
		} else {
			columnsStr = columnsStr + "," + val
		}
	}
	return columnsStr
}
