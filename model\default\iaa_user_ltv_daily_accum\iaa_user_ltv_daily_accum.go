package iaa_user_ltv_daily_accum

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("default")
	Table    = orm.Table("iaa_user_ltv_daily_accum")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 主键
	Id = property.Property("id")
	// Tday 日期
	Tday = property.Property("tday")
	// CpGameId 游戏原名ID
	CpGameId = property.Property("cp_game_id")
	// GameId 游戏ID
	GameId = property.Property("game_id")
	// CountryId 国家ID
	CountryId = property.Property("country_id")
	// SourceId 媒体ID
	SourceId = property.Property("source_id")
	// PackageId 包号ID
	PackageId = property.Property("package_id")
	// CreativeId 创意ID
	CreativeId = property.Property("creative_id")
	// Platform 客户端android、ios
	Platform = property.Property("platform")
	// NewUserNum 新增用户
	NewUserNum = property.Property("new_user_num")
	// ActiveNum 活跃用户
	ActiveNum = property.Property("active_num")
	// Revenue 广告收入 （美元）
	Revenue = property.Property("revenue")
	// RevenueRmb 广告收入（人民币）
	RevenueRmb = property.Property("revenue_rmb")
	// Money 充值金额 （美元）
	Money = property.Property("money")
	// MoneyRmb 充值金额  （人民币）
	MoneyRmb = property.Property("money_rmb")
	// DayType 留存标识
	DayType = property.Property("day_type")
	// UpdateTime 更新时间
	UpdateTime = property.Property("update_time")
	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone = property.Property("time_zone")
)

var FieldsAll = Fields(Id, Tday, CpGameId, GameId, CountryId, SourceId, PackageId, CreativeId, Platform, NewUserNum, ActiveNum, Revenue, RevenueRmb, Money, MoneyRmb, DayType, UpdateTime, TimeZone)
var NonePrimaryFields = Fields(Tday, CpGameId, GameId, CountryId, SourceId, PackageId, CreativeId, Platform, NewUserNum, ActiveNum, Revenue, RevenueRmb, Money, MoneyRmb, DayType, UpdateTime, TimeZone)
var NoneAutoIncrementFields = Fields(Tday, CpGameId, GameId, CountryId, SourceId, PackageId, CreativeId, Platform, NewUserNum, ActiveNum, Revenue, RevenueRmb, Money, MoneyRmb, DayType, UpdateTime, TimeZone)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// IaaUserLtvDailyAccumModel 用户LTV表
type IaaUserLtvDailyAccumModel struct {
	// Id 主键
	Id uint64 `orm:"id" json:"id"`

	// Tday 日期
	Tday int `orm:"tday" json:"tday"`

	// CpGameId 游戏原名ID
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// GameId 游戏ID
	GameId int `orm:"game_id" json:"game_id"`

	// CountryId 国家ID
	CountryId int `orm:"country_id" json:"country_id"`

	// SourceId 媒体ID
	SourceId int `orm:"source_id" json:"source_id"`

	// PackageId 包号ID
	PackageId int `orm:"package_id" json:"package_id"`

	// CreativeId 创意ID
	CreativeId string `orm:"creative_id" json:"creative_id"`

	// Platform 客户端android、ios
	Platform string `orm:"platform" json:"platform"`

	// NewUserNum 新增用户
	NewUserNum int `orm:"new_user_num" json:"new_user_num"`

	// ActiveNum 活跃用户
	ActiveNum int `orm:"active_num" json:"active_num"`

	// Revenue 广告收入 （美元）
	Revenue float32 `orm:"revenue" json:"revenue"`

	// RevenueRmb 广告收入（人民币）
	RevenueRmb float32 `orm:"revenue_rmb" json:"revenue_rmb"`

	// Money 充值金额 （美元）
	Money float32 `orm:"money" json:"money"`

	// MoneyRmb 充值金额  （人民币）
	MoneyRmb float32 `orm:"money_rmb" json:"money_rmb"`

	// DayType 留存标识
	DayType int `orm:"day_type" json:"day_type"`

	// UpdateTime 更新时间
	UpdateTime CustomTime `orm:"update_time" json:"update_time"`

	// TimeZone 时区: 1=北京，2=欧洲，3=美国
	TimeZone int `orm:"time_zone" json:"time_zone"`
}

type PagedResult struct {
	Records      []*IaaUserLtvDailyAccumModel `json:"list"`
	PageNum      int                          `json:"page"`
	PageSize     int                          `json:"page_size"`
	TotalPages   int                          `json:"total_pages"`
	TotalRecords int                          `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:         1,
	Tday:       2,
	CpGameId:   3,
	GameId:     4,
	CountryId:  5,
	SourceId:   6,
	PackageId:  7,
	CreativeId: 8,
	Platform:   9,
	NewUserNum: 10,
	ActiveNum:  11,
	Revenue:    12,
	RevenueRmb: 13,
	Money:      14,
	MoneyRmb:   15,
	DayType:    16,
	UpdateTime: 17,
	TimeZone:   18,
}

func (m *IaaUserLtvDailyAccumModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *IaaUserLtvDailyAccumModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.Platform, m.NewUserNum, m.ActiveNum, m.Revenue, m.RevenueRmb, m.Money, m.MoneyRmb, m.DayType, m.UpdateTime, m.TimeZone)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaUserLtvDailyAccumModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.Platform, m.NewUserNum, m.ActiveNum, m.Revenue, m.RevenueRmb, m.Money, m.MoneyRmb, m.DayType, m.UpdateTime, m.TimeZone)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *IaaUserLtvDailyAccumModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *IaaUserLtvDailyAccumModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.Platform, m.NewUserNum, m.ActiveNum, m.Revenue, m.RevenueRmb, m.Money, m.MoneyRmb, m.DayType, m.UpdateTime, m.TimeZone)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaUserLtvDailyAccumModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *IaaUserLtvDailyAccumModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.Platform, m.NewUserNum, m.ActiveNum, m.Revenue, m.RevenueRmb, m.Money, m.MoneyRmb, m.DayType, m.UpdateTime, m.TimeZone), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaUserLtvDailyAccumModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *IaaUserLtvDailyAccumModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.Platform, m.NewUserNum, m.ActiveNum, m.Revenue, m.RevenueRmb, m.Money, m.MoneyRmb, m.DayType, m.UpdateTime, m.TimeZone), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.Tday, m.CpGameId, m.GameId, m.CountryId, m.SourceId, m.PackageId, m.CreativeId, m.Platform, m.NewUserNum, m.ActiveNum, m.Revenue, m.RevenueRmb, m.Money, m.MoneyRmb, m.DayType, m.UpdateTime, m.TimeZone))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *IaaUserLtvDailyAccumModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *IaaUserLtvDailyAccumModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *IaaUserLtvDailyAccumModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *IaaUserLtvDailyAccumModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *IaaUserLtvDailyAccumModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *IaaUserLtvDailyAccumModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*IaaUserLtvDailyAccumModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*IaaUserLtvDailyAccumModel, error) {

	modelList := make([]*IaaUserLtvDailyAccumModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaUserLtvDailyAccumModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.Platform, &m.NewUserNum, &m.ActiveNum, &m.Revenue, &m.RevenueRmb, &m.Money, &m.MoneyRmb, &m.DayType, &m.UpdateTime, &m.TimeZone)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint64) (*IaaUserLtvDailyAccumModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint64) (*IaaUserLtvDailyAccumModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint64) (*IaaUserLtvDailyAccumModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint64) (*IaaUserLtvDailyAccumModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*IaaUserLtvDailyAccumModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*IaaUserLtvDailyAccumModel, error) {

	m := &IaaUserLtvDailyAccumModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.Platform, &m.NewUserNum, &m.ActiveNum, &m.Revenue, &m.RevenueRmb, &m.Money, &m.MoneyRmb, &m.DayType, &m.UpdateTime, &m.TimeZone)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*IaaUserLtvDailyAccumModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*IaaUserLtvDailyAccumModel, error) {

	modelList := make([]*IaaUserLtvDailyAccumModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &IaaUserLtvDailyAccumModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.Platform, &m.NewUserNum, &m.ActiveNum, &m.Revenue, &m.RevenueRmb, &m.Money, &m.MoneyRmb, &m.DayType, &m.UpdateTime, &m.TimeZone)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*IaaUserLtvDailyAccumModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*IaaUserLtvDailyAccumModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &IaaUserLtvDailyAccumModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.Tday, &m.CpGameId, &m.GameId, &m.CountryId, &m.SourceId, &m.PackageId, &m.CreativeId, &m.Platform, &m.NewUserNum, &m.ActiveNum, &m.Revenue, &m.RevenueRmb, &m.Money, &m.MoneyRmb, &m.DayType, &m.UpdateTime, &m.TimeZone)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint64) (*IaaUserLtvDailyAccumModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint64) (*IaaUserLtvDailyAccumModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*IaaUserLtvDailyAccumModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*IaaUserLtvDailyAccumModel, error) {

	m := &IaaUserLtvDailyAccumModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*IaaUserLtvDailyAccumModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*IaaUserLtvDailyAccumModel, error) {

	modelList := make([]*IaaUserLtvDailyAccumModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &IaaUserLtvDailyAccumModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*IaaUserLtvDailyAccumModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*IaaUserLtvDailyAccumModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*IaaUserLtvDailyAccumModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*IaaUserLtvDailyAccumModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.Tday, d.CpGameId, d.GameId, d.CountryId, d.SourceId, d.PackageId, d.CreativeId, d.Platform, d.NewUserNum, d.ActiveNum, d.Revenue, d.RevenueRmb, d.Money, d.MoneyRmb, d.DayType, d.UpdateTime, d.TimeZone))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "主键",
		},
		{
			"dataIndex": "tday",
			"title":     "日期",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "游戏原名ID",
		},
		{
			"dataIndex": "game_id",
			"title":     "游戏ID",
		},
		{
			"dataIndex": "country_id",
			"title":     "国家ID",
		},
		{
			"dataIndex": "source_id",
			"title":     "媒体ID",
		},
		{
			"dataIndex": "package_id",
			"title":     "包号ID",
		},
		{
			"dataIndex": "creative_id",
			"title":     "创意ID",
		},
		{
			"dataIndex": "platform",
			"title":     "客户端android、ios",
		},
		{
			"dataIndex": "new_user_num",
			"title":     "新增用户",
		},
		{
			"dataIndex": "active_num",
			"title":     "活跃用户",
		},
		{
			"dataIndex": "revenue",
			"title":     "广告收入 （美元）",
		},
		{
			"dataIndex": "revenue_rmb",
			"title":     "广告收入（人民币）",
		},
		{
			"dataIndex": "money",
			"title":     "充值金额 （美元）",
		},
		{
			"dataIndex": "money_rmb",
			"title":     "充值金额  （人民币）",
		},
		{
			"dataIndex": "day_type",
			"title":     "留存标识",
		},
		{
			"dataIndex": "update_time",
			"title":     "更新时间",
		},
		{
			"dataIndex": "time_zone",
			"title":     "时区: 1=北京，2=欧洲，3=美国",
		}}
}
