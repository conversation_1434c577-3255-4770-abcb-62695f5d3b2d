package summary_ad_roi_helper

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils"
	"iaa_data/utils/constant"
	"iaa_data/utils/country_helper"
	"iaa_data/utils/game_helper"
	"iaa_data/utils/sql_helper"
	"log"
	"strings"
	"time"
)

var keyValMap map[string]string
var addList []string

var mResvue = float32(0.00)

func SummaryReport(startTime, endTime, timeZoneStr string) {
	if !utils.IsTimeZone(timeZoneStr) {
		return
	}
	startDate, err1 := time.Parse(time.DateOnly, startTime)
	if err1 != nil {
		log.Println("时间格式有误startTime")
		return
	}
	endDate, err2 := time.Parse(time.DateOnly, endTime)
	if err2 != nil {
		log.Println("时间格式有误endTime")
		return
	}

	for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {
		timeVal := currentDate.Format("2006-01-02")
		sTime := timeVal + " 00:00:00"
		eTime := timeVal + " 23:59:59"
		tday := currentDate.Format("********")
		mResvue = 0
		eruStartTime := sTime
		eruEndTime := eTime

		if timeZoneStr == constant.TIME_ERU {
			sTime = utils.ERUtoBjTime(sTime)
			eTime = utils.ERUtoBjTime(eTime)
		} else if timeZoneStr == constant.TIME_US {
			sTime = utils.UStoBjTime(sTime)
			eTime = utils.UStoBjTime(eTime)
		}

		timeZone := utils.GeTimeZoneTag(timeZoneStr)

		addList = nil
		keyValMap = nil
		keyValMap = sql_helper.GetUniqueLogs(tday, fmt.Sprint(timeZone), "iaa_ad_roi_realtime")

		delSql := "delete FROM iaa_ad_roi_realtime where tday=" + tday + " and time_zone = " + fmt.Sprint(timeZone)
		_, errDel := plusQ.Db().Delete(delSql)
		if errDel != nil {
			log.Println("删除失败", errDel)
		}
		if timeZoneStr == constant.TIME_ERU {
			searAllAd(timeZoneStr, tday, eruStartTime, eruEndTime)
			searRealRevenue(eruStartTime, eruEndTime, timeZoneStr, tday)
		}
		searRevenue(timeZoneStr, tday, sTime, eTime)
		searIapPay(timeZoneStr, tday, sTime, eTime)
		searIapPayNewUser(timeZoneStr, tday, sTime, eTime)
		searNewUser(timeZoneStr, tday, sTime, eTime, "hw_sdk_user_login_package", "new_user_num")
		searNewUser(timeZoneStr, tday, sTime, eTime, "hw_sdk_active_log", "device_active_num")

		if len(addList) > 0 {
			for _, keyVal := range addList {
				delete(keyValMap, keyVal)
				if keyVal == "2||20250701||11||237||2||||110001" {
					log.Println("hhh=删除key")
				}
				info := make(map[string]interface{})
				info["key_val"] = keyVal
				info["table_name"] = "iaa_ad_roi_realtime"
				info["tday"] = tday
				info["time_zone"] = timeZone
				info["update_time"] = time.Now().Format("2006-01-02 15:04:05")
				sql_helper.LogHelperInstance().HandleDataMapInsert("data", "iaa_report_unique_log", "iaa_report_unique_log", info, 30, 2)
			}
			sql_helper.LogHelperInstance().HandleDataMapEnd("data", "iaa_report_unique_log", "iaa_report_unique_log")
		}
		if len(keyValMap) > 0 {
			for _, keyVal := range keyValMap {
				keyVals := strings.Split(keyVal, "||")
				if len(keyVals) == 7 {
					//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
					info := make(map[string]interface{})
					info["time_zone"] = keyVals[0]
					info["tday"] = keyVals[1]
					info["game_id"] = keyVals[2]
					info["country_id"] = keyVals[3]
					info["source_id"] = keyVals[4]
					info["creative_id"] = keyVals[5]
					info["package_id"] = keyVals[6]
					cpGameId := game_helper.GameHelInstant().GetCpGameId(utils.AnyToInt(keyVals[2]))
					channelId := game_helper.GameHelInstant().GetChannelId(utils.AnyToInt(keyVals[6]))
					info["cp_game_id"] = cpGameId
					info["channel_id"] = channelId
					info["cost_sun"] = 0
					info["cost_discount_sun"] = 0
					info["show_num"] = 0
					info["click_num"] = 0
					info["install_num"] = 0
					info["real_revenue"] = 0
					info["estimate_revenue"] = 0
					info["revenue"] = 0
					info["iap_pay_money"] = 0
					info["new_user_num"] = 0
					info["new_user_pay_num"] = 0
					info["new_user_pay_sum"] = 0
					info["device_active_num"] = 0
					info["update_time"] = time.Now().Format("2006-01-02 15:04:05")

					err := sql_helper.InsertOrUpdateAllFiledSummary("", "iaa_ad_roi_realtime", "time_zone|game_id|tday|country_id|source_id|creative_id|package_id", info)
					if err != nil {
						log.Println("更新置0失败 iaa_ad_roi_realtime:", err)
					}
				}
			}
		}
		log.Println("==============总金额"+timeZoneStr, mResvue)
	}
}

func searAllAd(timeZoneStr, tday, sTime, eTime string) {
	list, err := getResultList(sTime, eTime)
	if err != nil {
		return
	}
	summaryCommonNum(timeZoneStr, tday, list, "all_ad")
}

func searRevenue(timeZoneStr, tday, sTime, eTime string) {
	list, err := summaryRevenue(sTime, eTime)
	if err != nil {
		return
	}
	summaryCommonNum(timeZoneStr, tday, list, "estimate_revenue")
}
func searRealRevenue(sTime, eTime, timeZoneStr, tday string) {
	list, err := getRealAdList(sTime, eTime)
	if err != nil {
		return
	}
	summaryCommonNum(timeZoneStr, tday, list, "real_revenue")
}

func searIapPay(sTime, eTime, timeZoneStr, tday string) {
	list, err := summaryIapPay(sTime, eTime)
	if err != nil {
		return
	}
	summaryCommonNum(timeZoneStr, tday, list, "iap_pay_money")
}

func searIapPayNewUser(sTime, eTime, timeZoneStr, tday string) {
	list, err := summaryIapPayNewUser(sTime, eTime)
	if err != nil {
		return
	}
	summaryCommonNum(timeZoneStr, tday, list, "new_user_pay_num")
}

func searNewUser(timeZoneStr, tday, sTime, eTime, tableName, numTag string) {
	list, err := summaryNewUser(sTime, eTime, tableName)
	if err != nil {
		return
	}
	summaryCommonNum(timeZoneStr, tday, list, numTag)
}

func summaryCommonNum(timeZoneStr, tday string, list []map[string]interface{}, numTag string) {
	for _, v := range list {
		gameId := fmt.Sprint(v["game_id"])
		cpGameId := fmt.Sprint(v["cp_game_id"])
		package_id := fmt.Sprint(v["package_id"])

		source_id := fmt.Sprint(v["source_id"])
		country_id := 0
		if numTag == "all_ad" {
			country := fmt.Sprint(v["country_code"])
			country_id = country_helper.CountryInstant().GetCountryId(country)
		} else {
			//cp_game_id,game_id,country,package_id,source_id,os,creative_id"
			country := fmt.Sprint(v["country"])
			country_id = country_helper.CountryInstant().GetCountryId(country)
		}
		creative_id := ""
		timeZone := utils.GeTimeZoneTag(timeZoneStr)

		info := make(map[string]interface{})
		info["time_zone"] = timeZone
		info["tday"] = tday
		info["game_id"] = gameId
		info["cp_game_id"] = cpGameId
		info["country_id"] = country_id
		info["package_id"] = package_id
		info["source_id"] = source_id
		info["update_time"] = time.Now().Format(time.DateTime)
		channelId := game_helper.GameHelInstant().GetChannelId(utils.AnyToInt(package_id))
		info["channel_id"] = channelId
		if numTag == "all_ad" {
			creative_id = fmt.Sprint(v["creative_id_md5"])
			info["show_num"] = v["show_num"]
			info["click_num"] = v["click_num"]
			info["install_num"] = v["install_num"]
			info["cost_sun"] = v["cost_sun"]
			info["cost_discount_sun"] = v["cost_discount_sun"]
			platform := ""
			if fmt.Sprint(v["platform_id"]) == "1" {
				platform = "android"
			} else if fmt.Sprint(v["platform_id"]) == "2" {
				platform = "ios"
			}
			info["platform"] = platform
		} else {
			creative_id = fmt.Sprint(v["creative_id"])
			info[numTag] = v["num"]
			if numTag == "estimate_revenue" {
				creative_id = fmt.Sprint(v["iaa_creative_id"])
				lastrevenue := lastRevenue(fmt.Sprint(timeZone), tday, gameId, fmt.Sprint(country_id), source_id, creative_id, package_id)
				val1, _ := utils.StringToFloat64(fmt.Sprint(info[numTag]))
				val2, _ := utils.StringToFloat64(fmt.Sprint(lastrevenue))
				allVal := val1 + val2
				info["revenue"] = allVal
				mResvue = mResvue + val1
			} else {
				fmt.Sprint(v["creative_id"])
			}
			if numTag == "new_user_num" || numTag == "device_active_num" || numTag == "estimate_revenue" {
				info["platform"] = v["os"]
			} else {
				info["platform"] = v["platform"]
			}
		}
		info["creative_id"] = creative_id

		//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
		keyVal := fmt.Sprint(timeZone) + "||" + tday + "||" + gameId + "||" + fmt.Sprint(country_id) + "||" + fmt.Sprint(source_id) + "||" + creative_id + "||" + package_id
		if !sql_helper.IsHaveKey(addList, keyVal) {
			addList = append(addList, keyVal)
		}

		//2||20250701||11||237||2||||110001
		//cp_game_id =11 and tday =20250701 and  country_id  =237 and platform ='android' and  package_id  =110001 and game_id  =11 and source_id  =2

		err := sql_helper.InsertOrUpdateAllFiledSummary("", "iaa_ad_roi_realtime", "time_zone|game_id|tday|country_id|source_id|creative_id|package_id", info)
		if err != nil {
			log.Println("insert err:", err)
		}
	}
}

func getRealAdList(start, end string) ([]map[string]interface{}, error) {
	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql := "select  country, package_id,creative_id, source_id,platform," +
		" sum(revenue) AS revenue " +
		" FROM ad_report_user " +
		" where `date` between '" + start + "' and '" + end + "'"

	//`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`

	groupSql := " GROUP BY  country, package_id ,creative_id,source_id"
	selectSql = selectSql + groupSql

	list, err := plusQ.Db("data").List(selectSql)
	log.Println("ad_report_user  sum", selectSql)

	if err == nil {
		var newList []map[string]interface{}
		for _, item := range list {
			packageName := fmt.Sprint(item["package_id"]) //该表的包id存的是包名
			source_id := fmt.Sprint(item["source_id"])

			country := fmt.Sprint(item["country"])
			country = strings.ToUpper(country)
			gameId, cpGameId, packageId, err := game_helper.GameHelInstant().GetPackageInfo(packageName)
			if err != nil {
				continue
			}

			info := make(map[string]interface{})
			info["game_id"] = gameId
			info["cp_game_id"] = cpGameId
			info["package_id"] = packageId
			info["country"] = country
			info["source_id"] = source_id
			info["creative_id"] = item["creative_id"]
			info["platform"] = item["platform"]
			info["num"] = item["revenue"]
			newList = append(newList, info)
		}
		return newList, nil
	}
	return nil, err
}

func getResultList(start, end string) ([]map[string]interface{}, error) {

	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql := "select game_id,cp_game_id, country_code, package_id,creative_id_md5, source_id,platform_id," +
		" sum(`show`) AS show_num, " +
		" sum(click) AS click_num, " +
		" sum(install) AS install_num, " +
		" sum(cost) AS cost_sun, " +
		" sum(cost_discount) AS cost_discount_sun " +
		" FROM ad_cost " +
		" where `time` between '" + start + "' and '" + end + "'"

	//`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`

	groupSql := " GROUP BY  game_id,country_code, package_id ,creative_id_md5,source_id"
	selectSql = selectSql + groupSql

	list, err := plusQ.Db("data").List(selectSql)
	//log.Println("roi_getResultList", selectSql)

	return list, err
}

func summaryRevenue(start, end string) ([]map[string]interface{}, error) {
	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql := "SELECT cp_game_id,game_id,country,package_id,source_id,os,iaa_creative_id," +
		" sum(revenue) as num" +
		" FROM hw_sdk_ad_revenue_log " +
		" where time_bj between '" + start + "' and '" + end + "'"

	//`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql = selectSql + " group by game_id,package_id,country,source_id ,iaa_creative_id"

	log.Println("========ss=s=s=s", selectSql)
	list, err := plusQ.Db("data").List(selectSql)

	return list, err
}

func summaryIapPay(start, end string) ([]map[string]interface{}, error) {
	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql := "SELECT cp_game_id,game_id,country,package_id,source_id,os,creative_id," +
		" sum(money) as num" +
		" FROM hw_sdk_user_payment " +
		" where pay_time between '" + start + "' and '" + end + "'" +
		" and pay_result = 1"

	//`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql = selectSql + " group by game_id,package_id,country,source_id ,creative_id"

	list, err := plusQ.Db("data").List(selectSql)
	return list, err
}

func summaryIapPayNewUser(start, end string) ([]map[string]interface{}, error) {
	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql := "select a.cp_game_id, a.game_id, a.country, a.package_id, a.source_id, a.os, a.creative_id, count(distinct(a.core_account)) as num  " +
		" from hw_sdk_user_payment  a left join hw_sdk_user_login_package  b on a.core_account = b.core_account and a.package_id = b.package_id  " +
		" where a.pay_time  between '" + start + "' and '" + end + "' " +
		" and b.time_bj  between '" + start + "' and '" + end + "' " +
		" and pay_result = 1 "

	//`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql = selectSql + " group by game_id,package_id,country,source_id ,creative_id"

	list, err := plusQ.Db("data").List(selectSql)
	return list, err
}

func lastRevenue(time_zone, tday, game_id, country_id, source_id, creative_id, package_id string) interface{} {
	mT, _ := time.Parse("********", tday)
	mT = mT.Add(-24 * time.Hour)
	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`
	selectSql := "select " +
		" revenue " +
		" FROM iaa_ad_roi_realtime " +
		" where tday=" + mT.Format("********") +
		" and time_zone=" + time_zone +
		" and game_id=" + game_id +
		" and country_id=" + country_id +
		" and source_id=" + source_id +
		" and creative_id='" + creative_id + "'" +
		" and package_id=" + package_id

	selectSql = selectSql

	info, err := plusQ.Db().Get(selectSql)

	if err == nil {
		return info["revenue"]
	}
	return "0"
}

func summaryNewUser(start, end, tableName string) ([]map[string]interface{}, error) {
	//`time_zone`,`tday`,`game_id`,`country_id`,`source_id`,`creative_id`,`package_id`

	selectSql := "select game_id,cp_game_id, country, package_id,creative_id, source_id,os," +
		" count(*) AS num " +
		" FROM " + tableName +
		" where time_bj between '" + start + "' and '" + end + "'"

	groupSql := " GROUP BY  game_id,country, package_id ,creative_id,source_id"
	selectSql = selectSql + groupSql

	list, err := plusQ.Db("data").List(selectSql)

	return list, err
}
