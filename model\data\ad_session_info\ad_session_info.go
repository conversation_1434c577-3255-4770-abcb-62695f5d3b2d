package ad_session_info

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("data")
	Table    = orm.Table("ad_session_info")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id 自增id
	Id = property.Property("id")
	// DayNum 对应x天后的值
	DayNum = property.Property("day_num")
	// Day 日期
	Day = property.Property("day")
	// Application 应用程序名称
	Application = property.Property("application")
	// PackageName 包名
	PackageName = property.Property("package_name")
	// Platform 应用程序平台：android、fireos、 或ios。	android
	Platform = property.Property("platform")
	// Country 展示的两字母国家/地区代码
	Country = property.Property("country")
	// Installs 新安装者的数量
	Installs = property.Property("installs")
	// DailyUsage 安装后用户花费的平均时间x天（以秒为单位
	DailyUsage = property.Property("daily_usage")
	// SessionCount 安装后x天的用户会话总数
	SessionCount = property.Property("session_count")
	// UserCount 安装后x天内活跃用户数
	UserCount = property.Property("user_count")
	// SessionLength daily_usage_«x»÷ session_count_«x»
	SessionLength = property.Property("session_length")
	// Retention user_count_«x»÷ installs
	Retention = property.Property("retention")
	// Utime 更新时间
	Utime = property.Property("utime")
)

var FieldsAll = Fields(Id, DayNum, Day, Application, PackageName, Platform, Country, Installs, DailyUsage, SessionCount, UserCount, SessionLength, Retention, Utime)
var NonePrimaryFields = Fields(DayNum, Day, Application, PackageName, Platform, Country, Installs, DailyUsage, SessionCount, UserCount, SessionLength, Retention, Utime)
var NoneAutoIncrementFields = Fields(DayNum, Day, Application, PackageName, Platform, Country, Installs, DailyUsage, SessionCount, UserCount, SessionLength, Retention, Utime)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// AdSessionInfoModel 广告session信息表
type AdSessionInfoModel struct {
	// Id 自增id
	Id int `orm:"id" json:"id"`

	// DayNum 对应x天后的值
	DayNum *float32 `orm:"day_num" json:"day_num"`

	// Day 日期
	Day *CustomTime `orm:"day" json:"day"`

	// Application 应用程序名称
	Application *string `orm:"application" json:"application"`

	// PackageName 包名
	PackageName *string `orm:"package_name" json:"package_name"`

	// Platform 应用程序平台：android、fireos、 或ios。	android
	Platform *string `orm:"platform" json:"platform"`

	// Country 展示的两字母国家/地区代码
	Country *string `orm:"country" json:"country"`

	// Installs 新安装者的数量
	Installs *uint32 `orm:"installs" json:"installs"`

	// DailyUsage 安装后用户花费的平均时间x天（以秒为单位
	DailyUsage uint32 `orm:"daily_usage" json:"daily_usage"`

	// SessionCount 安装后x天的用户会话总数
	SessionCount uint32 `orm:"session_count" json:"session_count"`

	// UserCount 安装后x天内活跃用户数
	UserCount uint32 `orm:"user_count" json:"user_count"`

	// SessionLength daily_usage_«x»÷ session_count_«x»
	SessionLength uint32 `orm:"session_length" json:"session_length"`

	// Retention user_count_«x»÷ installs
	Retention uint32 `orm:"retention" json:"retention"`

	// Utime 更新时间
	Utime *CustomTime `orm:"utime" json:"utime"`
}

type PagedResult struct {
	Records      []*AdSessionInfoModel `json:"list"`
	PageNum      int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
	TotalPages   int                   `json:"total_pages"`
	TotalRecords int                   `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:            1,
	DayNum:        2,
	Day:           3,
	Application:   4,
	PackageName:   5,
	Platform:      6,
	Country:       7,
	Installs:      8,
	DailyUsage:    9,
	SessionCount:  10,
	UserCount:     11,
	SessionLength: 12,
	Retention:     13,
	Utime:         14,
}

func (m *AdSessionInfoModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *AdSessionInfoModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.DayNum, m.Day, m.Application, m.PackageName, m.Platform, m.Country, m.Installs, m.DailyUsage, m.SessionCount, m.UserCount, m.SessionLength, m.Retention, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdSessionInfoModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.DayNum, m.Day, m.Application, m.PackageName, m.Platform, m.Country, m.Installs, m.DailyUsage, m.SessionCount, m.UserCount, m.SessionLength, m.Retention, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = int(lastInsertId)

	return nil

}

func (m *AdSessionInfoModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *AdSessionInfoModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.DayNum, m.Day, m.Application, m.PackageName, m.Platform, m.Country, m.Installs, m.DailyUsage, m.SessionCount, m.UserCount, m.SessionLength, m.Retention, m.Utime)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdSessionInfoModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *AdSessionInfoModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.DayNum, m.Day, m.Application, m.PackageName, m.Platform, m.Country, m.Installs, m.DailyUsage, m.SessionCount, m.UserCount, m.SessionLength, m.Retention, m.Utime), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdSessionInfoModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *AdSessionInfoModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.DayNum, m.Day, m.Application, m.PackageName, m.Platform, m.Country, m.Installs, m.DailyUsage, m.SessionCount, m.UserCount, m.SessionLength, m.Retention, m.Utime), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.DayNum, m.Day, m.Application, m.PackageName, m.Platform, m.Country, m.Installs, m.DailyUsage, m.SessionCount, m.UserCount, m.SessionLength, m.Retention, m.Utime))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *AdSessionInfoModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *AdSessionInfoModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *AdSessionInfoModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *AdSessionInfoModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *AdSessionInfoModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *AdSessionInfoModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*AdSessionInfoModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*AdSessionInfoModel, error) {

	modelList := make([]*AdSessionInfoModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdSessionInfoModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.Day, &m.Application, &m.PackageName, &m.Platform, &m.Country, &m.Installs, &m.DailyUsage, &m.SessionCount, &m.UserCount, &m.SessionLength, &m.Retention, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id int) (*AdSessionInfoModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id int) (*AdSessionInfoModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id int) (*AdSessionInfoModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id int) (*AdSessionInfoModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*AdSessionInfoModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*AdSessionInfoModel, error) {

	m := &AdSessionInfoModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.Day, &m.Application, &m.PackageName, &m.Platform, &m.Country, &m.Installs, &m.DailyUsage, &m.SessionCount, &m.UserCount, &m.SessionLength, &m.Retention, &m.Utime)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*AdSessionInfoModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*AdSessionInfoModel, error) {

	modelList := make([]*AdSessionInfoModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &AdSessionInfoModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.Day, &m.Application, &m.PackageName, &m.Platform, &m.Country, &m.Installs, &m.DailyUsage, &m.SessionCount, &m.UserCount, &m.SessionLength, &m.Retention, &m.Utime)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*AdSessionInfoModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*AdSessionInfoModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &AdSessionInfoModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.DayNum, &m.Day, &m.Application, &m.PackageName, &m.Platform, &m.Country, &m.Installs, &m.DailyUsage, &m.SessionCount, &m.UserCount, &m.SessionLength, &m.Retention, &m.Utime)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id int) (*AdSessionInfoModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id int) (*AdSessionInfoModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*AdSessionInfoModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*AdSessionInfoModel, error) {

	m := &AdSessionInfoModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*AdSessionInfoModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*AdSessionInfoModel, error) {

	modelList := make([]*AdSessionInfoModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &AdSessionInfoModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*AdSessionInfoModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*AdSessionInfoModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*AdSessionInfoModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*AdSessionInfoModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.DayNum, d.Day, d.Application, d.PackageName, d.Platform, d.Country, d.Installs, d.DailyUsage, d.SessionCount, d.UserCount, d.SessionLength, d.Retention, d.Utime))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "自增id",
		},
		{
			"dataIndex": "day_num",
			"title":     "对应x天后的值",
		},
		{
			"dataIndex": "day",
			"title":     "日期",
		},
		{
			"dataIndex": "application",
			"title":     "应用程序名称",
		},
		{
			"dataIndex": "package_name",
			"title":     "包名",
		},
		{
			"dataIndex": "platform",
			"title":     "应用程序平台：android、fireos、 或ios。	android",
		},
		{
			"dataIndex": "country",
			"title":     "展示的两字母国家/地区代码",
		},
		{
			"dataIndex": "installs",
			"title":     "新安装者的数量",
		},
		{
			"dataIndex": "daily_usage",
			"title":     "安装后用户花费的平均时间x天（以秒为单位",
		},
		{
			"dataIndex": "session_count",
			"title":     "安装后x天的用户会话总数",
		},
		{
			"dataIndex": "user_count",
			"title":     "安装后x天内活跃用户数",
		},
		{
			"dataIndex": "session_length",
			"title":     "daily_usage_«x»÷ session_count_«x»",
		},
		{
			"dataIndex": "retention",
			"title":     "user_count_«x»÷ installs",
		},
		{
			"dataIndex": "utime",
			"title":     "更新时间",
		}}
}
