package max_user_report

import (
	"910.com/plus2.git/plusQ"
	"encoding/json"
	"fmt"
	"iaa_data/crontab_summary_data/task/adjust_match"
	"iaa_data/crontab_summary_data/task/summary_ad_roi/summary_ad_roi_helper"
	"iaa_data/utils/constant"
	"iaa_data/utils/csv_http"
	"iaa_data/utils/day_check_helper"
	"iaa_data/utils/http_utils"
	"iaa_data/utils/sql_helper"
	"log"
	"time"
)

func InitFunc(args ...string) {
	if day_check_helper.CheckDay14("max_user_report1") {
		return
	}
	for _, key := range constant.MAX_KEY_LIST {
		nowTime := time.Now()
		nowTime = nowTime.Add(-24 * time.Hour)
		listMap := getGameInfoList()
		if listMap != nil {
			for packge, os := range listMap {
				GetLtvReport(os, nowTime.Format(time.DateOnly), packge, key)
			}
		}
	}
}

func getGameInfoList() map[string]string {
	sql := "select * from max_api_conf"
	list, err := plusQ.Db("platform").List(sql)
	if err != nil {
		return nil
	}

	listMap := make(map[string]string)
	for _, row := range list {
		listMap[fmt.Sprint(row["package_name"])] = fmt.Sprint(row["platform"])
	}
	return listMap
}

func Replay(start, end time.Time) {
	for currentDate := start; !currentDate.After(end); currentDate = currentDate.AddDate(0, 0, 1) {
		for _, key := range constant.MAX_KEY_LIST {
			listMap := getGameInfoList()
			if listMap != nil {
				for packge, os := range listMap {
					GetLtvReport(os, currentDate.Format(time.DateOnly), packge, key)
				}
			}
		}
	}
}

func GetLtvReport(platform, dayTime, application, apiKey string) {

	headMap := make(map[string]string)
	params := make(map[string]interface{})

	params["api_key"] = apiKey
	params["platform"] = platform
	params["aggregated"] = "false"
	params["application"] = application
	params["date"] = dayTime
	params["format"] = "json"
	url := constant.AD_USER_REVEN_URL
	url = url + "?" + http_utils.GetParamsUrl(params)
	result, err := http_utils.HttpRequest(url, "GET", params, headMap)
	if err != nil {
		log.Println(err.Error())
	} else {
		log.Println(result)
	}
	var kk map[string]interface{}
	json.Unmarshal([]byte(result), &kk)

	//newUrl := fmt.Sprint(kk["url"])
	ad_revenue_report_url := fmt.Sprint(kk["ad_revenue_report_url"])
	//fb_estimated_revenue_url := fmt.Sprint(kk["fb_estimated_revenue_url"])

	list2 := csv_http.GetCsvData(ad_revenue_report_url)
	startTime := dayTime + " 00:00:00"
	endTime := dayTime + " 23:59:59"
	if len(list2) > 0 {
		sql := "DELETE FROM ad_report_user  WHERE `date` BETWEEN " + "'" + startTime + "' and '" + endTime + "' and platform = '" + platform + "' and package_id='" + application + "'"
		_, err2 := plusQ.Db("data").Delete(sql)
		if err2 != nil {
			log.Println("ppppppppppppppppp", err2)
		}
		log.Println(sql)
	}
	for _, item := range list2 {
		item["utime"] = time.Now().Format(time.DateTime)
		item["platform"] = platform
		item["package_id"] = application
		//item["date"] = dayTime
		sql_helper.LogHelperInstance().HandleDataMapInsert("data", "ad_report_user", "ad_report_user", item, 1, 1)
	}

	tt, errTime := time.Parse(time.DateOnly, dayTime)
	if errTime == nil {
		adjust_match.PlayAdUser(tt, tt)
		summary_ad_roi_helper.SummaryReport(dayTime, dayTime, constant.TIME_ERU)
	}
}
