package source_helper

import (
	"iaa_data/model/platform/hw_base_source_conf"
	"log"
	"time"
)

type SourceHelper struct {
	SourceMap map[string]int
}

var sourceHelperInstance *SourceHelper

func SourceInstant() *SourceHelper {
	if sourceHelperInstance == nil {
		sourceHelperInstance = &SourceHelper{}
	}
	return sourceHelperInstance
}

func (s *SourceHelper) GetSourceId(sourceName string) (int, error) {
	if len(sourceName) == 0 || sourceName == "<nil>" {
		sourceName = "unknown"
	}

	if s.SourceMap == nil {
		s.SourceMap = make(map[string]int)
		list, err := hw_base_source_conf.ListAll()
		if err != nil {
			log.Println("hw_base_source_conf", err)
			return 2, err
		}
		for _, v := range list {
			s.SourceMap[v.SourceName] = int(v.Id)
		}
	}

	id, ok := s.SourceMap[sourceName]
	if ok {
		return id, nil
	}
	now := time.Now()
	info := hw_base_source_conf.HwBaseSourceConfModel{
		SourceName: sourceName,
		AddTime:    hw_base_source_conf.CustomTime(now),
	}
	err := info.Insert()
	if err != nil {
		log.Println("hw_base_source_conf Insert", err)
		return 2, err
	}
	s.SourceMap[sourceName] = int(info.Id)
	return int(info.Id), nil
}
