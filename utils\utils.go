package utils

import (
	"encoding/json"
	"fmt"
	"iaa_data/utils/constant"
	"log"
	"net/url"
	"strconv"
	"time"
)

func ChangeServeTime(mTime string) time.Time {
	layout := "02/Jan/2006:15:04:05 -0700"
	t, err := time.Parse(layout, mTime)
	if err != nil {
		t2, _ := time.Parse(time.DateTime, "1970-01-01 08:00:00")
		return t2
	}
	return t
}

func AnyToInt64Time(value interface{}) int64 {
	if value == nil {
		return 0
	}
	int64Value, err := strconv.ParseFloat(fmt.Sprint(value), 64)

	if err != nil {
		int64Value2, err := strconv.ParseInt(fmt.Sprint(value), 10, 64)
		if err == nil {
			if len(fmt.Sprint(int64Value2)) > 11 {
				int64Value2 = int64Value2 / 1000
			}
			return int64Value2
		}
		return 0
	}
	if len(fmt.Sprint(int64(int64Value))) > 11 {
		int64Value = int64Value / 1000
	}
	return int64(int64Value)
}

// 判断字符串是否包含三个小数点
func hasThreeDots(s string) bool {
	dotCount := 0
	for _, char := range s {
		if char == '.' {
			dotCount++
			if dotCount > 3 { // 如果已经超过三个小数点，则直接返回false（可选）
				return false
			}
		}
	}
	return dotCount == 3
}

func ParseBjTime(layout, dateTimeStr string) (time.Time, error) {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Println("加载时区时出错:", err)
		return time.Now(), err
	}
	// 解析字符串为 time.Time 类型，并指定时区
	t, err := time.ParseInLocation(layout, dateTimeStr, loc)
	return t, err
}

// map合并
func InterfaceToMap(newMap any) map[string]interface{} {
	jsonStr, _ := json.Marshal(newMap)
	var newMap2 map[string]interface{}
	_ = json.Unmarshal(jsonStr, &newMap2)
	return newMap2
}

func MergeMaps(originalMap1, originalMap2 map[string]interface{}) map[string]interface{} {
	for key, val := range originalMap2 {
		originalMap1[key] = val
	}
	return originalMap1
}

func LongToDateTime(value interface{}) string {
	tim64 := AnyToInt64Time(value)
	if len(fmt.Sprint(tim64)) > 10 {
		tim64 = tim64 / 1000
	}
	tBj := time.Unix(tim64, 0)
	return tBj.Format(time.DateTime)
}

func AnyToInt(value interface{}) int {
	newValue, err := strconv.Atoi(fmt.Sprint(value))
	if err != nil {
		floatValue, err2 := strconv.ParseFloat(fmt.Sprint(value), 64)
		if err2 != nil {
			return 0
		}
		// 将浮点数转换为整数
		newValue = int(floatValue)
	}
	return newValue
}

func ChangeString(val interface{}) string {
	if val != nil {
		if fmt.Sprint(val) == "<nil>" {
			return ""
		}
		return fmt.Sprint(val)
	}
	return ""
}

func ChangeStringSummary(val interface{}) string {
	if val != nil {
		return fmt.Sprint(val)
	}
	return ""
}

func UrlEncoded(str string) string {
	return url.PathEscape(str)
}

func GetTimeForTimeZone(timeVal, timeZone string) string {
	timeBj, err := ParseBjTime(time.DateTime, timeVal)
	if err != nil {
		return timeVal
	}
	if timeZone == constant.TIME_US {
		laLoc, _ := time.LoadLocation("America/Los_Angeles")
		laTime := timeBj.In(laLoc)
		return laTime.Format("2006-01-02 15:04:05")
	} else if timeZone == constant.TIME_ERU {
		laLoc, _ := time.LoadLocation("UTC")
		laTime := timeBj.In(laLoc)
		return laTime.Format("2006-01-02 15:04:05")
	} else {
		return timeVal
	}
}

func ERUtoBjTime(timeVal string) string {
	// 解析字符串时间，默认解析为UTC时间
	utcTime, err := time.Parse(time.DateTime, timeVal)
	if err != nil {
		log.Println("解析时间错误:", err)
		return timeVal
	}
	// 显式设置为UTC时区
	utcTime = utcTime.UTC()
	// 加载上海时区(北京时间)
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Println("加载时区错误:", err)
		return timeVal
	}
	// 将UTC时间转换为北京时间
	beijingTime := utcTime.In(loc)
	return beijingTime.Format(time.DateTime)
}

func BjToERUTime(timeVal string) string {
	// 解析北京时间字符串为time.Time对象
	loc, err := time.LoadLocation("Asia/Shanghai") // 使用Asia/Shanghai时区
	if err != nil {
		log.Println("Error loading location:", err)
		return timeVal
	}
	beijingTime, err := time.ParseInLocation("2006-01-02 15:04:05", timeVal, loc)
	if err != nil {
		log.Println("Error parsing time:", err)
		return timeVal
	}

	// 转换为UTC时间
	utcTime := beijingTime.In(time.UTC)
	return utcTime.Format(time.DateTime)
}

func UStoBjTime(timeVal string) string {
	// 加载洛杉矶时区
	laLoc, err := time.LoadLocation("America/Los_Angeles")
	if err != nil {
		fmt.Println("加载时区失败:", err)
		return timeVal
	}

	// 解析为洛杉矶时间
	laTime, err := time.ParseInLocation(time.DateTime, timeVal, laLoc)
	if err != nil {
		fmt.Println("解析时间失败:", err)
		return timeVal
	}

	// 加载上海时区
	shanghaiLoc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Println("加载时区失败:", err)
		return timeVal
	}
	// 将美国时间转换为北京时间
	beijingTime := laTime.In(shanghaiLoc) // 或 laTime.In(shanghaiLoc)
	return beijingTime.Format(time.DateTime)
}

func BjToUSTime(timeVal string) string {
	// 解析北京时间字符串为time.Time对象
	loc, err := time.LoadLocation("Asia/Shanghai") // 使用Asia/Shanghai时区
	if err != nil {
		log.Println("Error loading location:", err)
		return timeVal
	}
	beijingTime, err := time.ParseInLocation("2006-01-02 15:04:05", timeVal, loc)
	if err != nil {
		log.Println("Error parsing time:", err)
		return timeVal
	}

	// 加载洛杉矶时区
	laLoc, err := time.LoadLocation("America/Los_Angeles")
	if err != nil {
		fmt.Println("加载时区失败:", err)
		return timeVal
	}

	nyTime := beijingTime.In(laLoc)

	return nyTime.Format(time.DateTime)
}

func GeTimeZoneTag(timeZoneStr string) int {
	if timeZoneStr == constant.TIME_US {
		return 3
	} else if timeZoneStr == constant.TIME_ERU {
		return 2
	} else {
		return 1
	}
}

func IsTimeZone(timeZoneStr string) bool {
	if timeZoneStr == constant.TIME_US || timeZoneStr == constant.TIME_ERU || timeZoneStr == constant.TIME_BJ {
		return true
	}
	return false
}

func StringToFloat32(s string) (float32, error) {
	// 先转为 float64（strconv.ParseFloat 只支持 float64）
	f64, err := strconv.ParseFloat(s, 32)
	if err != nil {
		return 0, err
	}
	// 显式转换为 float32
	return float32(f64), nil
}
func StringToFloat64(s string) (float32, error) {
	// 先转为 float64（strconv.ParseFloat 只支持 float64）
	f64, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0, err
	}
	// 显式转换为 float32
	return float32(f64), nil
}
