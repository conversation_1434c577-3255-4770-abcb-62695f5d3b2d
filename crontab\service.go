package crontab

import (
	"910.com/plus2.git/contract"
	"910.com/plus2.git/plusQ"
	"iaa_data/cmd"
)

// 定时任务
type Service struct{}

func NewService() *Service {
	return &Service{}
}

func (s *Service) InitHandler(c contract.Crontab) error {
	c.AddFunc("0 1 */1 * * *", FacebookGrabDay, "FacebookGrabDay")
	c.AddFunc("0 5 */1 * * *", FacebookGrabHour, "FacebookGrabHour")

	c.AddFunc("0 1 */1 * * *", GoogleGrabDay, "GoogleGrabDay")

	c.AddFunc("0 1 */1 * * *", TitTokGrabDay, "TitTokGrabDay")
	c.Add<PERSON>unc("0 5 */1 * * *", TitTokGrabHour, "TitTokGrabHour")

	c.AddFunc("0 1 */1 * * *", ApplovinGrabDay, "ApplovinGrabDay")
	c.AddFunc("0 5 */1 * * *", <PERSON><PERSON><PERSON>inGrabHour, "ApplovinGrabHour")
	return nil
}

func FacebookGrabHour(args ...string) {
	plusQ.Logger().Info("facebook_grab_cron", "开始执行hour")

	handler := &cmd.FacebookGrabHour{}
	params := make(map[string]string)
	err := handler.Run(params)

	plusQ.Logger().Error("facebook_grab_cron", err)
}

func FacebookGrabDay(args ...string) {
	plusQ.Logger().Info("facebook_grab_cron", "开始执行day")

	handler := &cmd.FacebookGrab{}
	params := make(map[string]string)
	err := handler.Run(params)

	plusQ.Logger().Error("facebook_grab_cron", err)
}

func GoogleGrabDay(args ...string) {
	plusQ.Logger().Info("google_grab_cron", "开始执行day")

	handler := &cmd.GoogleGrab{}
	params := make(map[string]string)
	err := handler.Run(params)

	plusQ.Logger().Error("google_grab_cron", err)
}

func TitTokGrabDay(args ...string) {
	plusQ.Logger().Info("tiktok_grab_cron", "开始执行day")

	handler := &cmd.TiktokGrab{}
	params := make(map[string]string)
	err := handler.Run(params)

	plusQ.Logger().Error("tiktok_grab_cron", err)
}

func TitTokGrabHour(args ...string) {
	plusQ.Logger().Info("tiktok_grab_cron", "开始执行hour")

	handler := &cmd.TiktokGrabHour{}
	params := make(map[string]string)
	err := handler.Run(params)

	plusQ.Logger().Error("tiktok_grab_cron", err)
}

func ApplovinGrabDay(args ...string) {
	plusQ.Logger().Info("applovin_grab_cron", "开始执行day")

	handler := &cmd.ApplovinGrab{}
	params := make(map[string]string)
	err := handler.Run(params)

	plusQ.Logger().Error("applovin_grab_cron", err)
}

func ApplovinGrabHour(args ...string) {
	plusQ.Logger().Info("applovin_grab_cron", "开始执行hour")

	handler := &cmd.ApplovinGrabHour{}
	params := make(map[string]string)
	err := handler.Run(params)

	plusQ.Logger().Error("applovin_grab_cron", err)
}
