package game

import (
	"context"
	"database/sql"
	"fmt"
	"reflect"
	"time"

	"910.com/plus2.git/object/condition"
	"910.com/plus2.git/object/property"
	"910.com/plus2.git/orm"
	"github.com/pkg/errors"
)

const (
	Database = orm.Database("platform")
	Table    = orm.Table("games")
)

type FieldsList property.Properties

func Fields(field ...property.Property) FieldsList {
	return append(FieldsList{}, field...)
}

const (
	// Id id
	Id = property.Property("id")
	// CpGameId cp游戏id
	CpGameId = property.Property("cp_game_id")
	// AppShowName 游戏前端名
	AppShowName = property.Property("app_show_name")
	// SmGameName 游戏统计名（唯一，录入后不允许修改）
	SmGameName = property.Property("sm_game_name")
	// AddTime 添加配置时间
	AddTime = property.Property("add_time")
	// UpdateTime 修改时间
	UpdateTime = property.Property("update_time")
	// Operator 操作人
	Operator = property.Property("operator")
	// PackageName 游戏包名
	PackageName = property.Property("package_name")
	// Ext 扩展字段
	Ext = property.Property("ext")
	// State 状态：1，启用；2，禁用
	State = property.Property("state")
	// Subject 主体
	Subject = property.Property("subject")
)

var FieldsAll = Fields(Id, CpGameId, AppShowName, SmGameName, AddTime, UpdateTime, Operator, PackageName, Ext, State, Subject)
var NonePrimaryFields = Fields(CpGameId, AppShowName, SmGameName, AddTime, UpdateTime, Operator, PackageName, Ext, State, Subject)
var NoneAutoIncrementFields = Fields(CpGameId, AppShowName, SmGameName, AddTime, UpdateTime, Operator, PackageName, Ext, State, Subject)

const extraScan = false

// 定义一个自定义时间类型
type CustomTime time.Time

// 实现自定义时间类型的 MarshalJSON() 方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	formatted := t.Format(time.DateTime)
	return []byte(`"` + formatted + `"`), nil
}

// GameModel 游戏统计名配置
type GameModel struct {
	// Id id
	Id uint32 `orm:"id" json:"id"`

	// CpGameId cp游戏id
	CpGameId int `orm:"cp_game_id" json:"cp_game_id"`

	// AppShowName 游戏前端名
	AppShowName string `orm:"app_show_name" json:"app_show_name"`

	// SmGameName 游戏统计名（唯一，录入后不允许修改）
	SmGameName string `orm:"sm_game_name" json:"sm_game_name"`

	// AddTime 添加配置时间
	AddTime CustomTime `orm:"add_time" json:"add_time"`

	// UpdateTime 修改时间
	UpdateTime CustomTime `orm:"update_time" json:"update_time"`

	// Operator 操作人
	Operator string `orm:"operator" json:"operator"`

	// PackageName 游戏包名
	PackageName string `orm:"package_name" json:"package_name"`

	// Ext 扩展字段
	Ext *string `orm:"ext" json:"ext"`

	// State 状态：1，启用；2，禁用
	State uint32 `orm:"state" json:"state"`

	// Subject 主体
	Subject string `orm:"subject" json:"subject"`
}

type PagedResult struct {
	Records      []*GameModel `json:"list"`
	PageNum      int          `json:"page"`
	PageSize     int          `json:"page_size"`
	TotalPages   int          `json:"total_pages"`
	TotalRecords int          `json:"total"`
}

var fieldIndex = map[property.Property]int{
	Id:          1,
	CpGameId:    2,
	AppShowName: 3,
	SmGameName:  4,
	AddTime:     5,
	UpdateTime:  6,
	Operator:    7,
	PackageName: 8,
	Ext:         9,
	State:       10,
	Subject:     11,
}

func (m *GameModel) Insert() error {
	return m.InsertContext(defaultContext())
}

func (m *GameModel) InsertContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(NoneAutoIncrementFields...), m.CpGameId, m.AppShowName, m.SmGameName, m.AddTime, m.UpdateTime, m.Operator, m.PackageName, m.Ext, m.State, m.Subject)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	lastInsertId, err := result.LastInsertId()
	if err != nil {
		return errors.Wrapf(err, "error getting last insert id for %s", Table)
	}

	m.Id = uint32(lastInsertId)

	return nil

}

func (m *GameModel) InsertAllContext(ctx context.Context) error {

	result, err := orm.Insert(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.AppShowName, m.SmGameName, m.AddTime, m.UpdateTime, m.Operator, m.PackageName, m.Ext, m.State, m.Subject)
	if err != nil {
		return errors.Wrapf(err, "error inserting to %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}

	return nil

}

func (m *GameModel) Replace() error {
	return m.ReplaceContext(defaultContext())
}

func (m *GameModel) ReplaceContext(ctx context.Context) error {

	result, err := orm.Replace(ctx, Table, orm.ToProperties(FieldsAll...), m.Id, m.CpGameId, m.AppShowName, m.SmGameName, m.AddTime, m.UpdateTime, m.Operator, m.PackageName, m.Ext, m.State, m.Subject)
	if err != nil {
		return errors.Wrapf(err, "error replacing into %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while replacing record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *GameModel) Update() error {
	return m.UpdateContext(defaultContext())
}

func (m *GameModel) UpdateContext(ctx context.Context) error {

	result, err := orm.Update(ctx, Table, orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.AppShowName, m.SmGameName, m.AddTime, m.UpdateTime, m.Operator, m.PackageName, m.Ext, m.State, m.Subject), Id.Equal(m.Id))
	if err != nil {
		return errors.Wrapf(err, "error updating %s", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while updating record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *GameModel) InsertOrUpdate() error {
	return m.InsertOrUpdateContext(defaultContext())
}

func (m *GameModel) InsertOrUpdateContext(ctx context.Context) error {

	result, err := orm.InsertOrUpdate(ctx, Table, orm.ToProperties(FieldsAll...),
		orm.ToValues(m.Id, m.CpGameId, m.AppShowName, m.SmGameName, m.AddTime, m.UpdateTime, m.Operator, m.PackageName, m.Ext, m.State, m.Subject), orm.ToProperties(NonePrimaryFields...),
		orm.ToValues(m.CpGameId, m.AppShowName, m.SmGameName, m.AddTime, m.UpdateTime, m.Operator, m.PackageName, m.Ext, m.State, m.Subject))
	if err != nil {
		return errors.Wrapf(err, "error insert or update %s ", Table)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrapf(err, "error getting affected rows for %s while inserting record", Table)
	}

	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (m *GameModel) Save() error {
	return m.SaveContext(defaultContext())
}

func (m *GameModel) SaveContext(ctx context.Context) error {

	if m.primaryValueValid() {

		err := m.UpdateContext(ctx)
		if err != nil {
			if err == orm.ErrAffectedZero {
				return m.InsertAllContext(ctx)
			}
		}
		return nil

	} else {
		return m.InsertContext(ctx)
	}

}

func (m *GameModel) Delete() error {
	return m.DeleteContext(defaultContext())
}

func (m *GameModel) DeleteContext(ctx context.Context) error {

	if m.primaryValueValid() {

		result, err := orm.Delete(ctx, Table, Id.Equal(m.Id))
		if err != nil {
			return errors.Wrapf(err, "error deleting %s", Table)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return errors.Wrapf(err, "error getting affected rows for %s while deleting record", Table)
		}

		if rowsAffected == 0 {
			return orm.ErrAffectedZero
		}

		return nil

	}
	return orm.ErrInvalidPrimaryKey

}

func (m *GameModel) primaryValueValid() bool {
	return m.Id != 0
}

func (m *GameModel) primaryValueCheck() error {

	if !m.primaryValueValid() {
		return orm.ErrInvalidPrimaryKey
	}
	return nil

}

func Query(query string, params ...interface{}) ([]*GameModel, error) {
	return QueryContext(defaultContext(), query, params...)
}

func QueryContext(ctx context.Context, query string, params ...interface{}) ([]*GameModel, error) {

	modelList := make([]*GameModel, 0)
	if err := orm.FetchRaw(ctx, query, params, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &GameModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.AppShowName, &m.SmGameName, &m.AddTime, &m.UpdateTime, &m.Operator, &m.PackageName, &m.Ext, &m.State, &m.Subject)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	}); err != nil {
		return nil, err
	}
	return modelList, nil

}

func Execute(stmt string, params ...interface{}) (int64, error) {
	return ExecuteContext(defaultContext(), stmt, params...)
}

func ExecuteContext(ctx context.Context, stmt string, params ...interface{}) (int64, error) {

	result, err := orm.ExecuteRaw(ctx, stmt, params...)
	if err != nil {
		return -1, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return -1, err
	}

	lastInsertId, err := result.LastInsertId()
	if err == nil {
		return lastInsertId, nil
	}

	if rowsAffected == 0 {
		return rowsAffected, orm.ErrAffectedZero
	}
	return rowsAffected, nil

}

func Load(id uint32) (*GameModel, error) {
	return LoadContext(defaultContext(), id)
}

func LoadContext(ctx context.Context, id uint32) (*GameModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return GetByContext(ctx, con)

}

func LoadUnlocked(id uint32) (*GameModel, error) {
	return LoadUnlockedContext(defaultContext(), id)
}

func LoadUnlockedContext(ctx context.Context, id uint32) (*GameModel, error) {
	return GetByContext(ctx, Id.Equal(id))
}

func GetBy(con condition.Condition) (*GameModel, error) {
	return GetByContext(defaultContext(), con)
}

func GetByContext(ctx context.Context, con condition.Condition) (*GameModel, error) {

	m := &GameModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			return orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.AppShowName, &m.SmGameName, &m.AddTime, &m.UpdateTime, &m.Operator, &m.PackageName, &m.Ext, &m.State, &m.Subject)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func ListBy(con condition.Condition) ([]*GameModel, error) {
	return ListByContext(defaultContext(), con)
}

func ListByContext(ctx context.Context, con condition.Condition) ([]*GameModel, error) {

	modelList := make([]*GameModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(FieldsAll...), con, func(rows *sql.Rows) error {

		for rows.Next() {
			m := &GameModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.AppShowName, &m.SmGameName, &m.AddTime, &m.UpdateTime, &m.Operator, &m.PackageName, &m.Ext, &m.State, &m.Subject)
			if err != nil {
				return err
			}
			modelList = append(modelList, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func ListAll() ([]*GameModel, error) {
	return ListAllContext(defaultContext())
}

func ListAllContext(ctx context.Context) ([]*GameModel, error) {
	return ListByContext(ctx, condition.EmptyCondition)
}

func CountContext(ctx context.Context, con condition.Condition, fields ...property.Property) (int64, error) {
	return orm.Count(ctx, Table, con, fields...)
}

func Count(con condition.Condition) (int64, error) {
	return CountContext(defaultContext(), con)
}

func PagedListBy(con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(defaultContext(), con, pageNum, pageSize)
}

func PagedListByContext(ctx context.Context, con condition.Condition, pageNum int, pageSize int) (*PagedResult, error) {
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	result := &PagedResult{}
	err := orm.PagedFetch(ctx, Table, orm.ToProperties(FieldsAll...), con, pageNum, pageSize, func(pageNum, pageSize, totalPages, totalRecords int, rows *sql.Rows) error {

		result.PageNum = pageNum
		result.PageSize = pageSize
		result.TotalPages = totalPages
		result.TotalRecords = totalRecords

		for rows.Next() {
			m := &GameModel{}
			err := orm.Scan(rows, extraScan, &m.Id, &m.CpGameId, &m.AppShowName, &m.SmGameName, &m.AddTime, &m.UpdateTime, &m.Operator, &m.PackageName, &m.Ext, &m.State, &m.Subject)
			if err != nil {
				return err
			}
			result.Records = append(result.Records, m)
		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return result, nil

}

func PagedList(pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListContext(defaultContext(), pageNum, pageSize)
}

func PagedListContext(ctx context.Context, pageNum int, pageSize int) (*PagedResult, error) {
	return PagedListByContext(ctx, condition.EmptyCondition, pageNum, pageSize)
}

func Update(fieldOp property.ConditionalSetter) error {
	return UpdateContext(defaultContext(), fieldOp)
}

func UpdateContext(ctx context.Context, fieldOp property.ConditionalSetter) error {

	result, err := orm.UpdateField(ctx, Table, fieldOp)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	if rowsAffected == 0 {
		return orm.ErrAffectedZero
	}
	return nil

}

func (f FieldsList) Load(id uint32) (*GameModel, error) {
	return f.LoadContext(defaultContext(), id)
}

func (f FieldsList) LoadContext(ctx context.Context, id uint32) (*GameModel, error) {

	con := Id.Equal(id)
	if orm.InTransaction(ctx) {
		con = con.ForUpdate()
	}
	return f.GetByContext(ctx, con)

}

func (f FieldsList) GetBy(con condition.Condition) (*GameModel, error) {
	return f.GetByContext(defaultContext(), con)
}

func (f FieldsList) GetByContext(ctx context.Context, con condition.Condition) (*GameModel, error) {

	m := &GameModel{}

	if !con.IsForUpdate() {
		con = con.Limit(1)
	}

	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		if rows.Next() {
			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}
			return orm.Scan(rows, extraScan, scanFields...)
		}
		return orm.ErrNotFound

	})
	if err != nil {
		return nil, err
	}
	return m, nil

}

func (f FieldsList) ListBy(con condition.Condition) ([]*GameModel, error) {
	return f.ListByContext(defaultContext(), con)
}

func (f FieldsList) ListByContext(ctx context.Context, con condition.Condition) ([]*GameModel, error) {

	modelList := make([]*GameModel, 0)
	err := orm.Fetch(ctx, Table, orm.ToProperties(f...), con, func(rows *sql.Rows) error {

		for rows.Next() {

			m := &GameModel{}

			scanFields := make([]interface{}, 0)
			elem := reflect.ValueOf(m).Elem()
			for _, fieldName := range f {
				idx, found := fieldIndex[fieldName]
				if !found {
					return fmt.Errorf("field %s not found in struct", fieldName)
				}
				field := elem.Field(idx - 1)
				scanFields = append(scanFields, field.Addr().Interface())
			}

			err := orm.Scan(rows, extraScan, scanFields...)
			if err != nil {
				return err
			}

			modelList = append(modelList, m)

		}
		return nil

	})
	if err != nil {
		return nil, err
	}
	return modelList, nil

}

func (f FieldsList) ListAll() ([]*GameModel, error) {
	return f.ListAllContext(defaultContext())
}

func (f FieldsList) ListAllContext(ctx context.Context) ([]*GameModel, error) {
	return f.ListByContext(ctx, condition.EmptyCondition)
}

func (f FieldsList) Set(v ...interface{}) property.Setters {
	return orm.ToProperties(f...).Set(v...)
}

func Transaction(transaction func(context.Context) error) error {
	return orm.TransactionWithContext(defaultContext(), transaction)
}

func NoneTransaction(transaction func(context.Context) error) error {
	return orm.NoneTransactionWithContext(defaultContext(), transaction)
}

func defaultContext() context.Context {
	return orm.DatabaseContext(context.Background(), Database)
}

// InsertOrUpdateBatch 批量插入或更新
func InsertOrUpdateBatch(data []*GameModel, batchSize ...int) (int64, int64, error) {
	return InsertOrUpdateBatchContext(defaultContext(), data, batchSize...)
}

// InsertOrUpdateBatchContext 批量插入或更新
func InsertOrUpdateBatchContext(ctx context.Context, data []*GameModel, batchSize ...int) (int64, int64, error) {
	values := make([][]interface{}, 0)
	for _, d := range data {
		values = append(values, orm.ToValues(d.Id, d.CpGameId, d.AppShowName, d.SmGameName, d.AddTime, d.UpdateTime, d.Operator, d.PackageName, d.Ext, d.State, d.Subject))
	}
	lastInsertId, rowsAffected, err := orm.InsertOrUpdateBatch(ctx, Table, orm.ToProperties(FieldsAll...), values, orm.ToProperties(FieldsAll...), batchSize...)
	if err != nil {
		return lastInsertId, rowsAffected, err
	}
	return lastInsertId, rowsAffected, nil
}

// GetColumns 获取表头展示配置
func GetColumns() []map[string]interface{} {
	return []map[string]interface{}{
		//"sorter":    true, // 是否开启排序

		{
			"dataIndex": "id",
			"title":     "id",
		},
		{
			"dataIndex": "cp_game_id",
			"title":     "cp游戏id",
		},
		{
			"dataIndex": "app_show_name",
			"title":     "游戏前端名",
		},
		{
			"dataIndex": "sm_game_name",
			"title":     "游戏统计名（唯一，录入后不允许修改）",
		},
		{
			"dataIndex": "add_time",
			"title":     "添加配置时间",
		},
		{
			"dataIndex": "update_time",
			"title":     "修改时间",
		},
		{
			"dataIndex": "operator",
			"title":     "操作人",
		},
		{
			"dataIndex": "package_name",
			"title":     "游戏包名",
		},
		{
			"dataIndex": "ext",
			"title":     "扩展字段",
		},
		{
			"dataIndex": "state",
			"title":     "状态：1，启用；2，禁用",
		},
		{
			"dataIndex": "subject",
			"title":     "主体",
		}}
}

type GameModelTableFields struct {
	Id uint32 `title:"id" json:"id"`

	CpGameId int `title:"cp游戏id" json:"cp_game_id"`

	AppShowName string `title:"游戏前端名" json:"app_show_name"`

	SmGameName string `title:"游戏统计名（唯一，录入后不允许修改）" json:"sm_game_name"`

	AddTime CustomTime `title:"添加配置时间" json:"add_time"`

	UpdateTime CustomTime `title:"修改时间" json:"update_time"`

	Operator string `title:"操作人" json:"operator"`

	PackageName string `title:"游戏包名" json:"package_name"`

	Ext *string `title:"扩展字段" json:"ext"`

	State uint32 `title:"状态：1，启用；2，禁用" json:"state"`

	Subject string `title:"主体" json:"subject"`
}
